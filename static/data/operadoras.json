{"Sheet1": [{"idOperadora": 0, "operadoraNome": "NENHUMA"}, {"idOperadora": 2, "operadoraNome": "Medial"}, {"idOperadora": 3, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4, "operadoraNome": "Sulamérica"}, {"idOperadora": 5, "operadoraNome": "GNDI"}, {"idOperadora": 6, "operadoraNome": "Bradesco Se<PERSON>ros - TRANSF 3975"}, {"idOperadora": 14, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 17, "operadoraNome": "Green Line"}, {"idOperadora": 21, "operadoraNome": "Interodonto"}, {"idOperadora": 27, "operadoraNome": "OdontoPrev"}, {"idOperadora": 28, "operadoraNome": "Omint"}, {"idOperadora": 30, "operadoraNome": "Porto Seguro"}, {"idOperadora": 32, "operadoraNome": "Prodent"}, {"idOperadora": 33, "operadoraNome": "Qualicorp"}, {"idOperadora": 37, "operadoraNome": "São Cristóvão"}, {"idOperadora": 43, "operadoraNome": "Simeam"}, {"idOperadora": 46, "operadoraNome": "Você Clube"}, {"idOperadora": 49, "operadoraNome": "Blue Med Alvorecer"}, {"idOperadora": 50, "operadoraNome": "Trasmontano"}, {"idOperadora": 51, "operadoraNome": "BB Seguro"}, {"idOperadora": 52, "operadoraNome": "Assist Card"}, {"idOperadora": 53, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 55, "operadoraNome": "Itau - Auto"}, {"idOperadora": 56, "operadoraNome": "Tempo <PERSON>"}, {"idOperadora": 57, "operadoraNome": "Mapfre - Auto/RE"}, {"idOperadora": 58, "operadoraNome": "<PERSON><PERSON><PERSON> - Mu<PERSON>u para Sompo Seguros"}, {"idOperadora": 59, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 61, "operadoraNome": "<PERSON>bb Segu<PERSON>ra - Auto/RE"}, {"idOperadora": 62, "operadoraNome": "HDI - Auto/RE"}, {"idOperadora": 64, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 65, "operadoraNome": "<PERSON>"}, {"idOperadora": 66, "operadoraNome": "Vida Seguradora"}, {"idOperadora": 68, "operadoraNome": "Univida Plus"}, {"idOperadora": 70, "operadoraNome": "Bradesco - Auto/RE"}, {"idOperadora": 71, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 73, "operadoraNome": "UBF Seguros"}, {"idOperadora": 83, "operadoraNome": "Prevident"}, {"idOperadora": 84, "operadoraNome": "AACL"}, {"idOperadora": 85, "operadoraNome": "Santamalia"}, {"idOperadora": 86, "operadoraNome": "Generali - Auto/RE"}, {"idOperadora": 88, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 89, "operadoraNome": "<PERSON>imed <PERSON>sp"}, {"idOperadora": 91, "operadoraNome": "Care Plus"}, {"idOperadora": 92, "operadoraNome": "Lincx"}, {"idOperadora": 93, "operadoraNome": "Amil"}, {"idOperadora": 101, "operadoraNome": "Extrassist"}, {"idOperadora": 102, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 112, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON> "}, {"idOperadora": 113, "operadoraNome": "Pluriclub"}, {"idOperadora": 114, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 117, "operadoraNome": "Caixa Seguradora"}, {"idOperadora": 1347, "operadoraNome": "GS Garantia"}, {"idOperadora": 1348, "operadoraNome": "Uniodonto"}, {"idOperadora": 1349, "operadoraNome": "Univida Health"}, {"idOperadora": 1350, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1351, "operadoraNome": "Unihealth"}, {"idOperadora": 1352, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1353, "operadoraNome": "Biovida Saúde"}, {"idOperadora": 1356, "operadoraNome": "Ameplan"}, {"idOperadora": 1357, "operadoraNome": "Viacorp"}, {"idOperadora": 1359, "operadoraNome": "Santa Helena (Grupo Amil)"}, {"idOperadora": 1364, "operadoraNome": "Omega Saude"}, {"idOperadora": 1365, "operadoraNome": "Coopus"}, {"idOperadora": 1366, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1367, "operadoraNome": "<PERSON> (Grupo Amil)"}, {"idOperadora": 1369, "operadoraNome": "<PERSON>"}, {"idOperadora": 1370, "operadoraNome": "Allcare"}, {"idOperadora": 1371, "operadoraNome": "Independencia"}, {"idOperadora": 1385, "operadoraNome": "Metlife"}, {"idOperadora": 1387, "operadoraNome": "TRANF 114 / 3707 - <PERSON><PERSON><PERSON>"}, {"idOperadora": 1388, "operadoraNome": "AMR Oeste"}, {"idOperadora": 1389, "operadoraNome": "Med-Tour Saúde"}, {"idOperadora": 1391, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1392, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1393, "operadoraNome": "Prevquali - TESTANDO"}, {"idOperadora": 1395, "operadoraNome": "Unimed SJC"}, {"idOperadora": 1398, "operadoraNome": "Nunes e Grossi"}, {"idOperadora": 1401, "operadoraNome": "Grupo Contém"}, {"idOperadora": 1402, "operadoraNome": "Next Saude"}, {"idOperadora": 1403, "operadoraNome": "Ouro Brasil"}, {"idOperadora": 1404, "operadoraNome": "Affix"}, {"idOperadora": 1406, "operadoraNome": "Health For Pet"}, {"idOperadora": 1407, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1409, "operadoraNome": "Unimed SA"}, {"idOperadora": 1410, "operadoraNome": "Dentalpar"}, {"idOperadora": 1412, "operadoraNome": "Soesp <PERSON>"}, {"idOperadora": 1414, "operadoraNome": "UP Odonto"}, {"idOperadora": 1415, "operadoraNome": "Saude São Lucas"}, {"idOperadora": 1416, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1419, "operadoraNome": "Hebrom"}, {"idOperadora": 1421, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1422, "operadoraNome": "Ideal Vida e Saude"}, {"idOperadora": 1423, "operadoraNome": "Admix"}, {"idOperadora": 1428, "operadoraNome": "Health Santaris"}, {"idOperadora": 1429, "operadoraNome": "Dentalplus"}, {"idOperadora": 1430, "operadoraNome": "Medical Health"}, {"idOperadora": 1432, "operadoraNome": "Allianz - Auto/RE"}, {"idOperadora": 1433, "operadoraNome": "Tokio Marine"}, {"idOperadora": 1434, "operadoraNome": "Alfa - Auto/RE"}, {"idOperadora": 1435, "operadoraNome": "Inpao Dental"}, {"idOperadora": 1436, "operadoraNome": "Bio Saúde"}, {"idOperadora": 1437, "operadoraNome": "Mogidonto"}, {"idOperadora": 1438, "operadoraNome": "Adplan"}, {"idOperadora": 1440, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1441, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1443, "operadoraNome": "Clarian Aeromédico"}, {"idOperadora": 1444, "operadoraNome": "Unihosp Saúde"}, {"idOperadora": 1445, "operadoraNome": "Unimed Nacional (CNU)"}, {"idOperadora": 1447, "operadoraNome": "Suhai - Auto/RE"}, {"idOperadora": 1448, "operadoraNome": "Santa Casa de São Jose dos Campos"}, {"idOperadora": 1450, "operadoraNome": "IBBCA"}, {"idOperadora": 1451, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1453, "operadoraNome": "RB Saude"}, {"idOperadora": 1454, "operadoraNome": "Garantia <PERSON>"}, {"idOperadora": 1455, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1457, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1458, "operadoraNome": "Icatu Vida e Previdencia"}, {"idOperadora": 1460, "operadoraNome": "Oral Company"}, {"idOperadora": 1461, "operadoraNome": "VR Benefícios"}, {"idOperadora": 1463, "operadoraNome": "Ha<PERSON><PERSON><PERSON>"}, {"idOperadora": 1464, "operadoraNome": "Free Life"}, {"idOperadora": 1465, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1466, "operadoraNome": "Unimed Fortaleza"}, {"idOperadora": 1467, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1468, "operadoraNome": "Biobox"}, {"idOperadora": 1470, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1471, "operadoraNome": "Sompo Seguros"}, {"idOperadora": 1472, "operadoraNome": "Semmler / VENDA SUSPENSA 30/08/2017"}, {"idOperadora": 1473, "operadoraNome": "<PERSON><PERSON> / VENDA SUSPENSA 30/08/2017"}, {"idOperadora": 1474, "operadoraNome": "Dix <PERSON>ler / VENDA SUSPENSA 30/08/2017"}, {"idOperadora": 1475, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1477, "operadoraNome": "Unimed Campinas"}, {"idOperadora": 1478, "operadoraNome": "Unimed <PERSON>"}, {"idOperadora": 1479, "operadoraNome": "April"}, {"idOperadora": 1480, "operadoraNome": "<PERSON><PERSON><PERSON> - Auto/RE"}, {"idOperadora": 1483, "operadoraNome": "Plamed"}, {"idOperadora": 1485, "operadoraNome": "Bradesco Dental"}, {"idOperadora": 1486, "operadoraNome": "Green Card Odonto"}, {"idOperadora": 1487, "operadoraNome": "São Miguel Saúde"}, {"idOperadora": 1489, "operadoraNome": "Allure Odonto"}, {"idOperadora": 1490, "operadoraNome": "Assobio"}, {"idOperadora": 1491, "operadoraNome": "Extramed"}, {"idOperadora": 1492, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 1493, "operadoraNome": "Unimed <PERSON>"}, {"idOperadora": 1494, "operadoraNome": "Mon<PERSON>al Aegon"}, {"idOperadora": 1495, "operadoraNome": "Porto Seguro Conecta"}, {"idOperadora": 1496, "operadoraNome": "Golden Cross"}, {"idOperadora": 1497, "operadoraNome": "Liberty Seguros"}, {"idOperadora": 1498, "operadoraNome": "RSA"}, {"idOperadora": 1499, "operadoraNome": "American Life"}, {"idOperadora": 1500, "operadoraNome": "Petplan"}, {"idOperadora": 1501, "operadoraNome": "Saúdesim"}, {"idOperadora": 1502, "operadoraNome": "São Francisco Vida"}, {"idOperadora": 1503, "operadoraNome": "Unimed Belo Horizonte"}, {"idOperadora": 1504, "operadoraNome": "QBE Brasil Seguros S/A"}, {"idOperadora": 1505, "operadoraNome": "Unimed Norte Nordeste"}, {"idOperadora": 1506, "operadoraNome": "Clinipam"}, {"idOperadora": 1507, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1508, "operadoraNome": "Odontogroup"}, {"idOperadora": 1509, "operadoraNome": "Unimed Curitiba"}, {"idOperadora": 1510, "operadoraNome": "Samedh"}, {"idOperadora": 1511, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1512, "operadoraNome": "Promed"}, {"idOperadora": 1513, "operadoraNome": "Vivamed"}, {"idOperadora": 1514, "operadoraNome": "Unimed Planalto"}, {"idOperadora": 1515, "operadoraNome": "Universo"}, {"idOperadora": 1516, "operadoraNome": "Quallity Pró <PERSON>"}, {"idOperadora": 1517, "operadoraNome": "Premium Saude"}, {"idOperadora": 1518, "operadoraNome": "Prime Beneficios"}, {"idOperadora": 1519, "operadoraNome": "<PERSON>e"}, {"idOperadora": 1520, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1521, "operadoraNome": "Unimed Recife"}, {"idOperadora": 1522, "operadoraNome": "Medsênior"}, {"idOperadora": 1523, "operadoraNome": "São Bernardo"}, {"idOperadora": 1524, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1525, "operadoraNome": "Unimed Vitoria"}, {"idOperadora": 1526, "operadoraNome": "Benevix"}, {"idOperadora": 1527, "operadoraNome": "Evercross"}, {"idOperadora": 1528, "operadoraNome": "Unimed <PERSON>oz"}, {"idOperadora": 1529, "operadoraNome": "Unimed Porto Alegre"}, {"idOperadora": 1530, "operadoraNome": "Grupo Afinidade"}, {"idOperadora": 1531, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1532, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1533, "operadoraNome": "Promédica"}, {"idOperadora": 1534, "operadoraNome": "Santa Tereza"}, {"idOperadora": 1535, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1536, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1537, "operadoraNome": "Centro Clinico Gaucho (Notre Dame Intermédica)"}, {"idOperadora": 1538, "operadoraNome": "Doctor <PERSON><PERSON>"}, {"idOperadora": 1539, "operadoraNome": "Salutar"}, {"idOperadora": 1540, "operadoraNome": "SF São Francisco Saude"}, {"idOperadora": 1541, "operadoraNome": "Parana Clinicas"}, {"idOperadora": 1542, "operadoraNome": "Unimed Federação do Rio Grande do Norte"}, {"idOperadora": 1543, "operadoraNome": "Best Life"}, {"idOperadora": 1545, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1546, "operadoraNome": "Unimed Rio"}, {"idOperadora": 1547, "operadoraNome": "Unimed Les<PERSON>inen<PERSON>"}, {"idOperadora": 1548, "operadoraNome": "Divicom / ABC Adm"}, {"idOperadora": 1549, "operadoraNome": "Sompo Seguros - Auto/RE"}, {"idOperadora": 1550, "operadoraNome": "Clube Care"}, {"idOperadora": 1552, "operadoraNome": "CACSS"}, {"idOperadora": 1553, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 1554, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1555, "operadoraNome": "Easy Saude"}, {"idOperadora": 1556, "operadoraNome": "Unimed Norte Capixaba"}, {"idOperadora": 1557, "operadoraNome": "Cemeru"}, {"idOperadora": 1558, "operadoraNome": "Health Club"}, {"idOperadora": 1559, "operadoraNome": "Amigoo Pet"}, {"idOperadora": 1560, "operadoraNome": "Qualivida"}, {"idOperadora": 1561, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1562, "operadoraNome": "Unimed Nova Friburgo"}, {"idOperadora": 1563, "operadoraNome": "Samoc"}, {"idOperadora": 1564, "operadoraNome": "Hapvida <PERSON>"}, {"idOperadora": 1565, "operadoraNome": "Odonto System"}, {"idOperadora": 1566, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 1567, "operadoraNome": "Unimed <PERSON>"}, {"idOperadora": 1568, "operadoraNome": "Indiana Seguros - Auto/RE"}, {"idOperadora": 1569, "operadoraNome": "Zurich Seguros - Auto/RE"}, {"idOperadora": 1570, "operadoraNome": "Interclinicas"}, {"idOperadora": 1571, "operadoraNome": "Uniconsult (Grupo Qualicorp)"}, {"idOperadora": 1572, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1573, "operadoraNome": "Santa Casa Saude Bragança"}, {"idOperadora": 1574, "operadoraNome": "Green Life"}, {"idOperadora": 1575, "operadoraNome": "Clube de Saúde (Qualicorp)"}, {"idOperadora": 1576, "operadoraNome": "FUMDAP - ANADEM"}, {"idOperadora": 1577, "operadoraNome": "Espaço Clinico"}, {"idOperadora": 1578, "operadoraNome": "APSSERJ"}, {"idOperadora": 1579, "operadoraNome": "Biolife"}, {"idOperadora": 1580, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 1582, "operadoraNome": "Prevent Sênior"}, {"idOperadora": 1583, "operadoraNome": "Capemisa"}, {"idOperadora": 1584, "operadoraNome": "Argo"}, {"idOperadora": 1585, "operadoraNome": "HBC Saude"}, {"idOperadora": 1586, "operadoraNome": "Abec"}, {"idOperadora": 1587, "operadoraNome": "<PERSON>"}, {"idOperadora": 1588, "operadoraNome": "Simples"}, {"idOperadora": 1589, "operadoraNome": "São Francisco Odonto"}, {"idOperadora": 1590, "operadoraNome": "Unimed São Carlos"}, {"idOperadora": 1591, "operadoraNome": "Unimed Fernandópolis"}, {"idOperadora": 1592, "operadoraNome": "Unimed São Roque"}, {"idOperadora": 1593, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 1594, "operadoraNome": "Unimed ABC"}, {"idOperadora": 1595, "operadoraNome": "Grupo Mast"}, {"idOperadora": 1596, "operadoraNome": "Sobam (Grupo Amil)"}, {"idOperadora": 1597, "operadoraNome": "Grupo São José"}, {"idOperadora": 1598, "operadoraNome": "ABCEL - Assistencia Funeraria"}, {"idOperadora": 1599, "operadoraNome": "Ascibras"}, {"idOperadora": 1601, "operadoraNome": "ARM Odontologia"}, {"idOperadora": 1602, "operadoraNome": "CB <PERSON>ude"}, {"idOperadora": 1604, "operadoraNome": "Life Class"}, {"idOperadora": 1605, "operadoraNome": "Unimed Natal"}, {"idOperadora": 1606, "operadoraNome": "One Health"}, {"idOperadora": 1607, "operadoraNome": "Sorria+ Odonto"}, {"idOperadora": 1608, "operadoraNome": "Centro-Oeste"}, {"idOperadora": 1609, "operadoraNome": "Plano Santa Saúde"}, {"idOperadora": 1610, "operadoraNome": "São Francisco Saúde"}, {"idOperadora": 1611, "operadoraNome": "PHS <PERSON>"}, {"idOperadora": 1612, "operadoraNome": "TT Administradora"}, {"idOperadora": 1613, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 2614, "operadoraNome": "<PERSON>"}, {"idOperadora": 2616, "operadoraNome": "Classe Administradora"}, {"idOperadora": 3616, "operadoraNome": "<PERSON>us"}, {"idOperadora": 3617, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3618, "operadoraNome": "Sempre <PERSON>"}, {"idOperadora": 3619, "operadoraNome": "Ortoclin"}, {"idOperadora": 3620, "operadoraNome": "Amex <PERSON>"}, {"idOperadora": 3621, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3622, "operadoraNome": "Cart<PERSON>"}, {"idOperadora": 3624, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3625, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3626, "operadoraNome": "Unimed São José do Rio Preto"}, {"idOperadora": 3627, "operadoraNome": "Long Life"}, {"idOperadora": 3628, "operadoraNome": "Unimed Volta Redonda"}, {"idOperadora": 3630, "operadoraNome": "Medical Brasil Saude"}, {"idOperadora": 3631, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3632, "operadoraNome": "Plano de Saude Santa Casa"}, {"idOperadora": 3633, "operadoraNome": "Previsul Seguradora"}, {"idOperadora": 3634, "operadoraNome": "Plural"}, {"idOperadora": 3635, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3636, "operadoraNome": "Good Life Saude"}, {"idOperadora": 3637, "operadoraNome": "Unimed Imperatriz/Maranhão do Sul"}, {"idOperadora": 3638, "operadoraNome": "Affiance"}, {"idOperadora": 3639, "operadoraNome": "Cartão Saúde Familiar"}, {"idOperadora": 3640, "operadoraNome": "Unimed Vale do Aço"}, {"idOperadora": 3641, "operadoraNome": "União Médica"}, {"idOperadora": 3642, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3644, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3645, "operadoraNome": "Zurich Seguros"}, {"idOperadora": 3646, "operadoraNome": "Qualywork - Serviços Médicos"}, {"idOperadora": 3647, "operadoraNome": "Unimed Federação Santa Catarina"}, {"idOperadora": 3648, "operadoraNome": "Unimed Federação do Rio Grande do Sul"}, {"idOperadora": 3649, "operadoraNome": "HDI Seguros"}, {"idOperadora": 3650, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3651, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3652, "operadoraNome": "Platinum"}, {"idOperadora": 3653, "operadoraNome": "APS Saúde"}, {"idOperadora": 3654, "operadoraNome": "Lancers"}, {"idOperadora": 3655, "operadoraNome": "Ideal Saude"}, {"idOperadora": 3656, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 3658, "operadoraNome": "Unihosp <PERSON>"}, {"idOperadora": 3659, "operadoraNome": "Salute"}, {"idOperadora": 3660, "operadoraNome": "Berkley Brasil Seguros - Auto/RE"}, {"idOperadora": 3661, "operadoraNome": "Unimed Nacional"}, {"idOperadora": 3662, "operadoraNome": "Canopus"}, {"idOperadora": 3663, "operadoraNome": "Sodexo"}, {"idOperadora": 3664, "operadoraNome": "Dental Uni"}, {"idOperadora": 3665, "operadoraNome": "Planmed"}, {"idOperadora": 3666, "operadoraNome": "Aliança (Grupo Qualicorp)"}, {"idOperadora": 3667, "operadoraNome": "Good Life Odonto"}, {"idOperadora": 3668, "operadoraNome": "Dental Master"}, {"idOperadora": 3669, "operadoraNome": "Oral Santa Helena"}, {"idOperadora": 3670, "operadoraNome": "Viva Mais"}, {"idOperadora": 3672, "operadoraNome": "Generali"}, {"idOperadora": 3673, "operadoraNome": "A4 Quatro"}, {"idOperadora": 3674, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3675, "operadoraNome": "Itura<PERSON>"}, {"idOperadora": 3676, "operadoraNome": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>"}, {"idOperadora": 3677, "operadoraNome": "Berkley Brasil Seguros"}, {"idOperadora": 3678, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3679, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3680, "operadoraNome": "Real"}, {"idOperadora": 3681, "operadoraNome": "Odontopam"}, {"idOperadora": 3682, "operadoraNome": "Slam"}, {"idOperadora": 3683, "operadoraNome": "Cemil"}, {"idOperadora": 3684, "operadoraNome": "Unimed Jundiaí PF"}, {"idOperadora": 3685, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3686, "operadoraNome": "ABPC"}, {"idOperadora": 3687, "operadoraNome": "Lojacorr"}, {"idOperadora": 3688, "operadoraNome": "Unimed Taubate"}, {"idOperadora": 3689, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3690, "operadoraNome": "HS Consórcios"}, {"idOperadora": 3691, "operadoraNome": "AEPESP"}, {"idOperadora": 3692, "operadoraNome": "AFSCOB"}, {"idOperadora": 3693, "operadoraNome": "AMEBRAS"}, {"idOperadora": 3694, "operadoraNome": "ASEBRAS"}, {"idOperadora": 3695, "operadoraNome": "Solutions"}, {"idOperadora": 3696, "operadoraNome": "S1 Saude"}, {"idOperadora": 3697, "operadoraNome": "Elo (Grupo Qualicorp)"}, {"idOperadora": 3698, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3699, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3700, "operadoraNome": "Alfa - Seguros"}, {"idOperadora": 3701, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3702, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3703, "operadoraNome": "Prudential Seguros"}, {"idOperadora": 3704, "operadoraNome": "Gestão"}, {"idOperadora": 3707, "operadoraNome": "Unimed <PERSON>"}, {"idOperadora": 3708, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3709, "operadoraNome": "Excelsior <PERSON>"}, {"idOperadora": 3710, "operadoraNome": "Mondial Assistence"}, {"idOperadora": 3711, "operadoraNome": "Union Life"}, {"idOperadora": 3712, "operadoraNome": "Total Medcare"}, {"idOperadora": 3713, "operadoraNome": "Unimed Norte de Minas"}, {"idOperadora": 3714, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3715, "operadoraNome": "Intervida"}, {"idOperadora": 3716, "operadoraNome": "Unimed Santa Catarina"}, {"idOperadora": 3717, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3719, "operadoraNome": "New Leader <PERSON><PERSON>"}, {"idOperadora": 3721, "operadoraNome": "Unimed Noroeste Capixaba"}, {"idOperadora": 3722, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3723, "operadoraNome": "Unimed Salto/Itu"}, {"idOperadora": 3724, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 3725, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3726, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3727, "operadoraNome": "Lifeday"}, {"idOperadora": 3729, "operadoraNome": "Safelife"}, {"idOperadora": 3730, "operadoraNome": "Premium Care"}, {"idOperadora": 3731, "operadoraNome": "Unimed Costa do Sol"}, {"idOperadora": 3732, "operadoraNome": "Primavida Dental"}, {"idOperadora": 3733, "operadoraNome": "AAPEMIG"}, {"idOperadora": 3734, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3735, "operadoraNome": "BGN Seguros"}, {"idOperadora": 3736, "operadoraNome": "Banco <PERSON>coval"}, {"idOperadora": 3737, "operadoraNome": "Banco Banrisul"}, {"idOperadora": 3738, "operadoraNome": "Banco Itaú BMG"}, {"idOperadora": 3739, "operadoraNome": "Bancopan / Panamericano"}, {"idOperadora": 3740, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3741, "operadoraNome": "Banco BMG"}, {"idOperadora": 3743, "operadoraNome": "Sistema G.T.A"}, {"idOperadora": 3744, "operadoraNome": "Ticket <PERSON>"}, {"idOperadora": 3745, "operadoraNome": "Assist Med Viagem"}, {"idOperadora": 3747, "operadoraNome": "RN Saúde"}, {"idOperadora": 3748, "operadoraNome": "Unimed Grande Florianópolis"}, {"idOperadora": 3749, "operadoraNome": "Unimed Uberlândia"}, {"idOperadora": 3750, "operadoraNome": "Unimed Governador <PERSON>"}, {"idOperadora": 3751, "operadoraNome": "Up Life Seguros"}, {"idOperadora": 3752, "operadoraNome": "Santa Casa de Mauá"}, {"idOperadora": 3753, "operadoraNome": "Valem"}, {"idOperadora": 3754, "operadoraNome": "Travel Ace Assistance"}, {"idOperadora": 3755, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3756, "operadoraNome": "Castro Projetos Especiais"}, {"idOperadora": 3757, "operadoraNome": "Multiclínica Planos de Saúde"}, {"idOperadora": 3758, "operadoraNome": "CAECS"}, {"idOperadora": 3759, "operadoraNome": "Ameron de Saúde Administradora"}, {"idOperadora": 3760, "operadoraNome": "<PERSON>"}, {"idOperadora": 3761, "operadoraNome": "Addere"}, {"idOperadora": 3762, "operadoraNome": "Sepaco Saúde"}, {"idOperadora": 3763, "operadoraNome": "LIV Saúde"}, {"idOperadora": 3764, "operadoraNome": "Cruz Azul Saúde"}, {"idOperadora": 3765, "operadoraNome": "Plano de Saúde Hospitalar - Londrina"}, {"idOperadora": 3766, "operadoraNome": "Plano de Saúde <PERSON>"}, {"idOperadora": 3767, "operadoraNome": "Mount Hermon"}, {"idOperadora": 3768, "operadoraNome": "G2C Administradora"}, {"idOperadora": 3769, "operadoraNome": "Newcare"}, {"idOperadora": 3770, "operadoraNome": "<PERSON> Saúde"}, {"idOperadora": 3771, "operadoraNome": "Union Adm"}, {"idOperadora": 3772, "operadoraNome": "ABESP"}, {"idOperadora": 3773, "operadoraNome": "<PERSON>"}, {"idOperadora": 3774, "operadoraNome": "Bit Life Beneficios"}, {"idOperadora": 3775, "operadoraNome": "Assiste"}, {"idOperadora": 3776, "operadoraNome": "Plano de Saúde Santa Casa Passos - MG"}, {"idOperadora": 3777, "operadoraNome": "Health Med"}, {"idOperadora": 3778, "operadoraNome": "GH Saúde"}, {"idOperadora": 3779, "operadoraNome": "Odontomais"}, {"idOperadora": 3780, "operadoraNome": "Consórc<PERSON>"}, {"idOperadora": 3781, "operadoraNome": "BR Consórcios"}, {"idOperadora": 3782, "operadoraNome": "Mediatorie"}, {"idOperadora": 3783, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3784, "operadoraNome": "Unimed Inconfidentes"}, {"idOperadora": 3785, "operadoraNome": "ADM Administradora"}, {"idOperadora": 3786, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 3787, "operadoraNome": "Odonto S.A"}, {"idOperadora": 3788, "operadoraNome": "Vixmed"}, {"idOperadora": 3789, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3790, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3791, "operadoraNome": "SBC Saúde"}, {"idOperadora": 3792, "operadoraNome": "Inovare"}, {"idOperadora": 3793, "operadoraNome": "Qualidonto"}, {"idOperadora": 3794, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3795, "operadoraNome": "Planidente"}, {"idOperadora": 3796, "operadoraNome": "Osan <PERSON>o de Assistência Funeral"}, {"idOperadora": 3797, "operadoraNome": "Hapville"}, {"idOperadora": 3799, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3800, "operadoraNome": "Nordeste Saúde Empresarial"}, {"idOperadora": 3801, "operadoraNome": "Santa Rita Saúde"}, {"idOperadora": 3802, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3803, "operadoraNome": "Delfhiodonto"}, {"idOperadora": 3804, "operadoraNome": "Grupo MBM"}, {"idOperadora": 3805, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3806, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3807, "operadoraNome": "SINDFESP"}, {"idOperadora": 3808, "operadoraNome": "CBT Club Mais"}, {"idOperadora": 3809, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3810, "operadoraNome": "Unimed Vertente de Caparaó"}, {"idOperadora": 3811, "operadoraNome": "Alter"}, {"idOperadora": 3812, "operadoraNome": "Amazônia Saúde"}, {"idOperadora": 3813, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3814, "operadoraNome": "Drogaria São Paulo"}, {"idOperadora": 3815, "operadoraNome": "Tem Adm Cartões"}, {"idOperadora": 3818, "operadoraNome": "AASPN"}, {"idOperadora": 3819, "operadoraNome": "AASPN"}, {"idOperadora": 3820, "operadoraNome": "Unimed Sudoeste"}, {"idOperadora": 3821, "operadoraNome": "Promed Brasil"}, {"idOperadora": 3822, "operadoraNome": "Sempre Seguro Prime"}, {"idOperadora": 3823, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3824, "operadoraNome": "América Planos de Saúde"}, {"idOperadora": 3825, "operadoraNome": "TEM Saúde"}, {"idOperadora": 3826, "operadoraNome": "Unimed Porto Velho"}, {"idOperadora": 3827, "operadoraNome": "Plamesc"}, {"idOperadora": 3828, "operadoraNome": "Hubcare"}, {"idOperadora": 3829, "operadoraNome": "Ctesk"}, {"idOperadora": 3830, "operadoraNome": "AB Plus"}, {"idOperadora": 3831, "operadoraNome": "Vip Care"}, {"idOperadora": 3832, "operadoraNome": "Ideal Odonto"}, {"idOperadora": 3834, "operadoraNome": "Sul America - PMBA"}, {"idOperadora": 3835, "operadoraNome": "Seguros D&O"}, {"idOperadora": 3836, "operadoraNome": "YALO"}, {"idOperadora": 3837, "operadoraNome": "Humana Saúde"}, {"idOperadora": 3839, "operadoraNome": "Medclass"}, {"idOperadora": 3840, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3841, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3842, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3843, "operadoraNome": "União Adm"}, {"idOperadora": 3844, "operadoraNome": "Nacional Saúde"}, {"idOperadora": 3845, "operadoraNome": "A+ Access Saúde"}, {"idOperadora": 3846, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3847, "operadoraNome": "Unimed Sul Capixaba"}, {"idOperadora": 3848, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3849, "operadoraNome": "Med Life Brasília"}, {"idOperadora": 3850, "operadoraNome": "Mais Saúde DF"}, {"idOperadora": 3851, "operadoraNome": "Santa Casa Saúde de Campo Grande"}, {"idOperadora": 3852, "operadoraNome": "PASI Seguros"}, {"idOperadora": 3853, "operadoraNome": "Santa Filomena <PERSON>"}, {"idOperadora": 3854, "operadoraNome": "UP Health"}, {"idOperadora": 3855, "operadoraNome": "Odontobase"}, {"idOperadora": 3856, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 3857, "operadoraNome": "Única Saúde"}, {"idOperadora": 3858, "operadoraNome": "QV Benefícios"}, {"idOperadora": 3859, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3860, "operadoraNome": "<PERSON>med"}, {"idOperadora": 3861, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3862, "operadoraNome": "YOU Saúde"}, {"idOperadora": 3863, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3864, "operadoraNome": "Unimed Norte Fluminense"}, {"idOperadora": 3865, "operadoraNome": "Santa Casa Saúde Ribeirão Preto"}, {"idOperadora": 3866, "operadoraNome": "Soluti Certificação Digital"}, {"idOperadora": 3867, "operadoraNome": "Austa Clínicas"}, {"idOperadora": 3868, "operadoraNome": "SS Sorriso"}, {"idOperadora": 3869, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 3870, "operadoraNome": "Master Health"}, {"idOperadora": 3871, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3872, "operadoraNome": "Quality"}, {"idOperadora": 3873, "operadoraNome": "Porto Dias Saúde"}, {"idOperadora": 3874, "operadoraNome": "Saúde Vida Grupo Santa Mônica"}, {"idOperadora": 3875, "operadoraNome": "Vidaplan Saúde"}, {"idOperadora": 3876, "operadoraNome": "MV2C Administradora"}, {"idOperadora": 3877, "operadoraNome": "Elos"}, {"idOperadora": 3878, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3880, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3881, "operadoraNome": "Unimed Sorocaba"}, {"idOperadora": 3882, "operadoraNome": "W<PERSON>"}, {"idOperadora": 3883, "operadoraNome": "Unimed Os Bandeirantes"}, {"idOperadora": 3884, "operadoraNome": "Unimed Santa Bárbara Doeste e Americana"}, {"idOperadora": 3885, "operadoraNome": "Best Sênior / Best Saúde"}, {"idOperadora": 3886, "operadoraNome": "Plano de Saúde Santa Casa de Lorena"}, {"idOperadora": 3887, "operadoraNome": "Medical"}, {"idOperadora": 3888, "operadoraNome": "Vida Class"}, {"idOperadora": 3889, "operadoraNome": "TECGROUP"}, {"idOperadora": 3890, "operadoraNome": "Uniben Saúde"}, {"idOperadora": 3891, "operadoraNome": "Unimed Oeste do Pará"}, {"idOperadora": 3892, "operadoraNome": "Unimed Norte Capixaba ACS"}, {"idOperadora": 3893, "operadoraNome": "Po<PERSON>lin"}, {"idOperadora": 3894, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3895, "operadoraNome": "Fenix Medical - Administradora"}, {"idOperadora": 3896, "operadoraNome": "Sistemas Planos de Saúde"}, {"idOperadora": 3897, "operadoraNome": "Casa do Saúde"}, {"idOperadora": 3898, "operadoraNome": "ABRACIM"}, {"idOperadora": 3899, "operadoraNome": "Climepe Total"}, {"idOperadora": 3900, "operadoraNome": "Med Gold"}, {"idOperadora": 3901, "operadoraNome": "<PERSON> <PERSON>"}, {"idOperadora": 3902, "operadoraNome": "KIPP Saúde"}, {"idOperadora": 3903, "operadoraNome": "Medsul Saúde"}, {"idOperadora": 3904, "operadoraNome": "<PERSON> Médica"}, {"idOperadora": 3905, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3906, "operadoraNome": "Royal Plus"}, {"idOperadora": 3907, "operadoraNome": "Universal"}, {"idOperadora": 3908, "operadoraNome": "GNDI Minas"}, {"idOperadora": 3909, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3910, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3912, "operadoraNome": "ACS"}, {"idOperadora": 3913, "operadoraNome": "Associação Castelo Forte"}, {"idOperadora": 3914, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3915, "operadoraNome": "Club Card"}, {"idOperadora": 3916, "operadoraNome": "Porto Pet"}, {"idOperadora": 3917, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3918, "operadoraNome": "<PERSON><PERSON> Benefício<PERSON>"}, {"idOperadora": 3920, "operadoraNome": "RCA Administradora"}, {"idOperadora": 3921, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3922, "operadoraNome": "Unimed Nordeste Paulista"}, {"idOperadora": 3923, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3924, "operadoraNome": "Plano de Saúde Fátima (Passou a Ser Humana Saúde Sul)"}, {"idOperadora": 3925, "operadoraNome": "Easyplan"}, {"idOperadora": 3926, "operadoraNome": "Pré-Cadastro de Produção"}, {"idOperadora": 3927, "operadoraNome": "Blue Med Saúde"}, {"idOperadora": 3928, "operadoraNome": "Plansul Saúde"}, {"idOperadora": 3929, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>efí<PERSON>"}, {"idOperadora": 3930, "operadoraNome": "Unimed Campina Grande"}, {"idOperadora": 3931, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3932, "operadoraNome": "Unnimax"}, {"idOperadora": 3933, "operadoraNome": "SEAAC"}, {"idOperadora": 3934, "operadoraNome": "Dental Center"}, {"idOperadora": 3935, "operadoraNome": "Unimed Centro Oeste Paulista"}, {"idOperadora": 3936, "operadoraNome": "Med Health"}, {"idOperadora": 3937, "operadoraNome": "Se<PERSON>re V<PERSON> (Humana Saúde Sul)"}, {"idOperadora": 3938, "operadoraNome": "Medplan"}, {"idOperadora": 3939, "operadoraNome": "Cuidar.Me"}, {"idOperadora": 3940, "operadoraNome": "Smartcare"}, {"idOperadora": 3941, "operadoraNome": "Saúde São José"}, {"idOperadora": 3942, "operadoraNome": "Oeste <PERSON>"}, {"idOperadora": 3943, "operadoraNome": "Unimed <PERSON>"}, {"idOperadora": 3944, "operadoraNome": "Sulmed"}, {"idOperadora": 3945, "operadoraNome": "Baruk"}, {"idOperadora": 3946, "operadoraNome": "Positiva"}, {"idOperadora": 3947, "operadoraNome": "<PERSON>imed Vitória - Dental"}, {"idOperadora": 3949, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3950, "operadoraNome": "Odonto Empresas"}, {"idOperadora": 3951, "operadoraNome": "<PERSON>"}, {"idOperadora": 3952, "operadoraNome": "Rede Sul Saúde"}, {"idOperadora": 3953, "operadoraNome": "Ully Clube"}, {"idOperadora": 3954, "operadoraNome": "Megaplan"}, {"idOperadora": 3956, "operadoraNome": "Unimed Costa Verde"}, {"idOperadora": 3957, "operadoraNome": "<PERSON>imed <PERSON>"}, {"idOperadora": 3958, "operadoraNome": "Vitória Benefícios"}, {"idOperadora": 3959, "operadoraNome": "Vidamax"}, {"idOperadora": 3960, "operadoraNome": "Prevenção Saúde"}, {"idOperadora": 3961, "operadoraNome": "D2PAR Benefícios"}, {"idOperadora": 3962, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3963, "operadoraNome": "Unidentis"}, {"idOperadora": 3964, "operadoraNome": "Plano de Saúde São Lucas"}, {"idOperadora": 3965, "operadoraNome": "Unicor"}, {"idOperadora": 3966, "operadoraNome": "Medplan Odonto"}, {"idOperadora": 3967, "operadoraNome": "Care Plano de Saúde Animal"}, {"idOperadora": 3968, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3969, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3970, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3971, "operadoraNome": "Sulamérica O<PERSON>to"}, {"idOperadora": 3972, "operadoraNome": "Notre Dame Intermédica"}, {"idOperadora": 3973, "operadoraNome": "Notre Dame Intermédica Minas"}, {"idOperadora": 3974, "operadoraNome": "Union Assistência Funeral"}, {"idOperadora": 3975, "operadoraNome": "Bradesco <PERSON>"}, {"idOperadora": 3976, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3977, "operadoraNome": "Unimed <PERSON>"}, {"idOperadora": 3978, "operadoraNome": "Intermed"}, {"idOperadora": 3979, "operadoraNome": "Unimed Rio Branco"}, {"idOperadora": 3980, "operadoraNome": "Corpe Saúde"}, {"idOperadora": 3981, "operadoraNome": "Ãngeli"}, {"idOperadora": 3982, "operadoraNome": "Unimed <PERSON>"}, {"idOperadora": 3983, "operadoraNome": "Plano Brasil Saúde"}, {"idOperadora": 3984, "operadoraNome": "Odonto Mix"}, {"idOperadora": 3985, "operadoraNome": "Hub Health"}, {"idOperadora": 3986, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3988, "operadoraNome": "+Dental.Com Tradicional"}, {"idOperadora": 3989, "operadoraNome": "+Dental.Com Ortho+"}, {"idOperadora": 3990, "operadoraNome": "+Dental.Com Invisa+"}, {"idOperadora": 3991, "operadoraNome": "+Dental.Com Implante+"}, {"idOperadora": 3992, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 3993, "operadoraNome": "3S Administradora"}, {"idOperadora": 3994, "operadoraNome": "MAG Seguros"}, {"idOperadora": 3995, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 3996, "operadoraNome": "AZOS - Seguros"}, {"idOperadora": 3997, "operadoraNome": "Campeã Administradora"}, {"idOperadora": 3998, "operadoraNome": "Vida & Saúde"}, {"idOperadora": 4000, "operadoraNome": "GS Saúde"}, {"idOperadora": 4001, "operadoraNome": "Unity"}, {"idOperadora": 4002, "operadoraNome": "Trino"}, {"idOperadora": 4004, "operadoraNome": "Bioral Sistema Odontológico"}, {"idOperadora": 4005, "operadoraNome": "Atitude Saúde"}, {"idOperadora": 4006, "operadoraNome": "<PERSON>ess<PERSON><PERSON>"}, {"idOperadora": 4007, "operadoraNome": "Odontoplan"}, {"idOperadora": 4008, "operadoraNome": "Global Benefícios"}, {"idOperadora": 4009, "operadoraNome": "Alpha Saúde"}, {"idOperadora": 4010, "operadoraNome": "Nova Odonto"}, {"idOperadora": 4011, "operadoraNome": "ABPS"}, {"idOperadora": 4013, "operadoraNome": "Sagrada Familia"}, {"idOperadora": 4014, "operadoraNome": "Saúde Total"}, {"idOperadora": 4015, "operadoraNome": "Epharma"}, {"idOperadora": 4017, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4021, "operadoraNome": "Unimed Vale do São Francisco"}, {"idOperadora": 4022, "operadoraNome": "Ceam <PERSON>"}, {"idOperadora": 4024, "operadoraNome": "Unimed Nova Iguaçu"}, {"idOperadora": 4025, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4026, "operadoraNome": "Aplus Administradora"}, {"idOperadora": 4027, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4028, "operadoraNome": "Sóvc"}, {"idOperadora": 4029, "operadoraNome": "Manual Base"}, {"idOperadora": 4030, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4031, "operadoraNome": "ANIC"}, {"idOperadora": 4032, "operadoraNome": "ABCPREV"}, {"idOperadora": 4033, "operadoraNome": "Unipactum"}, {"idOperadora": 4034, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4035, "operadoraNome": "Odont"}, {"idOperadora": 4036, "operadoraNome": "<PERSON>"}, {"idOperadora": 4037, "operadoraNome": "Gestão Saúde"}, {"idOperadora": 4038, "operadoraNome": "ABRAINFO"}, {"idOperadora": 4039, "operadoraNome": "HGU Saúde"}, {"idOperadora": 4040, "operadoraNome": "<PERSON>"}, {"idOperadora": 4041, "operadoraNome": "Retroativo"}, {"idOperadora": 4042, "operadoraNome": "Impetu"}, {"idOperadora": 4043, "operadoraNome": "Gympass"}, {"idOperadora": 4044, "operadoraNome": "Garantia <PERSON>"}, {"idOperadora": 4045, "operadoraNome": "Trindade Tecnologia"}, {"idOperadora": 4046, "operadoraNome": "Ativia <PERSON>"}, {"idOperadora": 4047, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4048, "operadoraNome": "<PERSON><PERSON> de <PERSON>ú<PERSON>"}, {"idOperadora": 4049, "operadoraNome": "Unimed Metropolitana do Agreste"}, {"idOperadora": 4050, "operadoraNome": "Univida Saúde"}, {"idOperadora": 4051, "operadoraNome": "Univida Administradora"}, {"idOperadora": 4052, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4053, "operadoraNome": "Fundação São Francisco Xavier"}, {"idOperadora": 4054, "operadoraNome": "Onmed"}, {"idOperadora": 4055, "operadoraNome": "Preserve Saúde"}, {"idOperadora": 4056, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4057, "operadoraNome": "Affinity Se<PERSON>ro <PERSON>"}, {"idOperadora": 4058, "operadoraNome": "Fenix Medical"}, {"idOperadora": 4059, "operadoraNome": "APASP"}, {"idOperadora": 4060, "operadoraNome": "Saúde Medicol"}, {"idOperadora": 4061, "operadoraNome": "Di<PERSON><PERSON>"}, {"idOperadora": 4062, "operadoraNome": "ASSEMGS"}, {"idOperadora": 4063, "operadoraNome": "Serma (Green Line)"}, {"idOperadora": 4064, "operadoraNome": "Samcil (Green Line)"}, {"idOperadora": 4065, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4066, "operadoraNome": "Interbrasil Saúde"}, {"idOperadora": 4067, "operadoraNome": "Catedral"}, {"idOperadora": 4068, "operadoraNome": "Assistência Funerária Grupo Zelo"}, {"idOperadora": 4069, "operadoraNome": "Blue"}, {"idOperadora": 4070, "operadoraNome": "Iclubpet"}, {"idOperadora": 4071, "operadoraNome": "Humana Saúde Sul"}, {"idOperadora": 4072, "operadoraNome": "ASPMEF"}, {"idOperadora": 4073, "operadoraNome": "Med Corp Saúde"}, {"idOperadora": 4074, "operadoraNome": "Medsul (Plan Saúde)"}, {"idOperadora": 4075, "operadoraNome": "Saúde PAS"}, {"idOperadora": 4076, "operadoraNome": "Saúde PAS - Administradora"}, {"idOperadora": 4078, "operadoraNome": "Clin - Plano Odonto Digital"}, {"idOperadora": 4079, "operadoraNome": "Alba Seguradora"}, {"idOperadora": 4080, "operadoraNome": "Você Total"}, {"idOperadora": 4081, "operadoraNome": "Servdonto - Administradora"}, {"idOperadora": 4082, "operadoraNome": "Zaloo"}, {"idOperadora": 4083, "operadoraNome": "Gente Seguradora"}, {"idOperadora": 4084, "operadoraNome": "Vida Top+ Saúde"}, {"idOperadora": 4085, "operadoraNome": "Novodente"}, {"idOperadora": 4086, "operadoraNome": "AEDUC"}, {"idOperadora": 4087, "operadoraNome": "Pré-Cadastro de Produção Administradora"}, {"idOperadora": 4088, "operadoraNome": "Aurora Saúde"}, {"idOperadora": 4089, "operadoraNome": "Go <PERSON> Saúde <PERSON>"}, {"idOperadora": 4090, "operadoraNome": "Viva Vida"}, {"idOperadora": 4091, "operadoraNome": "Nosamed Assistência Médica Ltda"}, {"idOperadora": 4092, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4093, "operadoraNome": "Conexa Saúde"}, {"idOperadora": 4094, "operadoraNome": "Vital Card Se<PERSON>"}, {"idOperadora": 4095, "operadoraNome": "Ideal Emergências Médicas"}, {"idOperadora": 4096, "operadoraNome": "Plenum Saúde"}, {"idOperadora": 4097, "operadoraNome": "Pessoal Saú<PERSON>"}, {"idOperadora": 4098, "operadoraNome": "Alymente Benefícios"}, {"idOperadora": 4099, "operadoraNome": "Saúde Brasil"}, {"idOperadora": 4100, "operadoraNome": "Barreira Grande Corretora"}, {"idOperadora": 4101, "operadoraNome": "AOPP"}, {"idOperadora": 4102, "operadoraNome": "Bauen Life"}, {"idOperadora": 4103, "operadoraNome": "+Pet"}, {"idOperadora": 4104, "operadoraNome": "Assistência Funerárea Grupo Mega"}, {"idOperadora": 4105, "operadoraNome": "Centro Clinico Gaucho Odonto"}, {"idOperadora": 4106, "operadoraNome": "HB Saúde (Hapvida Notredame Intermédica)"}, {"idOperadora": 4107, "operadoraNome": "Plano de Saúde São Camilo"}, {"idOperadora": 4108, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4109, "operadoraNome": "HSMED Saúde"}, {"idOperadora": 4110, "operadoraNome": "Memorial Prev"}, {"idOperadora": 4111, "operadoraNome": "Verde Benefícios"}, {"idOperadora": 4112, "operadoraNome": "<PERSON><PERSON>."}, {"idOperadora": 4113, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 4114, "operadoraNome": "Unimed Araguaia"}, {"idOperadora": 4115, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 4116, "operadoraNome": "Unity Saúde"}, {"idOperadora": 4118, "operadoraNome": "PS Padrão"}, {"idOperadora": 4119, "operadoraNome": "<PERSON>"}, {"idOperadora": 4120, "operadoraNome": "ADCPLASP"}, {"idOperadora": 4121, "operadoraNome": "Unimed Goiania"}, {"idOperadora": 4122, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4123, "operadoraNome": "ASSPS"}, {"idOperadora": 4124, "operadoraNome": "Capital Benefícios"}, {"idOperadora": 4125, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4126, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4127, "operadoraNome": "Bom Pastor <PERSON> de Assistência Funeral"}, {"idOperadora": 4128, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4129, "operadoraNome": "APUB Saúde"}, {"idOperadora": 4130, "operadoraNome": "APUB Sindicato"}, {"idOperadora": 4131, "operadoraNome": "Próspera Saúde"}, {"idOperadora": 4132, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4133, "operadoraNome": "Sabin Sinai"}, {"idOperadora": 4134, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4135, "operadoraNome": "Luca <PERSON> Saúde"}, {"idOperadora": 4136, "operadoraNome": "Plataforma de Telemedicina"}, {"idOperadora": 4137, "operadoraNome": "Beplus"}, {"idOperadora": 4138, "operadoraNome": "Select"}, {"idOperadora": 4139, "operadoraNome": "Medserv Saúde e Benefícios"}, {"idOperadora": 4140, "operadoraNome": "Unimed <PERSON>"}, {"idOperadora": 4141, "operadoraNome": "Cedplan <PERSON>"}, {"idOperadora": 4142, "operadoraNome": "ASMEFE"}, {"idOperadora": 4143, "operadoraNome": "ASSERCOM"}, {"idOperadora": 4144, "operadoraNome": "SIRENORTE"}, {"idOperadora": 4145, "operadoraNome": "V Rocchi Way"}, {"idOperadora": 4146, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4147, "operadoraNome": "<PERSON>a"}, {"idOperadora": 4148, "operadoraNome": "SINDEPRES"}, {"idOperadora": 4149, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4150, "operadoraNome": "Adventist Health"}, {"idOperadora": 4151, "operadoraNome": "HFC Saúde"}, {"idOperadora": 4152, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 4153, "operadoraNome": "EZZE Seguros"}, {"idOperadora": 4154, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4156, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4158, "operadoraNome": "<PERSON>qquer Administradora"}, {"idOperadora": 4159, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4160, "operadoraNome": "Tutor&Pet"}, {"idOperadora": 4161, "operadoraNome": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"idOperadora": 4162, "operadoraNome": "Fox Saúde"}, {"idOperadora": 4163, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4164, "operadoraNome": "Mega ADM"}, {"idOperadora": 4165, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4166, "operadoraNome": "Unimed <PERSON>"}, {"idOperadora": 4167, "operadoraNome": "SITIPAN"}, {"idOperadora": 4168, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4169, "operadoraNome": "ASSBRACOM"}, {"idOperadora": 4170, "operadoraNome": "ABESPUB"}, {"idOperadora": 4171, "operadoraNome": "Leve Dental"}, {"idOperadora": 4172, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4173, "operadoraNome": "<PERSON><PERSON><PERSON>"}, {"idOperadora": 4174, "operadoraNome": "Opção Única"}, {"idOperadora": 4175, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4176, "operadoraNome": "<PERSON><PERSON>"}, {"idOperadora": 4177, "operadoraNome": "Guardmed"}, {"idOperadora": 4178, "operadoraNome": "<PERSON><PERSON>"}]}