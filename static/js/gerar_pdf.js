const puppeteer = require('puppeteer');
const fs = require('fs');

// Caminho do Chrome no Heroku
const CHROME_PATH = '/app/.apt/usr/bin/google-chrome';

// Captura o argumento que contém o caminho para o arquivo temporário
const args = process.argv.slice(2);
const filePath = args[0];

// Lê o arquivo JSON contendo os dados da tabela
fs.readFile(filePath, 'utf8', async (err, data) => {
    if (err) {
        console.error('Erro ao ler o arquivo JSON:', err);
        process.exit(1);
    }

    const tabelaDados = JSON.parse(data);

    // Função de geração de tabela
    function gerarTabelaHTML(dados) {
        let tabelaHTML = `
            <html>
            <head>
                <style>
                    body {
                        font-family: 'Montserrat', sans-serif;
                        margin: 20px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                        font-size: 10px;
                    }
                    th, td {
                        border: 1px solid black;
                        padding: 8px;
                        text-align: left;
                    }
                    th {
                        background-color: #D66833;
                        color: white;
                        font-weight: bold;
                    }
                    tr:nth-child(even) {
                        background-color: #f2f2f2;
                    }
                    @page {
                        margin: 30px;
                    }
                </style>
            </head>
            <body>
                <h2>Grade de Comissão</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Operadora</th>
                            <th>Modalidade</th>
                            <th>Grade</th>
                            <th>Totais</th>
                            <th>1ª Parcela</th>
                            <th>2ª Parcela</th>
                            <th>3ª Parcela</th>
                            <th>4ª Parcela</th>
                            <th>5ª Parcela</th>
                            <th>6ª Parcela</th>
                            <th>7ª Parcela</th>
                            <th>8ª Parcela</th>
                            <th>9ª Parcela</th>
                            <th>10ª Parcela</th>
                            <th>11ª Parcela</th>
                            <th>12ª Parcela</th>
                        </tr>
                    </thead>
                    <tbody>`;

        dados.forEach(row => {
            tabelaHTML += `
                <tr>
                    <td>${row.operadora}</td>
                    <td>${row.modalidade}</td>
                    <td>${row.grade}</td>
                    <td>${(row.totais * 100).toFixed(2)}%</td>
                    <td>${row.parcela_1 === 0 ? '-' : (row.parcela_1 * 100).toFixed(2) + '%'}</td>
                    <td>${row.parcela_2 === 0 ? '-' : (row.parcela_2 * 100).toFixed(2) + '%'}</td>
                    <td>${row.parcela_3 === 0 ? '-' : (row.parcela_3 * 100).toFixed(2) + '%'}</td>
                    <td>${row.parcela_4 === 0 ? '-' : (row.parcela_4 * 100).toFixed(2) + '%'}</td>
                    <td>${row.parcela_5 === 0 ? '-' : (row.parcela_5 * 100).toFixed(2) + '%'}</td>
                    <td>${row.parcela_6 === 0 ? '-' : (row.parcela_6 * 100).toFixed(2) + '%'}</td>
                    <td>${row.parcela_7 === 0 ? '-' : (row.parcela_7 * 100).toFixed(2) + '%'}</td>
                    <td>${row.parcela_8 === 0 ? '-' : (row.parcela_8 * 100).toFixed(2) + '%'}</td>
                    <td>${row.parcela_9 === 0 ? '-' : (row.parcela_9 * 100).toFixed(2) + '%'}</td>
                    <td>${row.parcela_10 === 0 ? '-' : (row.parcela_10 * 100).toFixed(2) + '%'}</td>
                    <td>${row.parcela_11 === 0 ? '-' : (row.parcela_11 * 100).toFixed(2) + '%'}</td>
                    <td>${row.parcela_12 === 0 ? '-' : (row.parcela_12 * 100).toFixed(2) + '%'}</td>
                </tr>`;
        });

        tabelaHTML += `
                    </tbody>
                </table>
            </body>
            </html>`;

        return tabelaHTML;
    }

    // Gera a tabela HTML com base nos dados da tabela
    const tabelaHTML = gerarTabelaHTML(tabelaDados);

    try {
        // Lançamento do Puppeteer com o caminho do Chrome no Heroku
        const browser = await puppeteer.launch({
            executablePath: CHROME_PATH,
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const page = await browser.newPage();
        await page.setContent(tabelaHTML);
        await page.pdf({
            path: 'tabela_filtrada.pdf',
            format: 'A4',
            landscape: true,  // Formato horizontal
            margin: {
                top: '30px',
                bottom: '30px',
                left: '20px',
                right: '20px'
            },
            displayHeaderFooter: true,
            footerTemplate: `
                <div style="font-size: 10px; text-align: center; width: 100%;">
                    <span class="pageNumber"></span> / <span class="totalPages"></span>
                </div>`,
            printBackground: true  // Fundo do cabeçalho
        });

        console.log('PDF gerado com sucesso.');
        await browser.close();
    } catch (err) {
        console.error('Erro ao gerar o PDF:', err);
        process.exit(1);
    }
});