document.addEventListener("DOMContentLoaded", function () {
    // Função para carregar candidaturas com base no ID do seletivo
    function carregarCandidaturas(id_seletivo) {
        if (!id_seletivo) {
            $('#seletivo-table tbody').empty();  // Limpa a tabela se nenhum processo for selecionado
            return;
        }

        $.ajax({
            url: '/consulta_candidaturas/' + id_seletivo,
            method: 'GET',
            success: function (response) {
                var tbody = $('#seletivo-table tbody');
                tbody.empty();

                if (response.length > 0) {
                    // Preenche a tabela com as candidaturas
                    response.forEach(function (item, index) {
                        var rowClass = index % 2 === 0 ? 'bg-light' : 'bg-white';
                        var tr = `<tr class="${rowClass}">
                                    <td>${item.nome}</td>
                                    <td>${item.departamento_atual}</td>
                                    <td>${item.cargo_atual}</td>
                                    <td>${item.cnh}</td>
                                    <td>${item.carro}</td>
                                    <td>${item.formacao}</td>
                                    <td>${item.formacao_detal}</td>
                                    <td>${item.certificacao}</td>
                                    <td>${item.motivo}</td>
                                  </tr>`;
                        tbody.append(tr);
                    });
                } else {
                    tbody.append('<tr><td colspan="4">Nenhuma candidatura encontrada.</td></tr>');
                }
            },
            error: function (xhr, status, error) {
                console.error('Erro ao carregar candidaturas: ', error);
            }
        });
    }

    // Função para exportar a tabela para Excel
    document.getElementById('export-excel').addEventListener('click', function () {
        var table = document.getElementById('seletivo-table');
        var wb = XLSX.utils.table_to_book(table, { sheet: "Candidaturas" });
        XLSX.writeFile(wb, 'candidaturas.xlsx');
    });

    // Inicializa DataTables para a tabela de candidaturas
    $('#seletivo-table').DataTable({
        paging: false,
        searching: false,
        ordering: false,
        info: false
    });

    // Quando o seletor muda, carregar as candidaturas correspondentes
    document.getElementById('seletivo-select').addEventListener('change', function () {
        var id_seletivo = this.value;
        carregarCandidaturas(id_seletivo);
    });

    // Inicia a página com a tabela limpa (sem candidaturas selecionadas)
    carregarCandidaturas("");
});
