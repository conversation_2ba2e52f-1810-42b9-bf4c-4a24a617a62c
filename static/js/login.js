document.addEventListener('DOMContentLoaded', function () {
    const loginForm = document.querySelector('.login-form');
    const windowsUsernameField = document.getElementById('windows_username');

    if (loginForm) {
        // Captura o nome de usuário do Windows via lógica de simulação
        fetch('/get_windows_username')
            .then(response => response.json())
            .then(data => {
                windowsUsernameField.value = data.username || "unknown_user";
            })
            .catch(error => {
                console.error('Error fetching username:', error);
                windowsUsernameField.value = "unknown_user";
            });

        loginForm.addEventListener('submit', function (e) {
            e.preventDefault();

            const formData = new FormData(loginForm);

            fetch('/login', {
                method: 'POST',
                body: formData,
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = data.redirect_url;
                    } else {
                        if (data.redirect_url) {
                            window.location.href = data.redirect_url;
                        } else {
                            alert(data.message);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        });
    }
});
