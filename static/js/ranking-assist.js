$(document).ready(function () {
    console.log("Initializing DataTable...");

    // Função para atualizar a legenda de última atualização
    function updateLastExecutionTime() {
        $.ajax({
            url: "/api/last-execution",
            method: "GET",
            success: function (response) {
                if (response.status === 'success' && response.data) {
                    $('#last-update').html(`Última atualização: ${response.data.data}`);
                }
            },
            error: function (xhr, status, error) {
                console.log("Error fetching last execution time: ", error);
            }
        });
    }

    // Atualiza a legenda quando a página carrega
    updateLastExecutionTime();

    // Função de ordenação customizada para DataTables que entende o formato MM/YYYY
    jQuery.extend(jQuery.fn.dataTableExt.oSort, {
        "monthYear-pre": function (a) {
            var parts = a.split('/');
            return new Date(parts[1], parts[0] - 1).getTime();
        },
        "monthYear-asc": function (a, b) {
            return ((a < b) ? -1 : ((a > b) ? 1 : 0));
        },
        "monthYear-desc": function (a, b) {
            return ((a < b) ? 1 : ((a > b) ? -1 : 0));
        }
    });

    function loadMonthYearOptions() {
        $.ajax({
            url: "/api/meses_anos",
            method: "GET",
            success: function (data) {
                console.log("Month/Year options loaded: ", data);

                // Ordena o array de meses/anos em ordem decrescente
                data.sort(function (a, b) {
                    var aParts = a.split('/');
                    var bParts = b.split('/');
                    var aDate = new Date(aParts[1], aParts[0] - 1);
                    var bDate = new Date(bParts[1], bParts[0] - 1);
                    return bDate - aDate;
                });

                $('#monthYearPicker').empty();
                data.forEach(function (monthYear) {
                    $('#monthYearPicker').append($('<option>', {
                        value: monthYear,
                        text: monthYear
                    }));
                });
                $('#monthYearPicker').multiselect('rebuild');

                // Selecionar o mês mais atual
                var latestMonthYear = data[0]; // Assume que a lista está ordenada do mais recente para o mais antigo
                $('#monthYearPicker').multiselect('select', latestMonthYear);

                $('#rankingTable').DataTable().ajax.reload(); // Recarregar a tabela com o mês/ano selecionado
            },
            error: function (xhr, status, error) {
                console.log("Error fetching month/year options: ", xhr.responseText);
            }
        });
    }

    function getCurrentMonthYear() {
        var date = new Date();
        var month = (date.getMonth() + 1).toString().padStart(2, '0');
        var year = date.getFullYear();
        return month + '/' + year;
    }

    $('#monthYearPicker').multiselect({
        enableFiltering: true,
        includeSelectAllOption: true,
        buttonWidth: '250px',
        nonSelectedText: 'Selecione Mês/Ano',
        allSelectedText: 'Todos os Meses/Anos',
        onChange: function (option, checked) {
            console.log("Month/Year changed: ", $('#monthYearPicker').val());
            $('#rankingTable').DataTable().ajax.reload();
        }
    });

    loadMonthYearOptions();

    $('#rankingTable').DataTable({
        "columnDefs": [
            { "type": "monthYear", "targets": 0 }  // Aplica a função de ordenação customizada à primeira coluna
        ],
        "order": [[1, 'desc']],  // Ordena pela segunda coluna (Total) em ordem decrescente
        "ajax": {
            "url": "/api/ranking",
            "dataSrc": function (json) {
                let data = {};
                json.forEach(function (item) {
                    if (!data[item.assistente]) {
                        data[item.assistente] = {
                            assistente: item.assistente,
                            vltotal: 0,
                            vlcontrato: 0,
                            vlvida: 0,
                            vidacount: 0,
                            corretorcount: 0,
                            contratocount: 0
                        };
                    }
                    data[item.assistente].vltotal += item.vltotal;
                    data[item.assistente].vlcontrato += item.vlcontrato;
                    data[item.assistente].vlvida += item.vlvida;
                    data[item.assistente].vidacount += item.vidacount;
                    data[item.assistente].corretorcount += item.corretorcount;
                    data[item.assistente].contratocount += item.contratocount;
                });
                return Object.values(data);
            },
            "data": function (d) {
                let mesAno = $('#monthYearPicker').val();
                console.log("Fetching data for: ", mesAno);
                if (mesAno) {
                    d.mesAno = mesAno.join(',');
                } else {
                    d.mesAno = getCurrentMonthYear();
                }
            },
            "error": function (xhr, error, thrown) {
                console.log("Error fetching data: ", xhr.responseText);
            }
        },
        "columns": [
            { "data": "assistente", "width": "1000px" },
            { "data": "vltotal", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ '), "width": "350px" },
            { "data": "vidacount", "width": "100px" },
            { "data": "corretorcount", "width": "100px" },
            { "data": "contratocount", "width": "100px" },
            { "data": "vlcontrato", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ '), "width": "200px" },
            { "data": "vlvida", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ '), "width": "150px" }
        ],
        "dom": 'Bfrtip',
        "buttons": ['copy', 'csv', 'excel', 'pdf', 'print'],
        "paging": false,
        "searching": false,
        "info": false,
        "footerCallback": function (row, data, start, end, display) {
            var api = this.api(), data;

            var intVal = function (i) {
                return typeof i === 'string' ?
                    i.replace(/[\$,R$]/g, '') * 1 :
                    typeof i === 'number' ?
                        i : 0;
            };

            var totalTotal = api
                .column(1, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalVidas = api
                .column(2, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalCorretores = api
                .column(3, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalContratos = api
                .column(4, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalMediaContrato = api
                .column(5, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalMediaVida = api
                .column(6, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            $(api.column(1).footer()).html('R$ ' + totalTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            $(api.column(2).footer()).html(totalVidas.toLocaleString('pt-BR'));
            $(api.column(3).footer()).html(totalCorretores.toLocaleString('pt-BR'));
            $(api.column(4).footer()).html(totalContratos.toLocaleString('pt-BR'));
            $(api.column(5).footer()).html('R$ ' + totalMediaContrato.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            $(api.column(6).footer()).html(totalMediaVida.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
        }
    }).buttons().container().appendTo('#buttons-container');

    console.log("DataTable initialized.");
});
