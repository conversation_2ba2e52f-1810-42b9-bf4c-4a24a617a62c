function loadContent(contentType) {
    const contentContainer = document.getElementById('content-container');
    let content = '';

    switch (contentType) {
        case 'dashboard-resumo':
            content = `
                <h1>Dashboard Resumo</h1>
                <p>Bem-vindo à página de Gestão. Aqui você encontrará informações e recursos importantes para a gestão da sua área.</p>
                <!-- Adicione aqui o conteúdo relevante sobre dashboard-resumo -->
            `;
            break;
        case 'gestao-clientes':
            content = `
                <h1>Gestão de Clientes</h1>
                <p>Gerencie informações e interações com clientes.</p>
                <!-- Adicione aqui o conteúdo relevante sobre gestão de clientes -->
            `;
            break;
        case 'gestao-politicas':
            content = `
                <h1>Gestão de Políticas</h1>
                <p>Administre todas as políticas de seguro oferecidas pela corretora.</p>
                <!-- Adicione aqui o conteúdo relevante sobre gestão de políticas -->
            `;
            break;
        case 'gestao-leads':
            content = `
                <h1>Gestão de Leads</h1>
                <p>Administre os leads e estratégias de conversão.</p>
                <!-- Adicione aqui o conteúdo relevante sobre gestão de leads -->
            `;
            break;
        case 'relatorios-analises':
            content = `
                <h1>Relatórios e Análises</h1>
                <p>Visualize relatórios detalhados e análises de desempenho.</p>
                <!-- Adicione aqui o conteúdo relevante sobre relatórios e análises -->
            `;
            break;
        case 'gestao-equipe':
            content = `
                <h1>Gestão de Equipe</h1>
                <p>Gerencie a equipe e aloque recursos de forma eficiente.</p>
                <!-- Adicione aqui o conteúdo relevante sobre gestão de equipe -->
            `;
            break;
        case 'compliance-regulamentacao':
            content = `
                <h1>Compliance e Regulamentação</h1>
                <p>Monitore e assegure conformidade com as regulamentações vigentes.</p>
                <!-- Adicione aqui o conteúdo relevante sobre compliance e regulamentação -->
            `;
            break;
        case 'portal-documentos':
            content = `
                <h1>Portal de Documentos</h1>
                <p>Acesse documentos importantes e compartilhe com a equipe.</p>
                <!-- Adicione aqui o conteúdo relevante sobre portal de documentos -->
            `;
            break;
        case 'integracoes':
            content = `
                <h1>Integrações</h1>
                <p>Conecte-se com outros sistemas e plataformas.</p>
                <!-- Adicione aqui o conteúdo relevante sobre integrações -->
            `;
            break;
        case 'suporte-treinamento':
            content = `
                <h1>Suporte e Treinamento</h1>
                <p>Obtenha suporte técnico e acesse recursos de treinamento.</p>
                <!-- Adicione aqui o conteúdo relevante sobre suporte e treinamento -->
            `;
            break;
        default:
            content = `<p>Conteúdo não encontrado.</p>`;
    }

    contentContainer.innerHTML = content;
}
