document.addEventListener('DOMContentLoaded', function () {
    const headers = document.querySelectorAll('.admin-table th');

    // Eventos para linhas clicáveis
    document.querySelectorAll('.clickable-row').forEach(function (row) {
        row.addEventListener('click', function () {
            const userId = this.getAttribute('data-id');
            window.location.href = `/change_user/${userId}`;
        });
    });

    // Inicializa os filtros da tabela
    initTableFilters();

    // Evento de busca
    const searchInput = document.getElementById("searchInput");
    if (searchInput) {
        // Preencher o campo de busca com o valor do filtro atual
        const urlParams = new URLSearchParams(window.location.search);
        const filtroColuna = urlParams.get('filtro_coluna');
        const filtroValor = urlParams.get('filtro_valor');

        if (filtroColuna === 'nome' && filtroValor) {
            searchInput.value = filtroValor;
        }

        // Adicionar evento para busca ao pressionar Enter
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchTable();
            }
        });

        // Adicionar evento para busca ao clicar no ícone de busca ou após um tempo sem digitar
        let timeoutId;
        searchInput.addEventListener('input', function() {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(function() {
                // Não fazer busca automática, apenas ao pressionar Enter
            }, 500);
        });
    }

    // Adiciona evento ao botão de exportação
    const exportExcelBtn = document.getElementById('exportExcelBtn');
    if (exportExcelBtn) {
        exportExcelBtn.addEventListener('click', function () {
            window.location.href = '/export_usuarios';
        });
    }

    // Destacar a coluna ordenada
    const urlParams = new URLSearchParams(window.location.search);
    const ordenarPor = urlParams.get('ordenar_por');
    const direcao = urlParams.get('direcao');

    if (ordenarPor) {
        const columnNames = [
            "id", "tipo_usuario", "nome", "data_nascimento",
            "cpf", "email", "email_confirmed", "telefone"
        ];

        const columnIndex = columnNames.indexOf(ordenarPor);
        if (columnIndex !== -1) {
            const th = document.querySelectorAll('.admin-table th')[columnIndex];
            th.classList.add('sorted');
            th.classList.add(direcao === 'desc' ? 'sorted-desc' : 'sorted-asc');
        }
    }
});

let activeFilters = {}; // Armazena os filtros ativos

function initTableFilters() {
    const headers = document.querySelectorAll('.admin-table th');

    headers.forEach((header, index) => {
        const button = header.querySelector('.filter-button');
        if (button) {
            button.addEventListener('click', function (event) {
                event.stopPropagation(); // Impede a propagação do evento para fechar o dropdown
                toggleDropdown(event, index);
            });
        }
    });
}

function toggleDropdown(event, columnIndex) {
    // Verifica se o dropdown já está aberto
    const currentDropdown = document.querySelector('.dropdown-menu');
    if (currentDropdown && currentDropdown.dataset.columnIndex == columnIndex) {
        closeAllDropdowns(); // Fecha o dropdown se já estiver aberto
        return; // Sai da função se o dropdown for o mesmo que já está aberto
    }
    closeAllDropdowns(); // Fecha outros dropdowns abertos
    const dropdown = createDropdown(columnIndex);
    dropdown.dataset.columnIndex = columnIndex; // Armazena o índice da coluna no dropdown
    document.body.appendChild(dropdown);
    positionDropdown(dropdown, event.target);

    dropdown.addEventListener('click', function (event) {
        event.stopPropagation();
    });

    // Adiciona um listener para fechar o dropdown quando clicar fora
    document.addEventListener('click', closeAllDropdowns, { once: true });
}

function createDropdown(columnIndex) {
    const dropdown = document.createElement('div');
    dropdown.classList.add('dropdown-menu', 'show');

    // Adiciona um indicador de carregamento
    dropdown.innerHTML = `
<div class="sort-options">
<button onclick="sortColumn(${columnIndex}, 'asc')"><i class="bi bi-sort-alpha-down"></i></button><p>Classificar de A a Z</p>
</div>
<div class="sort-options">
<button onclick="sortColumn(${columnIndex}, 'desc')"><i class="bi bi-sort-alpha-down-alt"></i></button><p>Classificar de Z a A</p>
</div>
<input type="text" placeholder="Pesquisar..." oninput="filterDropdownSearch(this, ${columnIndex})">
<div class="loading-indicator">Carregando...</div>
<ul class="filter-options"></ul>
<div class="filter-actions">
<button onclick="applyFilters(${columnIndex})">Aplicar</button>
<button onclick="closeAllDropdowns()">Cancelar</button>
</div>
`;

    // Mapear índice da coluna para nome da coluna
    const columnNames = [
        "id", "tipo_usuario", "nome", "data_nascimento",
        "cpf", "email", "email_confirmed", "telefone"
    ];

    const columnName = columnNames[columnIndex];

    // Buscar valores da API
    fetchColumnValues(columnName, dropdown);

    return dropdown;
}

function fetchColumnValues(columnName, dropdown) {
    fetch(`/api/admin/valores-coluna?coluna=${columnName}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro ao buscar valores da coluna');
            }
            return response.json();
        })
        .then(data => {
            // Remover indicador de carregamento
            const loadingIndicator = dropdown.querySelector('.loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }

            // Preencher as opções do filtro
            const filterOptions = dropdown.querySelector('.filter-options');
            filterOptions.innerHTML = generateFilterOptions(data.valores);
        })
        .catch(error => {
            console.error('Erro:', error);
            // Em caso de erro, usar os valores da página atual
            const loadingIndicator = dropdown.querySelector('.loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }

            const filterOptions = dropdown.querySelector('.filter-options');
            const uniqueValues = getUniqueColumnValues(parseInt(columnIndex));
            filterOptions.innerHTML = generateFilterOptions(uniqueValues);
        });
}

function getUniqueColumnValues(columnIndex) {
    const values = new Set();
    document.querySelectorAll(`.admin-table tbody tr`).forEach(row => {
        const cellText = row.cells[columnIndex]?.innerText.trim();
        if (cellText) values.add(cellText);
    });
    return [...values];
}

function generateFilterOptions(uniqueValues) {
    return uniqueValues.map(value => {
        const safeValue = value.replace(/"/g, '&quot;');
        return `<li>
        <input type="checkbox" id="filter-${safeValue}" name="filter-option" value="${safeValue}">
        <label for="filter-${safeValue}">${value}</label>
    </li>`;
    }).join('');
}

function positionDropdown(dropdown, referenceElement) {
    const tableRect = document.querySelector('.admin-table').getBoundingClientRect();
    const buttonRect = referenceElement.getBoundingClientRect();
    const theadRect = document.querySelector('.admin-table thead').getBoundingClientRect();

    dropdown.style.position = 'absolute';

    dropdown.style.top = `${theadRect.bottom}px`;

    let leftPosition = buttonRect.left + (buttonRect.width / 2) - (dropdown.offsetWidth / 2);
    leftPosition = Math.max(tableRect.left, leftPosition);
    leftPosition = Math.min(leftPosition, tableRect.right - dropdown.offsetWidth);

    dropdown.style.left = `${leftPosition}px`;
}

function sortColumn(columnIndex, order) {
    // Mapear índice da coluna para nome da coluna
    const columnNames = [
        "id", "tipo_usuario", "nome", "data_nascimento",
        "cpf", "email", "email_confirmed", "telefone"
    ];

    const columnName = columnNames[columnIndex];

    // Redirecionar para a mesma página com parâmetros de ordenação
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('ordenar_por', columnName);
    currentUrl.searchParams.set('direcao', order);

    // Manter os parâmetros de filtro existentes
    const filtroColuna = currentUrl.searchParams.get('filtro_coluna');
    const filtroValor = currentUrl.searchParams.get('filtro_valor');

    // Resetar a página para 1 ao ordenar
    currentUrl.searchParams.set('pagina', 1);

    window.location.href = currentUrl.toString();
}

function filterDropdownSearch(inputElement, columnIndex) {
    const searchValue = inputElement.value.toLowerCase();
    const options = inputElement.closest('.dropdown-menu').querySelectorAll('.filter-options li');
    options.forEach(option => {
        const matchesSearch = option.textContent.toLowerCase().includes(searchValue);
        option.style.display = matchesSearch ? '' : 'none';
    });
}

function applyFilters(columnIndex) {
    const checkboxes = document.querySelectorAll('.dropdown-menu .filter-options input[type="checkbox"]:checked');
    const filterValues = Array.from(checkboxes).map(cb => cb.value);

    if (filterValues.length === 0) {
        // Se não houver valores selecionados, apenas fechar o dropdown
        closeAllDropdowns();
        return;
    }

    // Mapear índice da coluna para nome da coluna
    const columnNames = [
        "id", "tipo_usuario", "nome", "data_nascimento",
        "cpf", "email", "email_confirmed", "telefone"
    ];

    const columnName = columnNames[columnIndex];

    // Redirecionar para a mesma página com parâmetros de filtro
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('filtro_coluna', columnName);

    // Se houver apenas um valor selecionado, usamos ele diretamente
    // Se houver múltiplos, precisamos implementar uma lógica no backend para lidar com isso
    // Por enquanto, vamos usar apenas o primeiro valor
    currentUrl.searchParams.set('filtro_valor', filterValues[0]);

    // Manter os parâmetros de ordenação existentes
    const ordenarPor = currentUrl.searchParams.get('ordenar_por');
    const direcao = currentUrl.searchParams.get('direcao');

    // Resetar a página para 1 ao filtrar
    currentUrl.searchParams.set('pagina', 1);

    window.location.href = currentUrl.toString();
}

function closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown-menu');
    dropdowns.forEach(dropdown => dropdown.remove());
}

function searchTable() {
    const input = document.getElementById("searchInput");
    const filter = input.value.trim();

    if (filter === '') {
        return; // Não fazer nada se o campo de busca estiver vazio
    }

    // Redirecionar para a mesma página com parâmetro de busca
    const currentUrl = new URL(window.location.href);

    // Vamos usar o nome como coluna padrão para busca
    currentUrl.searchParams.set('filtro_coluna', 'nome');
    currentUrl.searchParams.set('filtro_valor', filter);

    // Resetar a página para 1 ao buscar
    currentUrl.searchParams.set('pagina', 1);

    window.location.href = currentUrl.toString();
}

document.addEventListener('DOMContentLoaded', function() {
    const clearFilterBtn = document.getElementById('clearFilterBtn');
    if (clearFilterBtn) {
        clearFilterBtn.addEventListener('click', function () {
            // Limpar todos os parâmetros de URL exceto a página atual
            const currentUrl = new URL(window.location.href);
            const pagina = currentUrl.searchParams.get('pagina') || 1;

            // Limpar todos os parâmetros
            currentUrl.search = '';

            // Manter apenas a página atual
            if (pagina > 1) {
                currentUrl.searchParams.set('pagina', pagina);
            }

            // Redirecionar para a URL limpa
            window.location.href = currentUrl.toString();
        });
    }
});
