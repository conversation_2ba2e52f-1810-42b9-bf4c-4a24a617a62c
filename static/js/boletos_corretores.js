document.getElementById("boletos_corretores").addEventListener("click", function (event) {
    event.preventDefault();
    console.log("Carregando página de Boletos 1ª Parcela...");

    // Faz a requisição AJAX para buscar a página
    fetch('/boletos_corretores')
        .then(response => response.text())
        .then(html => {
            document.getElementById("mainContent").innerHTML = html;
            // Inicializa os eventos do formulário dentro da página de boletos
            initializeBoletosForm();
        })
        .catch(error => console.error('Erro ao carregar página de Boletos 1ª Parcela:', error));
});

// Função para inicializar o formulário de boletos
function initializeBoletosForm() {
    document.getElementById("codigo_proposta").addEventListener("change", function () {
        let codigoProposta = this.value;
        console.log("Buscando dados para a proposta:", codigoProposta);

        // Faz a requisição para a API
        fetch(`/api/get_proposta/${codigoProposta}`)
            .then(response => response.json())
            .then(data => {
                console.log("Dados recebidos da API:", data);

                if (!data.error) {
                    document.getElementById("corretor").value = data.corretor || "Não disponível";
                    document.getElementById("supervisor").value = data.supervisor || "Não disponível";
                    document.getElementById("modalidade").value = data.modalidade || "Não disponível";
                    document.getElementById("operadora").value = data.nomeOperadora || "Não disponível";
                    document.getElementById("subproduto").value = data.subProduto || "Não disponível";
                    document.getElementById("segurado").value = data.segurado || "Não disponível";
                    document.getElementById("valor_proposta").value = data.vlBoleto ? data.vlBoleto.toFixed(2) : "Não disponível";
                    document.getElementById("acordo").value = data.comissionamentoGradeProducao || "Não disponível";
                    document.getElementById("tabela_padrao").value = data.comissionamentoGradeCorretor || "Não disponível";

                    // Preenche a tabela com os dados
                    document.getElementById("render_codigo_proposta").innerText = data.codigo_proposta || "Não disponível";
                    document.getElementById("render_corretor").innerText = data.corretor || "Não disponível";
                    document.getElementById("render_supervisor").innerText = data.supervisor || "Não disponível";
                    document.getElementById("render_modalidade").innerText = data.modalidade || "Não disponível";
                    document.getElementById("render_operadora").innerText = data.nomeOperadora || "Não disponível";
                    document.getElementById("render_subproduto").innerText = data.subProduto || "Não disponível";
                    document.getElementById("render_segurado").innerText = data.segurado || "Não disponível";
                    document.getElementById("render_valor_proposta").innerText = data.vlBoleto ? data.vlBoleto.toFixed(2) : "Não disponível";
                    document.getElementById("acordo").value = data.comissionamentoGradeProducao || "Não disponível";
                    document.getElementById("tabela_padrao").value = data.comissionamentoGradeCorretor || "Não disponível";
                }
            })
            .catch(error => console.error('Erro ao buscar dados da proposta:', error));
    });

    // Função de upload de boleto
    document.getElementById('boleto').addEventListener('change', function () {
        const input = document.getElementById('boleto');
        const formData = new FormData();
        formData.append('file', input.files[0]);

        fetch('/upload_boleto', {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(data => {
                if (data.codigo_barras) {
                    document.getElementById('codigo_barras').value = data.codigo_barras;
                    document.getElementById('render_codigo_barras').innerText = data.codigo_barras;
                } else if (data.solicitar_senha) {
                    solicitarSenha(); // Abre o modal para o usuário digitar a senha
                } else if (data.solicitar_codigo_barras) {
                    solicitarCodigoBarras(); // Abre o modal para o usuário digitar o código de barras
                } else {
                    alert('Erro ao ler o código de barras: ' + data.error);
                }
            })
            .catch(error => console.error('Erro no upload do boleto:', error));
    });

    // Função para abrir o modal de senha
    function solicitarSenha() {
        $('#senhaModal').modal('show');
    }

    // Função para abrir o modal de código de barras
    function solicitarCodigoBarras() {
        $('#codigoBarrasModal').modal('show');
    }

    // Função para enviar a senha para o backend
    document.getElementById('senhaForm').addEventListener('submit', function(event) {
        event.preventDefault();
        const senha = document.getElementById('senha').value;
        const formData = new FormData();
        formData.append('file', document.getElementById('boleto').files[0]);
        formData.append('senha', senha);

        fetch('/upload_boleto', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.codigo_barras) {
                document.getElementById('codigo_barras').value = data.codigo_barras;
                document.getElementById('render_codigo_barras').innerText = data.codigo_barras;
                $('#senhaModal').modal('hide');
            } else if (data.solicitar_codigo_barras) {
                solicitarCodigoBarras(); // Abre o modal para o usuário digitar o código de barras
            } else {
                alert('Erro ao ler o código de barras: ' + data.error);
            }
        })
        .catch(error => console.error('Erro no upload do boleto:', error));
    });

    // Função para enviar o código de barras manualmente
    document.getElementById('codigo_barras_manual').addEventListener('submit', function(event) {
        event.preventDefault();
        const codigoBarras = document.getElementById('codigo_barras_manual').value;
        document.getElementById('codigo_barras').value = codigoBarras;
        document.getElementById('render_codigo_barras').innerText = codigoBarras;
        $('#codigoBarrasModal').modal('hide');
    });

    // Função para mostrar a tela de carregamento
    function showLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
    }

    // Função para esconder a tela de carregamento
    function hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }

    // Função de exibir o nome do arquivo selecionado, no upload do boleto
    document.getElementById('boleto').addEventListener('change', function () {
        const input = document.getElementById('boleto');
        const fileName = input.files[0] ? input.files[0].name : 'Nenhum arquivo selecionado';
        document.getElementById('file-name').textContent = fileName;
    });

    // Evento para lidar com o envio do formulário
    document.getElementById("formulario-proposta").addEventListener("submit", function (event) {
        event.preventDefault(); // Previne o comportamento padrão de recarregar a página

        showLoadingScreen();
        const formData = new FormData(this);

        fetch('/enviar_solicitacao', {
            method: 'POST',
            body: formData
        })
            .then(response => {
                hideLoadingScreen();
                if (response.ok) {
                    alert('Solicitação enviada com sucesso!');
                    this.reset(); // Limpa todos os campos do formulário, incluindo campos de texto

                    // Limpar o campo de upload do boleto
                    const fileInput = document.getElementById('boleto');
                    fileInput.value = ''; // Remove o arquivo selecionado
                    document.getElementById('file-name').textContent = 'Nenhum arquivo selecionado'; // Atualiza o nome do arquivo exibido
                } else {
                    throw new Error('Erro ao enviar a solicitação.');
                }
            })
            .catch(error => {
                console.error('Erro ao enviar solicitação:', error);
                hideLoadingScreen();
                alert('Erro ao enviar solicitação. Por favor, tente novamente.');
            });
    });
}
