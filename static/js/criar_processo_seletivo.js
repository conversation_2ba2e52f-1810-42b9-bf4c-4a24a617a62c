document.addEventListener('DOMContentLoaded', function() {
    initializeEditor();

    // Adicione o listener ao formulário
    const form = document.getElementById('seletivo-form');
    if (form) {
        form.addEventListener('submit', function(event) {
            event.preventDefault();
            submitForm(this);
        });
    }
});

function initializeEditor() {
    if (typeof CKEDITOR === 'undefined') {
        console.warn('CKEditor não encontrado!');
        return;
    }

    CKEDITOR.replace('descricao');
    CKEDITOR.config.height = 300;

    // Após a inicialização, anexa o evento para verificar a contagem de caracteres
    CKEDITOR.instances.descricao.on('instanceReady', function () {
        this.document.on('keyup', function () {
            updateCharacterCounter();
        });
        this.document.on('paste', function () {
            setTimeout(updateCharacterCounter, 0); // Assegura a contagem após a conclusão do evento de colar
        });
    });
}

// Função para atualizar o contador de caracteres
function updateCharacterCounter() {
    var content = CKEDITOR.instances.descricao.getData();
    var plainTextContent = CKEDITOR.instances.descricao.document.getBody().getText(); // Obtém o texto sem tags HTML
    var contentLength = plainTextContent.length;
    var counterElement = document.getElementById('conteudo-contador');

    // Atualiza o contador no HTML
    if (counterElement) {
        counterElement.textContent = `${contentLength}/5000`;
    }

    // Se o conteúdo exceder 5000 caracteres, corta para 5000 e impede novas entradas
    if (contentLength > 5000) {
        var trimmedContent = plainTextContent.substring(0, 5000);
        CKEDITOR.instances.descricao.setData(trimmedContent);
        counterElement.textContent = `5000/5000`;
        CKEDITOR.instances.descricao.fire('change');
    }
}

function submitForm(form) {
    const formData = new FormData(form);
    formData.set('descricao', CKEDITOR.instances.descricao.getData());

    const xhr = new XMLHttpRequest();
    xhr.open('POST', form.action, true);

    xhr.onload = function() {
        if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
                alert('Vaga publicada com sucesso!');
                window.location.href = '/';
            } else {
                alert('Erro ao publicar a vaga.');
            }
        } else {
            alert('Erro ao publicar a vaga.');
        }
    };

    xhr.onerror = function() {
        alert('Erro na conexão ao publicar a vaga.');
    };

    xhr.send(formData);
}