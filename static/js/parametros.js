document.addEventListener('DOMContentLoaded', () => {
    const btnMetas = document.getElementById('btnMetas');
    const btnVidasOperadoras = document.getElementById('btnVidasOperadoras');
    const btnVidasAssistentes = document.getElementById('btnVidasAssistentes');
    const btnDataGlobal = document.getElementById('btnDataGlobal');
    const contentArea = document.getElementById('content-area');

    // Função para remover classe active de todos os botões
    function clearActiveButtons() {
        document.querySelectorAll('.nav-item').forEach(btn => {
            btn.classList.remove('active');
        });
    }

    // Função para mostrar mensagem central
    function showMessageCenter() {
        contentArea.innerHTML = `
            <div class="message-center">
                <div>
                    <i class="fa-solid fa-compass text-6xl text-gray-300 mb-4 block"></i>
                    <h3 class="text-xl font-medium">Selecione uma opção no menu</h3>
                    <p class="text-gray-400 mt-2">Escolha uma seção no menu lateral para começar</p>
                </div>
            </div>
        `;
    }

    // Handler para o botão Metas
    btnMetas.addEventListener('click', () => {
        clearActiveButtons();
        btnMetas.classList.add('active');
        loadMetasContent();
    });

    // Handler para o botão Vidas Operadoras
    btnVidasOperadoras.addEventListener('click', () => {
        clearActiveButtons();
        btnVidasOperadoras.classList.add('active');
        loadVidasOperadorasContent();
    });

    // Handler para o botão Vidas Assistentes
    btnVidasAssistentes.addEventListener('click', () => {
        clearActiveButtons();
        btnVidasAssistentes.classList.add('active');
        loadVidasAssistentesContent();
    });

    // Handler para o botão Data Global
    btnDataGlobal.addEventListener('click', () => {
        clearActiveButtons();
        btnDataGlobal.classList.add('active');
        loadDataGlobalContent();
    });

    // Função para carregar conteúdo de Metas
    function loadMetasContent() {
        contentArea.innerHTML = `
            <div class="content-wrapper">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">Gerenciamento de Metas</h2>
                </div>
                <div class="overflow-x-auto">
                    <table id="metasTable" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Canal</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Meta</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <!-- O conteúdo será populado via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        fetchMetas();
    }

    // Função para carregar conteúdo de Vidas Operadoras
    function loadVidasOperadorasContent() {
        contentArea.innerHTML = `
            <div class="content-wrapper">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">Gerenciamento de Vidas por Operadora</h2>
                </div>
                <div class="overflow-x-auto">
                    <table id="vidasOperadorasTable" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operadora</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Meta</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <!-- O conteúdo será populado via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        fetchVidasOperadoras();
    }

    // Função para carregar conteúdo de Vidas Assistentes
    function loadVidasAssistentesContent() {
        contentArea.innerHTML = `
            <div class="content-wrapper">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">Gerenciamento de Vidas por Assistente</h2>
                </div>
                <div class="overflow-x-auto">
                    <table id="vidasAssistentesTable" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assistente</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operadora</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipe</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Meta</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <!-- O conteúdo será populado via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        fetchVidasAssistentes();
    }

    // Função para carregar conteúdo de Data Global
    function loadDataGlobalContent() {
        contentArea.innerHTML = `
            <div class="content-wrapper">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">Período de Referência do Sistema</h2>
                </div>
                <div class="grid grid-cols-1 gap-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <form id="dataGlobalForm" class="space-y-6">
                            <div class="grid grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Data Inicial do Período
                                    </label>
                                    <input type="date" id="dataInicialGlobal" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    <p class="mt-1 text-sm text-gray-500">Início do período de referência</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Data Final do Período
                                    </label>
                                    <input type="date" id="dataFinalGlobal" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    <p class="mt-1 text-sm text-gray-500">Fim do período de referência</p>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button type="submit" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <i class="fa-solid fa-save mr-2"></i>
                                    Salvar Período
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;

        // Adicionar listener para o formulário
        const form = document.getElementById('dataGlobalForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            const dataInicial = document.getElementById('dataInicialGlobal').value;
            const dataFinal = document.getElementById('dataFinalGlobal').value;

            if (new Date(dataFinal) < new Date(dataInicial)) {
                alert('A data final não pode ser menor que a data inicial');
                return;
            }

            updateDataGlobal({
                dt_inicio_global: dataInicial,
                dt_final_global: dataFinal
            });
        });

        // Carregar datas atuais
        fetchDataGlobal();
    }

    // Função para buscar data global atual
    function fetchDataGlobal() {
        console.log('Iniciando fetch para /api/get_datas_globais...');

        fetch('/api/get_datas_globais')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro na resposta do servidor');
                }
                return response.json();
            })
            .then(data => {

                if (data) {
                    // Cria objetos Date a partir das strings
                    const dtInicio = new Date(data.data_inicio);
                    const dtFim = new Date(data.data_fim);

                    // Converte para o formato yyyy-mm-dd
                    const dtInicioISO = dtInicio.toISOString().split('T')[0];
                    const dtFimISO = dtFim.toISOString().split('T')[0];

                    // Define o valor dos inputs
                    document.getElementById('dataInicialGlobal').value = dtInicioISO;
                    document.getElementById('dataFinalGlobal').value = dtFimISO;

                    // Se necessário, armazena o ID retornado pela API
                    if (data.id) {
                        document.getElementById('dataGlobalForm').dataset.dataId = data.id;
                    }
                }
            })
            .catch(error => {
                console.error('Erro ao carregar período de referência:', error);
                alert('Erro ao carregar período de referência. Por favor, tente novamente.');
            });
    }

    // Função para atualizar data global
    function updateDataGlobal(dados) {
        const form = document.getElementById('dataGlobalForm');
        // Definir ID fixo como 1, como mencionado pelo usuário
        const dataId = 1;

        fetch(`/api/update_datas_globais/${dataId}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                data_inicio: dados.dt_inicio_global,
                data_fim: dados.dt_final_global
            })
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro na resposta do servidor');
                }
                return response.json();
            })
            .then(result => {
                if (result.message) {
                    alert('Período de referência atualizado com sucesso!');
                    // Recarregar os dados após atualização bem-sucedida
                    fetchDataGlobal();
                } else {
                    throw new Error(result.error || 'Erro desconhecido');
                }
            })
            .catch(error => {
                console.error('Erro ao atualizar período de referência:', error);
                alert('Erro ao atualizar período de referência. Por favor, tente novamente.');
            });
    }
});

function fetchMetas() {
    // Adiciona estado de carregamento
    const tbody = document.querySelector('#metasTable tbody');
    tbody.innerHTML = `
        <tr>
            <td colspan="4" class="text-center py-8">
                <div class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    <span class="ml-2 text-gray-600">Carregando...</span>
                </div>
            </td>
        </tr>
    `;

    fetch('/api/get_metas', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro na resposta do servidor');
            }
            return response.json();
        })
        .then(data => {
            populateMetasTable(data);
        })
        .catch(error => {
            console.error('Erro ao buscar metas:', error);
            tbody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center py-8 text-red-600">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        Erro ao carregar dados. Tente novamente.
                    </td>
                </tr>
            `;
        });
}

function populateMetasTable(metas) {
    const tbody = document.querySelector('#metasTable tbody');
    tbody.innerHTML = ''; // Limpa conteúdo anterior

    if (metas.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center py-8 text-gray-500">
                    <i class="fas fa-info-circle mr-2"></i>
                    Nenhuma meta encontrada
                </td>
            </tr>
        `;
        return;
    }

    metas.forEach(meta => {
        const tr = document.createElement('tr');
        tr.classList.add('hover:bg-gray-50', 'transition-colors');

        // Coluna ID
        const tdId = document.createElement('td');
        tdId.classList.add('px-6', 'py-4', 'whitespace-nowrap', 'text-sm', 'text-gray-900');
        tdId.textContent = meta.id;

        // Coluna Canal
        const tdCanal = document.createElement('td');
        tdCanal.classList.add('px-6', 'py-4', 'whitespace-nowrap', 'text-sm', 'text-gray-900');
        tdCanal.textContent = meta.canal;

        // Coluna Meta (editável)
        const tdMeta = document.createElement('td');
        tdMeta.classList.add('px-6', 'py-4', 'whitespace-nowrap');

        const inputMeta = document.createElement('input');
        inputMeta.type = 'number';
        inputMeta.value = meta.meta;
        inputMeta.classList.add(
            'w-24',
            'px-3',
            'py-2',
            'border',
            'rounded-md',
            'focus:ring-indigo-500',
            'focus:border-indigo-500',
            'text-sm'
        );
        tdMeta.appendChild(inputMeta);

        // Coluna Ações
        const tdAcoes = document.createElement('td');
        tdAcoes.classList.add('px-6', 'py-4', 'whitespace-nowrap', 'text-sm', 'text-gray-500');

        const btnSalvar = document.createElement('button');
        btnSalvar.innerHTML = '<i class="fas fa-save mr-2"></i>Salvar';
        btnSalvar.classList.add(
            'inline-flex',
            'items-center',
            'px-3',
            'py-2',
            'border',
            'border-transparent',
            'text-sm',
            'font-medium',
            'rounded-md',
            'text-white',
            'bg-indigo-600',
            'hover:bg-indigo-700',
            'focus:outline-none',
            'focus:ring-2',
            'focus:ring-offset-2',
            'focus:ring-indigo-500',
            'transition-colors'
        );

        btnSalvar.addEventListener('click', () => {
            updateMeta(meta.id, inputMeta.value, btnSalvar);
        });

        tdAcoes.appendChild(btnSalvar);

        // Adiciona todas as colunas à linha
        tr.appendChild(tdId);
        tr.appendChild(tdCanal);
        tr.appendChild(tdMeta);
        tr.appendChild(tdAcoes);

        tbody.appendChild(tr);
    });
}

function updateMeta(metaId, valor, btnSalvar) {
    // Desabilita o botão e mostra loading
    const originalContent = btnSalvar.innerHTML;
    btnSalvar.disabled = true;
    btnSalvar.innerHTML = '<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>';
    btnSalvar.classList.add('opacity-75');

    fetch(`/api/update_meta/${metaId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ valor })
    })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }

            // Feedback visual de sucesso
            btnSalvar.innerHTML = '<i class="fas fa-check mr-2"></i>Salvo';
            btnSalvar.classList.remove('bg-indigo-600', 'hover:bg-indigo-700');
            btnSalvar.classList.add('bg-green-600', 'hover:bg-green-700');

            setTimeout(() => {
                btnSalvar.innerHTML = originalContent;
                btnSalvar.classList.remove('bg-green-600', 'hover:bg-green-700');
                btnSalvar.classList.add('bg-indigo-600', 'hover:bg-indigo-700');
                btnSalvar.disabled = false;
                btnSalvar.classList.remove('opacity-75');
            }, 2000);
        })
        .catch(error => {
            console.error('Erro ao atualizar meta:', error);

            // Feedback visual de erro
            btnSalvar.innerHTML = '<i class="fas fa-times mr-2"></i>Erro';
            btnSalvar.classList.remove('bg-indigo-600', 'hover:bg-indigo-700');
            btnSalvar.classList.add('bg-red-600', 'hover:bg-red-700');

            setTimeout(() => {
                btnSalvar.innerHTML = originalContent;
                btnSalvar.classList.remove('bg-red-600', 'hover:bg-red-700');
                btnSalvar.classList.add('bg-indigo-600', 'hover:bg-indigo-700');
                btnSalvar.disabled = false;
                btnSalvar.classList.remove('opacity-75');
            }, 2000);
        });
}

// Função para buscar vidas operadoras
function fetchVidasOperadoras() {
    const tbody = document.querySelector('#vidasOperadorasTable tbody');
    tbody.innerHTML = `
        <tr>
            <td colspan="3" class="text-center py-8">
                <div class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    <span class="ml-2 text-gray-600">Carregando...</span>
                </div>
            </td>
        </tr>
    `;

    fetch('/api/get_metasporvida_operadora')
        .then(response => response.json())
        .then(data => {
            populateVidasOperadorasTable(data);
        })
        .catch(error => {
            console.error('Erro ao buscar vidas operadoras:', error);
            tbody.innerHTML = `
                <tr>
                    <td colspan="3" class="text-center py-8 text-red-600">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        Erro ao carregar dados. Tente novamente.
                    </td>
                </tr>
            `;
        });
}

// Função para popular tabela de vidas operadoras
function populateVidasOperadorasTable(vidas) {
    const tbody = document.querySelector('#vidasOperadorasTable tbody');
    tbody.innerHTML = '';

    if (vidas.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="3" class="text-center py-8 text-gray-500">
                    <i class="fas fa-info-circle mr-2"></i>
                    Nenhuma operadora encontrada
                </td>
            </tr>
        `;
        return;
    }

    vidas.forEach(vida => {
        const tr = document.createElement('tr');
        tr.classList.add('hover:bg-gray-50', 'transition-colors');

        // Coluna Operadora
        const tdOperadora = document.createElement('td');
        tdOperadora.classList.add('px-6', 'py-4', 'whitespace-nowrap', 'text-sm', 'text-gray-900');
        tdOperadora.textContent = vida.operadora;

        // Coluna Meta (editável)
        const tdMeta = document.createElement('td');
        tdMeta.classList.add('px-6', 'py-4', 'whitespace-nowrap');

        const inputMeta = document.createElement('input');
        inputMeta.type = 'number';
        inputMeta.value = vida.metas_vidas;
        inputMeta.classList.add(
            'w-24',
            'px-3',
            'py-2',
            'border',
            'rounded-md',
            'focus:ring-indigo-500',
            'focus:border-indigo-500',
            'text-sm'
        );
        tdMeta.appendChild(inputMeta);

        // Coluna Ações
        const tdAcoes = document.createElement('td');
        tdAcoes.classList.add('px-6', 'py-4', 'whitespace-nowrap', 'text-sm', 'text-gray-500');

        const btnSalvar = document.createElement('button');
        btnSalvar.innerHTML = '<i class="fas fa-save mr-2"></i>Salvar';
        btnSalvar.classList.add(
            'inline-flex',
            'items-center',
            'px-3',
            'py-2',
            'border',
            'border-transparent',
            'text-sm',
            'font-medium',
            'rounded-md',
            'text-white',
            'bg-indigo-600',
            'hover:bg-indigo-700',
            'focus:outline-none',
            'focus:ring-2',
            'focus:ring-offset-2',
            'focus:ring-indigo-500',
            'transition-colors'
        );

        btnSalvar.addEventListener('click', () => {
            updateVidaOperadora(vida.operadora, inputMeta.value, btnSalvar);
        });

        tdAcoes.appendChild(btnSalvar);

        tr.appendChild(tdOperadora);
        tr.appendChild(tdMeta);
        tr.appendChild(tdAcoes);

        tbody.appendChild(tr);
    });
}

// Função para buscar vidas assistentes
function fetchVidasAssistentes() {
    const tbody = document.querySelector('#vidasAssistentesTable tbody');
    tbody.innerHTML = `
        <tr>
            <td colspan="5" class="text-center py-8">
                <div class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    <span class="ml-2 text-gray-600">Carregando...</span>
                </div>
            </td>
        </tr>
    `;

    fetch('/api/get_metas_vidas_assistentes')
        .then(response => response.json())
        .then(data => {
            populateVidasAssistentesTable(data);
        })
        .catch(error => {
            console.error('Erro ao buscar vidas assistentes:', error);
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-8 text-red-600">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        Erro ao carregar dados. Tente novamente.
                    </td>
                </tr>
            `;
        });
}

// Função para popular tabela de vidas assistentes
function populateVidasAssistentesTable(vidas) {
    const tbody = document.querySelector('#vidasAssistentesTable tbody');
    tbody.innerHTML = '';

    if (vidas.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-8 text-gray-500">
                    <i class="fas fa-info-circle mr-2"></i>
                    Nenhum assistente encontrado
                </td>
            </tr>
        `;
        return;
    }

    vidas.forEach(vida => {
        const tr = document.createElement('tr');
        tr.classList.add('hover:bg-gray-50', 'transition-colors');

        // Coluna Assistente
        const tdAssistente = document.createElement('td');
        tdAssistente.classList.add('px-6', 'py-4', 'whitespace-nowrap', 'text-sm', 'text-gray-900');
        tdAssistente.textContent = vida.assistente;

        // Coluna Operadora
        const tdOperadora = document.createElement('td');
        tdOperadora.classList.add('px-6', 'py-4', 'whitespace-nowrap', 'text-sm', 'text-gray-900');
        tdOperadora.textContent = vida.operadora;

        // Coluna Equipe
        const tdEquipe = document.createElement('td');
        tdEquipe.classList.add('px-6', 'py-4', 'whitespace-nowrap', 'text-sm', 'text-gray-900');
        tdEquipe.textContent = vida.equipe;

        // Coluna Meta (editável)
        const tdMeta = document.createElement('td');
        tdMeta.classList.add('px-6', 'py-4', 'whitespace-nowrap');

        const inputMeta = document.createElement('input');
        inputMeta.type = 'number';
        inputMeta.value = vida.metas_vidas;
        inputMeta.classList.add(
            'w-24',
            'px-3',
            'py-2',
            'border',
            'rounded-md',
            'focus:ring-indigo-500',
            'focus:border-indigo-500',
            'text-sm'
        );
        tdMeta.appendChild(inputMeta);

        // Coluna Ações
        const tdAcoes = document.createElement('td');
        tdAcoes.classList.add('px-6', 'py-4', 'whitespace-nowrap', 'text-sm', 'text-gray-500');

        const btnSalvar = document.createElement('button');
        btnSalvar.innerHTML = '<i class="fas fa-save mr-2"></i>Salvar';
        btnSalvar.classList.add(
            'inline-flex',
            'items-center',
            'px-3',
            'py-2',
            'border',
            'border-transparent',
            'text-sm',
            'font-medium',
            'rounded-md',
            'text-white',
            'bg-indigo-600',
            'hover:bg-indigo-700',
            'focus:outline-none',
            'focus:ring-2',
            'focus:ring-offset-2',
            'focus:ring-indigo-500',
            'transition-colors'
        );

        btnSalvar.addEventListener('click', () => {
            updateVidaAssistente(vida.assistente, vida.operadora, inputMeta.value, btnSalvar);
        });

        tdAcoes.appendChild(btnSalvar);

        tr.appendChild(tdAssistente);
        tr.appendChild(tdOperadora);
        tr.appendChild(tdEquipe);
        tr.appendChild(tdMeta);
        tr.appendChild(tdAcoes);

        tbody.appendChild(tr);
    });
}

// Função para atualizar vida operadora
function updateVidaOperadora(operadora, valor, btnSalvar) {
    const originalContent = btnSalvar.innerHTML;
    btnSalvar.disabled = true;
    btnSalvar.innerHTML = '<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>';
    btnSalvar.classList.add('opacity-75');

    fetch(`/api/update_metas_vidas_operadora/${operadora}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ valor })
    })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }

            btnSalvar.innerHTML = '<i class="fas fa-check mr-2"></i>Salvo';
            btnSalvar.classList.remove('bg-indigo-600', 'hover:bg-indigo-700');
            btnSalvar.classList.add('bg-green-600', 'hover:bg-green-700');

            setTimeout(() => {
                btnSalvar.innerHTML = originalContent;
                btnSalvar.classList.remove('bg-green-600', 'hover:bg-green-700');
                btnSalvar.classList.add('bg-indigo-600', 'hover:bg-indigo-700');
                btnSalvar.disabled = false;
                btnSalvar.classList.remove('opacity-75');
            }, 2000);
        })
        .catch(error => {
            console.error('Erro ao atualizar vida operadora:', error);

            btnSalvar.innerHTML = '<i class="fas fa-times mr-2"></i>Erro';
            btnSalvar.classList.remove('bg-indigo-600', 'hover:bg-indigo-700');
            btnSalvar.classList.add('bg-red-600', 'hover:bg-red-700');

            setTimeout(() => {
                btnSalvar.innerHTML = originalContent;
                btnSalvar.classList.remove('bg-red-600', 'hover:bg-red-700');
                btnSalvar.classList.add('bg-indigo-600', 'hover:bg-indigo-700');
                btnSalvar.disabled = false;
                btnSalvar.classList.remove('opacity-75');
            }, 2000);
        });
}

// Função para atualizar vida assistente
function updateVidaAssistente(assistente, operadora, valor, btnSalvar) {
    const originalContent = btnSalvar.innerHTML;
    btnSalvar.disabled = true;
    btnSalvar.innerHTML = '<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>';
    btnSalvar.classList.add('opacity-75');

    fetch(`/api/update_metas_vidas_assistente/${assistente}/${operadora}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ valor })
    })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }

            btnSalvar.innerHTML = '<i class="fas fa-check mr-2"></i>Salvo';
            btnSalvar.classList.remove('bg-indigo-600', 'hover:bg-indigo-700');
            btnSalvar.classList.add('bg-green-600', 'hover:bg-green-700');

            setTimeout(() => {
                btnSalvar.innerHTML = originalContent;
                btnSalvar.classList.remove('bg-green-600', 'hover:bg-green-700');
                btnSalvar.classList.add('bg-indigo-600', 'hover:bg-indigo-700');
                btnSalvar.disabled = false;
                btnSalvar.classList.remove('opacity-75');
            }, 2000);
        })
        .catch(error => {
            console.error('Erro ao atualizar vida assistente:', error);

            btnSalvar.innerHTML = '<i class="fas fa-times mr-2"></i>Erro';
            btnSalvar.classList.remove('bg-indigo-600', 'hover:bg-indigo-700');
            btnSalvar.classList.add('bg-red-600', 'hover:bg-red-700');

            setTimeout(() => {
                btnSalvar.innerHTML = originalContent;
                btnSalvar.classList.remove('bg-red-600', 'hover:bg-red-700');
                btnSalvar.classList.add('bg-indigo-600', 'hover:bg-indigo-700');
                btnSalvar.disabled = false;
                btnSalvar.classList.remove('opacity-75');
            }, 2000);
        });
}
