// Listener para o link de benefícios
document.getElementById('beneficiosLink').addEventListener('click', function(event) {
    event.preventDefault();
    fetch('/meus-beneficios')
        .then(response => response.text())
        .then(data => {
            const mainContent = document.getElementById('mainContent');
            mainContent.innerHTML = data;
            mainContent.classList.add('beneficios-page');

            // Reativamos as funções de cálculo de férias e toggleTable após o conteúdo ser carregado
            attachFormEventListeners();
        })
        .catch(error => console.error('Erro ao carregar o conteúdo de benefícios:', error));
});

// Função para anexar os event listeners após o conteúdo ser carregado
function attachFormEventListeners() {
    // Função para limpar os resultados do cálculo de férias
    const limparBtn = document.querySelector('[name="limpar"]'); // Certifique-se de que o botão de limpar está sendo selecionado corretamente
    if (limparBtn) {
        limparBtn.addEventListener('click', function() {
            document.getElementById('calculoFeriasForm').reset(); // Limpa os inputs do formulário
            const resultado = document.getElementById('resultado');
            if (resultado) {
                resultado.innerHTML = ""; // Limpa o resultado exibido
            }
        });
    }

    // Função para mostrar/ocultar a tabela de coparticipação
    const toggleTableBtn = document.getElementById('toggleTableBtn');
    if (toggleTableBtn) {
        toggleTableBtn.addEventListener('click', function() {
            const table = document.getElementById('coparticipation-table');
            if (table.style.display === "none") {
                table.style.display = "block";
            } else {
                table.style.display = "none";
            }
        });
    }

    // Função para lidar com a submissão do formulário de cálculo de férias
    const calculoFeriasForm = document.getElementById('calculoFeriasForm');
    if (calculoFeriasForm) {
        calculoFeriasForm.addEventListener('submit', function(event) {
            event.preventDefault();  // Previne o comportamento padrão do formulário

            const formData = new FormData(calculoFeriasForm);

            fetch('/calcular-ferias', {  // Supondo que a rota seja '/calcular-ferias'
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultado = `
                    <h3>Resultado</h3>
                    <p><strong>Valor Total das Férias:</strong> ${data.valor_ferias}</p>
                    <p><strong>Adicional 1/3 sobre Férias:</strong> ${data.adicional_terco}</p>
                    <p><strong>13º Salário Proporcional:</strong> ${data.valor_13_proporcional}</p>
                    <p><strong>INSS sobre Férias:</strong> ${data.inss}</p>
                    <p><strong>Valor Líquido:</strong> ${data.valor_liquido}</p>
                `;
                document.getElementById('resultado').innerHTML = resultado;
            })
            .catch(error => console.error('Erro ao calcular férias:', error));
        });
    }
}

// Função para formatar o valor como moeda
function formatCurrency(value) {
    value = value.replace(/\D/g, ''); // Remove todos os caracteres não numéricos
    value = (value / 100).toFixed(2); // Divide por 100 para trabalhar com centavos e arredonda
    value = value.replace('.', ','); // Troca o ponto por vírgula para a notação brasileira
    return value.replace(/\B(?=(\d{3})+(?!\d))/g, "."); // Adiciona pontos como separadores de milhar
}

// Adicionar event listener para formatar o valor do campo salário
document.getElementById('salario').addEventListener('input', function (e) {
    const cursorPosition = this.selectionStart; // Guarda a posição do cursor
    const originalLength = this.value.length;   // Guarda o comprimento original do valor

    let value = this.value.replace(/[^0-9,]+/g,""); // Remove tudo exceto números e vírgula
    this.value = "R$ " + formatCurrency(value); // Formata o valor como moeda

    const newLength = this.value.length; // Obtém o novo comprimento após a formatação
    this.selectionEnd = cursorPosition + (newLength - originalLength); // Reposiciona o cursor corretamente
});

// Função para remover o símbolo de moeda e formatação antes de enviar o formulário
document.getElementById('calculoFeriasForm').addEventListener('submit', function () {
    const salarioInput = document.getElementById('salario');
    salarioInput.value = salarioInput.value.replace(/[^0-9,]+/g,"").replace(",", "."); // Remove tudo exceto números e ponto
});

// Reativar os eventos ao carregar o conteúdo
document.addEventListener('DOMContentLoaded', attachFormEventListeners);
