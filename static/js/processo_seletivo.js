// Função para abrir o modal com os detalhes do processo seletivo
function openModal(button) {
    // Exibe a tela de carregamento
    document.getElementById("loadingScreen").style.display = "flex";
    
    const seletivoId = button.getAttribute('data-id');

    fetch(`/rh/processos-seletivos-internos/${seletivoId}`)
        .then(response => response.json())
        .then(seletivo => {
            if (seletivo.error) {
                alert(seletivo.error);
            } else {
                document.getElementById("modal-titulo").innerText = seletivo.titulo;
                document.getElementById("modal-requisitos").innerText = seletivo.requisitos;
                document.getElementById("modal-descricao").innerHTML = seletivo.descricao;
                document.getElementById("modal-data-inicio").innerText = seletivo.data_inicio;
                document.getElementById("modal-data-final").innerText = seletivo.data_fim;
                document.getElementById("id_seletivo").value = seletivoId;

                var myModal = new bootstrap.Modal(document.getElementById('modal'), {});
                myModal.show();
            }
        })
        .catch(error => {
            console.error('Erro ao buscar detalhes do processo seletivo:', error);
        })
        .finally(() => {
            // Esconde a tela de carregamento
            document.getElementById("loadingScreen").style.display = "none";
        });
}

// Função para enviar o formulário via JavaScript
function submitForm() {
    // Exibe a tela de carregamento
    document.getElementById("loadingScreen").style.display = "flex";

    const form = document.getElementById("seletivo-form");
    const formData = new FormData(form);
    const seletivoId = document.getElementById("id_seletivo").value;

    fetch(`/rh/processos-seletivos-internos/${seletivoId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('Inscrição realizada com sucesso!');
            // Fecha o modal explicitamente
            var modalElement = document.getElementById('modal');
            var modalInstance = bootstrap.Modal.getInstance(modalElement);
            modalInstance.hide(); // Fecha o modal
            
            // Opcional: Se quiser forçar o refresh da página depois que o modal fechar
            // window.location.reload();
        } else {
            alert('Erro ao realizar a inscrição: ' + result.error);
        }
    })
    .catch(error => {
        console.error('Erro ao enviar formulário:', error);
    })
    .finally(() => {
        // Esconde a tela de carregamento
        document.getElementById("loadingScreen").style.display = "none";
    });
}
