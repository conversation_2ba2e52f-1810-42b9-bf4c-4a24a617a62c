document.getElementById('upload-form').addEventListener('submit', function(event) {
    event.preventDefault();
    var form = event.target;
    var formData = new FormData(form);

    showLoadingScreen();  // Mostra a tela de carregamento

    fetch('/upload', {
        method: 'POST',
        body: formData
    }).then(response => {
        hideLoadingScreen();  // Esconde a tela de carregamento
        if (response.ok) {
            window.location.href = '/upload-tabela-comissoes';
        } else {
            alert('Erro ao fazer upload. Tente novamente.');
        }
    }).catch(error => {
        hideLoadingScreen();  // Esconde a tela de carregamento em caso de erro
        console.error('Erro:', error);
        alert('Erro ao fazer upload. Tente novamente.');
    });
});

function showLoadingScreen() {
    var loadingScreen = document.getElementById("loadingScreen");
    if (loadingScreen) {
        loadingScreen.style.display = 'flex';
    }
}

function hideLoadingScreen() {
    var loadingScreen = document.getElementById("loadingScreen");
    if (loadingScreen) {
        loadingScreen.style.display = 'none';
    }
}
