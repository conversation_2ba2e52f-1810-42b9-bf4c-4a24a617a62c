document.addEventListener('DOMContentLoaded', function () {
    var editUserForm = document.getElementById('updateUserForm');

    if (editUserForm) {
        editUserForm.addEventListener('submit', function (e) {
            e.preventDefault();

            if (!confirm('Tem certeza que deseja atualizar este usuário?')) {
                return;
            }

            var dados = new FormData(editUserForm);

            // Verificar se o checkbox "ativo" está marcado ou não e definir o valor correspondente
            if (document.getElementById('ativo').checked) {
                dados.set('ativo', 'on');  // Valor quando marcado
            } else {
                dados.set('ativo', 'off'); // Valor quando desmarcado
            }

            // Garantir que o campo editEquipe seja enviado, mesmo quando vazio
            if (!dados.has('equipe_id') || dados.get('equipe_id') === '') {
                dados.set('equipe_id', '');
            }

            atualizarUsuario(dados);
        });
    }

    // Toggle password visibility
    function ShowPswd() {
        var inputPass = document.getElementById('password')
        var btnShowPass = document.getElementById('btn-senha')

        if (inputPass.type === 'password') {
            inputPass.setAttribute('type', 'text')
            btnShowPass.classList.replace('bi-eye-fill', 'bi-eye-slash-fill')
        }
        else {
            inputPass.setAttribute('type', 'password')
            btnShowPass.classList.replace('bi-eye-slash-fill', 'bi-eye-fill')
        }
    }

    function atualizarUsuario(dados) {
        fetch('/atualizar_usuario', {
            method: 'POST',
            body: dados,
            credentials: 'include'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro na atualização do usuário');
                }
                return response.json();
            })
            .then(data => {
                alert(data.message);
                window.location.reload();
            })
            .catch(error => {
                console.error('Erro na atualização:', error);
                alert('Erro ao processar a solicitação.');
            });
    }

    var deleteUserButton = document.getElementById('deleteUserButton');
    if (deleteUserButton) {
        deleteUserButton.addEventListener('click', function () {
            var userId = document.getElementById('editUserId').value;
            excluirUsuario(userId);
        });
    }

    function excluirUsuario(userId) {
        if (!confirm('Tem certeza que deseja excluir este usuário?')) {
            return;
        }

        fetch(`/excluir_user/${userId}`, {
            method: 'DELETE',
            credentials: 'include' // Necessário para incluir cookies em chamadas cross-site
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro na exclusão do usuário');
                }
                return response.json();
            })
            .then(data => {
                alert(data.message); // Exibe um alerta com a mensagem de sucesso ou erro
                window.location = '/admin'; // Redireciona para a página de administração após a exclusão
            })
            .catch(error => {
                console.error('Erro na exclusão:', error);
                alert('Erro ao processar a solicitação de exclusão.'); // Se ocorrer um erro, o usuário será notificado por outro alerta.
            });
    }
});
