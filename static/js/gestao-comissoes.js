function carregarComissoes() {
    axios.get('/api/comissoes', {
        params: {
            DataTipo: document.getElementById('DataTipo').value,
            Visualizacao: document.getElementById('Visualizacao').value,
            TextoDeProcura: document.getElementById('TextoDeProcura').value,
            DataInicio: document.getElementById('DataInicio').value,
            DataFinal: document.getElementById('DataFinal').value,
            Pagina: 1,  // Defina a página inicial
            RegistrosPorPagina: 100  // Limite o número de registros por página
        }
    })
    .then(function (response) {
        console.log("Resposta da API:", response.data);  // Verifica a resposta no console

        // Verifica se a `result` e `collection` existem na resposta da API
        const result = response.data.result;
        if (!result || !Array.isArray(result.collection) || result.collection.length === 0) {
            alert('Nenhuma comissão encontrada para os filtros aplicados.');
            return;
        }

        // Obtém o corpo da tabela e limpa os dados atuais
        let tableBody = document.getElementById('comissaoTableBody');
        tableBody.innerHTML = '';

        const comissoes = result.collection;

        // Itera sobre cada item da coleção e cria uma nova linha de tabela
        comissoes.forEach(comissao => {
            let row = `<tr>
                <td>${comissao.corretor || 'N/A'}</td>
                <td>${comissao.assistente || 'N/A'}</td>
                <td>${comissao.assistenteOriginal || 'N/A'}</td>
                <td>${comissao.idProducao || 'N/A'}</td>
                <td>${comissao.proposta || 'N/A'}</td>
                <td>${comissao.periodo || 'N/A'}</td>
                <td>${comissao.modalidade || 'N/A'}</td>
                <td>${comissao.conferente || 'N/A'}</td>
                <td>${comissao.segurado || 'N/A'}</td>
                <td>${comissao.telefoneCelular || 'N/A'}</td>
                <td>${comissao.telefoneResidencial || 'N/A'}</td>
                <td>${comissao.telefoneComercial || 'N/A'}</td>
                <td>${comissao.email || 'N/A'}</td>
                <td>${comissao.dtVencto ? new Date(comissao.dtVencto).toLocaleDateString() : 'N/A'}</td>
                <td>${comissao.dtVigencia ? new Date(comissao.dtVigencia).toLocaleDateString() : 'N/A'}</td>
                <td>${comissao.dtProducao ? new Date(comissao.dtProducao).toLocaleDateString() : 'N/A'}</td>
                <td>${comissao.dtAssinatura ? new Date(comissao.dtAssinatura).toLocaleDateString() : 'N/A'}</td>
                <td>${comissao.dtCadastro ? new Date(comissao.dtCadastro).toLocaleDateString() : 'N/A'}</td>
                <td>${comissao.loja || 'N/A'}</td>
                <td>${comissao.documento || 'N/A'}</td>
                <td>R$ ${comissao.vlParcela ? comissao.vlParcela.toFixed(2) : '0.00'}</td>
                <td>${comissao.comissaoTotal ? (comissao.comissaoTotal * 100).toFixed(2) + '%' : '0.00%'}</td>
                <td>${comissao.comissaoCorretor ? (comissao.comissaoCorretor * 100).toFixed(2) + '%' : '0.00%'}</td>
                <td>${comissao.status || 'N/A'}</td>
                <td><button class="btn btn-info btn-sm" onclick="verDetalhes(${comissao.idProducao})">Detalhes</button></td>
            </tr>`;

            tableBody.innerHTML += row;
        });
    })
    .catch(function (error) {
        console.error('Erro ao carregar dados da API:', error);
    });
}

// Função para abrir detalhes da comissão em um modal ou nova página
function verDetalhes(idProducao) {
    // Aqui você pode implementar a lógica para abrir os detalhes da comissão
    console.log('Exibir detalhes da produção:', idProducao);
}

// Adiciona o evento ao botão de buscar
document.getElementById('buscarComissoes').addEventListener('click', carregarComissoes);
