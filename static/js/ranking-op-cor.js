$(document).ready(function () {
    console.log("Initializing DataTable...");

    // Função para atualizar a legenda de última atualização
    function updateLastExecutionTime() {
        $.ajax({
            url: "/api/last-execution",
            method: "GET",
            success: function (response) {
                if (response.status === 'success' && response.data) {
                    $('#last-update').html(`Última atualização: ${response.data.data}`);
                }
            },
            error: function (xhr, status, error) {
                console.log("Error fetching last execution time: ", error);
            }
        });
    }

    // Atualiza a legenda quando a página carrega
    updateLastExecutionTime();

    function loadTables() {
        $.ajax({
            url: "/api/ranking_op_cor",
            method: "GET",
            success: function (data) {
                console.log("Ranking data loaded: ", data);

                $('#tables-container').empty();

                var operadorasOrdenadas = Object.keys(data);

                operadorasOrdenadas.forEach(function (operadora, index) {
                    var tableId = 'table-' + index;
                    var rowsHtml = '';
                    var items = data[operadora];

                    items.forEach(item => {
                        let valor = parseFloat(item.vltotal.replace(',', ''));
                        rowsHtml += `
                            <tr>
                                <td>${item.assistente}</td>
                                <td class="text-left">${item.corretor}</td>
                                <td class="text-accounting">R$ ${valor.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                            </tr>
                        `;
                    });

                    var tableHtml = `
                        <div class="col-md-8 mb-4 table-container" style="display: flex; justify-content: center;">
                            <table id="${tableId}" class="display" style="width:100%">
                                <thead class="ranking-header">
                                    <tr><th colspan="3" class="text-center" style="text-align: center;">${operadora}</th></tr>
                                    <tr>
                                        <th class="ranking-column-header text-center" style="text-align: center; width: 850px;">Assistente</th>
                                        <th class="ranking-column-header text-center" style="text-align: center; width: 950px">Corretor</th>
                                        <th class="ranking-column-header text-center" style="text-align: center; width: 170px;">Total</th>
                                    </tr>
                                </thead>
                                <tbody class="ranking-body">
                                    ${rowsHtml}
                                </tbody>
                                <tfoot class="ranking-footer">
                                    <tr>
                                        <th class="ranking-column-footer"></th>
                                        <th class="ranking-column-footer text-center" style="text-align: center;">Total Geral</th>
                                        <th class="ranking-column-footer text-accounting" style="text-align: center;">
                                            R$ ${items.reduce((sum, item) => sum + parseFloat(item.vltotal.replace(',', '')), 0).toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                        </th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    `;
                    $('#tables-container').append(tableHtml);

                    $('#' + tableId).DataTable({
                        paging: false,
                        searching: false,
                        info: false,
                        dom: 'Bfrtip',
                        buttons: [],
                        ordering: false
                    });

                    // Estilos das linhas do corpo da tabela
                    $('#' + tableId + ' tbody tr').css('background-color', '#013977').css('color', 'white');
                    $('#' + tableId + ' tbody tr:nth-child(even)').css('background-color', '#f9f9f9').css('color', 'black');
                    $('#' + tableId + ' tbody tr:hover').css('background-color', '#f1f1f1').css('color', 'black');
                    $('#' + tableId + ' tbody tr').css('border-bottom', '1px solid white');

                    // Estilos do cabeçalho da tabela
                    $('#' + tableId + ' thead th').css('background-color', '#F2753D').css('color', 'white');
                    $('#' + tableId + ' thead th[colspan="3"]').css({
                        'text-align': 'center', // Centraliza o texto do cabeçalho da operadora
                        'white-space': 'nowrap',
                        'overflow': 'hidden',
                        'text-overflow': 'ellipsis',
                        'margin-right': '10px'
                    });

                    // Estilos do rodapé da tabela
                    $('#' + tableId + ' tfoot th').css('background-color', '#F2753D').css('color', 'white');
                });
            },
            error: function (xhr, status, error) {
                console.log("Error fetching ranking data: ", xhr.responseText);
            }
        });
    }

    loadTables();
});
