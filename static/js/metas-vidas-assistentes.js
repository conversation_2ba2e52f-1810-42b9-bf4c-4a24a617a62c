$(document).ready(function () {
    let table;
    let periodoSelecionado = '';
    let assistentesSelecionados = [];
    let equipesSelecionadas = [];
    let operadorasSelecionadas = [];
    let dadosCompletos = [];

    // Função para converter formato MM/YYYY para YYYY-MM-01
    function converterFormatoPeriodo(periodo) {
        // Verifica se o período está no formato MM/YYYY
        if (periodo && periodo.includes('/')) {
            const [mes, ano] = periodo.split('/');
            // Retorna no formato YYYY-MM-01
            return `${ano}-${mes.padStart(2, '0')}-01`;
        }
        return periodo; // Retorna sem alteração se já estiver no formato correto
    }

    // Função para atualizar a legenda de última atualização
    function updateLastExecutionTime() {
        $.ajax({
            url: "/api/last-execution",
            method: "GET",
            success: function (response) {
                if (response.status === 'success' && response.data) {
                    $('#last-update').html(`Última atualização: ${response.data.data}`);
                }
            },
            error: function (xhr, status, error) {
                console.log("Error fetching last execution time: ", error);
            }
        });
    }

    // Atualiza a legenda quando a página carrega
    updateLastExecutionTime();

    // Cria div de debug
    const debugContainer = $('<div>')
        .attr('id', 'debug-container')
        .addClass('mt-4 p-3 border')
        .css('display', 'none')
        .appendTo('.ranking-container');

    // Inicializa os seletores
    function initSeletores() {
        console.log("Seletores serão inicializados quando os dados forem carregados");
    }

    // Função principal assíncrona
    async function inicializarMetasVidasAssistentes() {
        // Função para buscar os meses/anos do endpoint
        async function buscarMesesAnos() {
            try {
                const response = await fetch('/api/mesesanos-meta-vidas-assistente-api');
                const data = await response.json();

                if (data.status === 'success' && data.data.length > 0) {
                    // Retorna a lista completa de períodos
                    return data.data;
                }
                throw new Error('Nenhum mês/ano encontrado');
            } catch (error) {
                console.error('Erro ao buscar meses/anos:', error);
                return [];
            }
        }

        // Busca os períodos disponíveis
        const periodos = await buscarMesesAnos();
        if (periodos.length > 0) {
            // Seleciona o primeiro período por padrão (mais recente)
            periodoSelecionado = periodos[0];

            // Inicializa o seletor de períodos
            atualizarSeletorPeriodos(periodos);

            // Carrega os dados para o período selecionado
            carregarDados();
        } else {
            console.error("Nenhum período disponível");
            $('#metasAssistentesTable tbody').html('<tr><td colspan="5" class="text-center">Nenhum período disponível</td></tr>');
        }

        // Função para obter dados da API
        function carregarDados() {
            console.log("Carregando dados da API...");

            // Exibe mensagem de carregamento
            $('#metasAssistentesTable tbody').html('<tr><td colspan="5" class="text-center">Carregando dados...</td></tr>');

            // Limpa o debug
            debugContainer.html('<h4>Debug de Dados</h4><div id="raw-response"></div>');

            // Converte o formato do período selecionado de MM/YYYY para YYYY-MM-01
            const periodoFormatado = converterFormatoPeriodo(periodoSelecionado);
            console.log("Período formatado para API:", periodoFormatado);

            // Faz a requisição para a API
            $.ajax({
                url: '/api/metas-vidas-assistentes',
                type: 'GET',
                data: { mesano: periodoFormatado },
                dataType: 'json',
                success: function (response) {
                    console.log("Resposta da API:", response);

                    if (response.status === 'success' && response.data) {
                        processarDados(response.data);
                    } else {
                        console.error("Erro na resposta da API:", response.message || "Formato de resposta inválido");
                        $('#metasAssistentesTable tbody').html('<tr><td colspan="5" class="text-center">Erro ao carregar dados: ' +
                            (response.message || "Formato de resposta inválido") + '</td></tr>');
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Erro na requisição:", error);
                    $('#metasAssistentesTable tbody').html('<tr><td colspan="5" class="text-center">Erro na requisição: ' + error + '</td></tr>');
                }
            });
        }

        // Função para processar os dados obtidos
        function processarDados(dados) {
            // Mostra resposta bruta para debug
            $('#raw-response').html(
                '<h5>Dados recebidos:</h5>' +
                '<pre>' + JSON.stringify(dados, null, 2) + '</pre>'
            );

            dadosCompletos = dados.map(item => {
                // Mapeia os nomes das colunas retornadas pela API para o formato esperado
                const formattedItem = {
                    assistente: item['assistente'] || "Não informado",
                    operadora: item['operadora'] || "Desconhecida",
                    meta: parseInt(item['meta_vidas']) || 0,
                    vidas: parseInt(item['vidas_vendidas']) || 0,
                    periodo: item['mesano'] || "",
                    equipe: item['equipe'] || "Não informada",
                    percentual: parseFloat(item['diferenca_percentual'] || 0) * 100
                };

                return formattedItem;
            });

            // Exibe exemplo de item após ajuste
            if (dadosCompletos.length > 0) {
                $('#debug-container').append(
                    '<h5>Exemplo após ajuste:</h5>' +
                    '<pre>' + JSON.stringify(dadosCompletos[0], null, 2) + '</pre>'
                );
            }

            // Extrai valores únicos para os filtros
            const assistentesUnicos = [...new Set(dadosCompletos.map(item => item.assistente))];
            const equipesUnicas = [...new Set(dadosCompletos.map(item => item.equipe))];
            const operadorasUnicas = [...new Set(dadosCompletos.map(item => item.operadora))];

            // Ordena os outros filtros por ordem alfabética
            assistentesUnicos.sort();
            equipesUnicas.sort();
            operadorasUnicas.sort();

            // Atualiza os seletores com os valores disponíveis
            atualizarSeletorAssistentes(assistentesUnicos);
            atualizarSeletorEquipes(equipesUnicas);
            atualizarSeletorOperadoras(operadorasUnicas);

            // Atualiza a tabela com os dados
            atualizarTabela();
        }

        // Função para atualizar o seletor de períodos
        function atualizarSeletorPeriodos(periodos) {
            // Limpa opções atuais
            $('#monthYearPicker').empty();

            // Adiciona as opções com os períodos disponíveis
            periodos.forEach(periodo => {
                $('#monthYearPicker').append($('<option>', {
                    value: periodo,
                    text: periodo
                }));
            });

            // Define o primeiro período como selecionado (mais recente)
            $('#monthYearPicker').val(periodos[0]);

            // Adiciona o evento de mudança
            $('#monthYearPicker').off('change').on('change', function () {
                periodoSelecionado = $(this).val();
                carregarDados();
            });
        }

        // Função para atualizar o seletor de assistentes
        function atualizarSeletorAssistentes(assistentes) {
            // Limpa opções atuais
            $('#assistentePicker').empty();

            // Adiciona as opções com os assistentes disponíveis
            assistentes.forEach(assistente => {
                $('#assistentePicker').append($('<option>', {
                    value: assistente,
                    text: assistente
                }));
            });

            // Destroy o multiselect existente se houver
            if ($('#assistentePicker').hasClass('multiselect-initialized')) {
                $('#assistentePicker').multiselect('destroy');
            }

            // Atualiza o multiselect com a configuração correta
            $('#assistentePicker').multiselect({
                includeSelectAllOption: true,
                selectAllText: 'Todos os assistentes',
                nonSelectedText: 'Selecione os assistentes',
                nSelectedText: 'assistentes selecionados',
                allSelectedText: 'Todos os assistentes selecionados',
                buttonClass: 'btn btn-outline-primary',
                maxHeight: 300,
                enableFiltering: true,
                filterPlaceholder: 'Buscar assistente',
                enableCaseInsensitiveFiltering: true,
                buttonText: function (options, select) {
                    if (options.length === 0) {
                        return 'Selecione os assistentes';
                    }
                    else if (options.length > 1) {
                        return options.length + ' assistentes selecionados';
                    }
                    else {
                        // Exibe o texto do assistente selecionado
                        return $(options[0]).text();
                    }
                }
            }).addClass('multiselect-initialized');

            // Adiciona o evento de mudança
            $('#assistentePicker').off('change').on('change', function () {
                assistentesSelecionados = $(this).val() || [];
                atualizarTabela();
            });

            // Inicialmente não seleciona nenhum assistente
            assistentesSelecionados = [];
            $('#assistentePicker').val([]);
            $('#assistentePicker').multiselect('refresh');
        }

        // Função para atualizar o seletor de equipes
        function atualizarSeletorEquipes(equipes) {
            // Limpa opções atuais
            $('#equipePicker').empty();

            // Adiciona as opções com as equipes disponíveis
            equipes.forEach(equipe => {
                $('#equipePicker').append($('<option>', {
                    value: equipe,
                    text: equipe
                }));
            });

            // Destroy o multiselect existente se houver
            if ($('#equipePicker').hasClass('multiselect-initialized')) {
                $('#equipePicker').multiselect('destroy');
            }

            // Atualiza o multiselect com a configuração correta
            $('#equipePicker').multiselect({
                includeSelectAllOption: true,
                selectAllText: 'Todas as equipes',
                nonSelectedText: 'Selecione as equipes',
                nSelectedText: 'equipes selecionadas',
                allSelectedText: 'Todas as equipes selecionadas',
                buttonClass: 'btn btn-outline-primary',
                maxHeight: 300,
                enableFiltering: true,
                filterPlaceholder: 'Buscar equipe',
                enableCaseInsensitiveFiltering: true,
                buttonText: function (options, select) {
                    if (options.length === 0) {
                        return 'Selecione as equipes';
                    }
                    else if (options.length > 1) {
                        return options.length + ' equipes selecionadas';
                    }
                    else {
                        // Exibe o texto da equipe selecionada
                        return $(options[0]).text();
                    }
                }
            }).addClass('multiselect-initialized');

            // Adiciona o evento de mudança
            $('#equipePicker').off('change').on('change', function () {
                equipesSelecionadas = $(this).val() || [];
                atualizarTabela();
            });

            // Inicialmente não seleciona nenhuma equipe
            equipesSelecionadas = [];
            $('#equipePicker').val([]);
            $('#equipePicker').multiselect('refresh');
        }

        // Função para atualizar o seletor de operadoras
        function atualizarSeletorOperadoras(operadoras) {
            // Limpa opções atuais
            $('#operadoraPicker').empty();

            // Adiciona as opções com as operadoras disponíveis
            operadoras.forEach(operadora => {
                $('#operadoraPicker').append($('<option>', {
                    value: operadora,
                    text: operadora
                }));
            });

            // Destroy o multiselect existente se houver
            if ($('#operadoraPicker').hasClass('multiselect-initialized')) {
                $('#operadoraPicker').multiselect('destroy');
            }

            // Atualiza o multiselect com a configuração correta
            $('#operadoraPicker').multiselect({
                includeSelectAllOption: true,
                selectAllText: 'Todas as operadoras',
                nonSelectedText: 'Selecione as operadoras',
                nSelectedText: 'operadoras selecionadas',
                allSelectedText: 'Todas as operadoras selecionadas',
                buttonClass: 'btn btn-outline-primary',
                maxHeight: 300,
                enableFiltering: true,
                filterPlaceholder: 'Buscar operadora',
                enableCaseInsensitiveFiltering: true,
                buttonText: function (options, select) {
                    if (options.length === 0) {
                        return 'Selecione as operadoras';
                    }
                    else if (options.length > 1) {
                        return options.length + ' operadoras selecionadas';
                    }
                    else {
                        // Exibe o texto da operadora selecionada
                        return $(options[0]).text();
                    }
                }
            }).addClass('multiselect-initialized');

            // Adiciona o evento de mudança
            $('#operadoraPicker').off('change').on('change', function () {
                operadorasSelecionadas = $(this).val() || [];
                atualizarTabela();
            });

            // Inicialmente não seleciona nenhuma operadora
            operadorasSelecionadas = [];
            $('#operadoraPicker').val([]);
            $('#operadoraPicker').multiselect('refresh');
        }

        // Inicializa a tabela DataTable
        function initDataTable(dados) {
            console.log("Inicializando tabela com dados:", dados);

            if (table) {
                table.destroy();
            }

            if (!dados || dados.length === 0) {
                $('#metasAssistentesTable tbody').html('<tr><td colspan="5" class="text-center">Nenhum dado disponível para os filtros selecionados</td></tr>');
                return;
            }

            table = $('#metasAssistentesTable').DataTable({
                data: dados,
                columns: [
                    { data: 'assistente' },
                    { data: 'operadora' },
                    { data: 'meta', render: $.fn.dataTable.render.number('.', ',', 0) },
                    { data: 'vidas', render: $.fn.dataTable.render.number('.', ',', 0) },
                    {
                        data: 'percentual',
                        render: function (data, type, row) {
                            // Calcula o percentual baseado nas vidas vendidas dividido pela meta
                            const percentual = row.meta > 0 ? (row.vidas / row.meta) * 100 : 0;
                            const percentValue = percentual.toFixed(2) + '%';

                            // Verde se atingiu 100% ou mais da meta
                            // Amarelo se atingiu entre 80% e 99.99% da meta
                            // Vermelho se atingiu menos de 80% da meta
                            if (percentual >= 100) {
                                return '<span style="color: #28a745; font-weight: bold;">' + percentValue + '</span>';
                            } else if (percentual >= 80) {
                                return '<span style="color: #ffc107; font-weight: bold;">' + percentValue + '</span>';
                            } else {
                                return '<span style="color: #dc3545; font-weight: bold;">' + percentValue + '</span>';
                            }
                        }
                    }
                ],
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Portuguese-Brasil.json'
                },
                dom: 'Bfrtip',
                buttons: [
                    'copy', 'csv', 'excel', 'pdf', 'print'
                ],
                order: [[0, 'asc']], // Ordena por assistente (coluna 0) em ordem crescente
                pageLength: 25,
                // Configura o footer para mostrar totais
                footerCallback: function (row, data, start, end, display) {
                    const api = this.api();

                    // Remove formatação para calcular
                    const intVal = function (i) {
                        return typeof i === 'string' ?
                            i.replace(/[\$,]/g, '') * 1 :
                            typeof i === 'number' ? i : 0;
                    };

                    // Total meta
                    const totalMeta = api
                        .column(2)
                        .data()
                        .reduce((a, b) => intVal(a) + intVal(b), 0);

                    // Total vidas
                    const totalVidas = api
                        .column(3)
                        .data()
                        .reduce((a, b) => intVal(a) + intVal(b), 0);

                    // Calcula percentual total
                    const percentualTotal = totalMeta > 0 ?
                        (totalVidas / totalMeta) * 100 : 0;

                    // Status total pelo percentual
                    let statusClass;
                    if (percentualTotal >= 100) {
                        statusClass = 'style="color: #28a745; font-weight: bold;"';
                    } else if (percentualTotal >= 80) {
                        statusClass = 'style="color: #ffc107; font-weight: bold;"';
                    } else {
                        statusClass = 'style="color: #dc3545; font-weight: bold;"';
                    }

                    // Atualiza o footer
                    $(api.column(0).footer()).html('Total Geral');
                    $(api.column(1).footer()).html('');
                    $(api.column(2).footer()).html(totalMeta.toLocaleString('pt-BR'));
                    $(api.column(3).footer()).html(totalVidas.toLocaleString('pt-BR'));
                    $(api.column(4).footer()).html('<span ' + statusClass + '>' + percentualTotal.toFixed(2) + '%</span>');
                }
            });

            // Move os botões para o container
            $('.dt-buttons').detach().appendTo('#buttons-container');
        }

        // Função para atualizar a tabela com base nos filtros
        function atualizarTabela() {
            console.log("Atualizando tabela com filtros:", {
                periodo: periodoSelecionado,
                assistentes: assistentesSelecionados,
                equipes: equipesSelecionadas,
                operadoras: operadorasSelecionadas
            });

            if (!dadosCompletos || !dadosCompletos.length) {
                return;
            }

            // Filtra os dados com base nas seleções
            let dadosFiltrados = dadosCompletos;

            // Filtra por assistente
            if (assistentesSelecionados.length > 0) {
                dadosFiltrados = dadosFiltrados.filter(item => {
                    return assistentesSelecionados.includes(item.assistente);
                });
            }

            // Filtra por equipe
            if (equipesSelecionadas.length > 0) {
                dadosFiltrados = dadosFiltrados.filter(item => {
                    return equipesSelecionadas.includes(item.equipe);
                });
            }

            // Filtra por operadora
            if (operadorasSelecionadas.length > 0) {
                dadosFiltrados = dadosFiltrados.filter(item => {
                    return operadorasSelecionadas.includes(item.operadora);
                });
            }

            // Se não encontrar nada com os filtros, exibe mensagem
            if (dadosFiltrados.length === 0) {
                console.log("Nenhum dado encontrado com os filtros selecionados");
                $('#metasAssistentesTable tbody').html('<tr><td colspan="5" class="text-center">Nenhum dado disponível para os filtros selecionados</td></tr>');
                $('#graficos-metas-assistentes').empty().html('<p>Nenhum dado disponível para os filtros selecionados</p>');
                return;
            }

            console.log("Dados filtrados:", dadosFiltrados);

            // Reinicializa a tabela com os dados filtrados
            initDataTable(dadosFiltrados);

            // Atualiza os gráficos
            atualizarGraficos(dadosFiltrados);
        }

        // Atualiza os gráficos conforme dados filtrados
        function atualizarGraficos(dados) {
            const container = $('#graficos-metas-assistentes');
            container.empty();

            if (dados.length === 0) {
                container.html('<p>Nenhum dado disponível para os filtros selecionados.</p>');
                return;
            }

            // Criar div para o gráfico
            container.html('<div id="grafico-barras-assistentes" style="width: 100%; height: 400px;"></div>');

            // Agregamos dados por assistente para o gráfico (soma de todas as operadoras)
            const dadosPorAssistente = {};

            dados.forEach(item => {
                if (!dadosPorAssistente[item.assistente]) {
                    dadosPorAssistente[item.assistente] = {
                        assistente: item.assistente,
                        meta: 0,
                        vidas: 0
                    };
                }

                dadosPorAssistente[item.assistente].meta += item.meta;
                dadosPorAssistente[item.assistente].vidas += item.vidas;
            });

            // Convertemos para array
            const dadosGrafico = Object.values(dadosPorAssistente);

            // Preparar dados para o gráfico
            const assistentes = [];
            const metasDados = [];
            const vidasDados = [];

            dadosGrafico.forEach(item => {
                assistentes.push(item.assistente);
                metasDados.push(item.meta);
                vidasDados.push(item.vidas);
            });

            // Criar gráfico usando Chart.js (se disponível)
            if (typeof Chart !== 'undefined') {
                new Chart(document.getElementById('grafico-barras-assistentes').getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: assistentes,
                        datasets: [
                            {
                                label: 'Meta',
                                data: metasDados,
                                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'Vidas',
                                data: vidasDados,
                                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            } else {
                container.append('<p>Biblioteca Chart.js não disponível.</p>');
            }
        }

        // Inicializa os componentes
        initSeletores();
    }

    // Inicializa a função
    inicializarMetasVidasAssistentes();
}); 