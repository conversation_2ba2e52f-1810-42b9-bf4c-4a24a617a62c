console.log("Arquivo sugestoes.js carregado com sucesso!");

document.addEventListener("DOMContentLoaded", function () {
    console.log("DOM totalmente carregado e analisado.");

    const formSugestao = document.getElementById("form-sugestao");

    formSugestao.addEventListener("submit", function (event) {
        event.preventDefault(); // Impede o envio padrão do formulário
        console.log("Formulário foi submetido via AJAX.");

        // Confirmação antes de enviar a sugestão
        if (confirm("Realmente deseja enviar essa sugestão?")) {
            const formData = new FormData(formSugestao);

            fetch(formSugestao.action, {
                method: "POST",
                body: formData
            })
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        throw new Error("Erro na resposta do servidor");
                    }
                })
                .then(data => {
                    if (data.success) {
                        alert("Sugestão enviada com sucesso!");
                        formSugestao.reset(); // Reseta o formulário
                    } else {
                        alert("Erro: " + data.message);
                    }
                })
                .catch(error => {
                    console.error("Erro ao enviar a sugestão:", error);
                    alert("Erro ao enviar a sugestão. Por favor, tente novamente.");
                });
        } else {
            alert("Envio da sugestão cancelado.");
        }
    });
});