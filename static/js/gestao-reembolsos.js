// Função para abrir o modal com os detalhes da solicitação
function openModal(card) {
    const reembolsoId = card.id;

    fetch(`/detalhes-reembolso/${reembolsoId}`)
        .then(response => response.json())
        .then(reembolso => {
            if (reembolso.error) {
                alert(reembolso.error);
            } else {
                // Preenchendo os campos do modal com os dados do reembolso
                document.getElementById("modal-nome").innerText = reembolso.nome || 'Não definido';
                document.getElementById("modal-departamento").innerText = reembolso.departamento || 'Não definido';
                document.getElementById("modal-email").innerText = reembolso.email || 'Não definido';
                document.getElementById("modal-telefone").innerText = reembolso.telefone || 'Não definido';
                document.getElementById("modal-data").innerText = formatDateTime(reembolso.data_despesa);
                document.getElementById("modal-tipo").innerText = reembolso.tipo_despesa || 'Não definido';
                document.getElementById("modal-valor").innerText = reembolso.valor_despesa || 'Não definido';
                document.getElementById("modal-data-solicitacao").innerText = formatDateTime(reembolso.data_solicitacao) || 'Não definido';

                // Link do anexo
                const anexoElement = document.getElementById("modal-anexo");
                if (anexoElement) {
                    const anexoUrl = reembolso.link_anexo || '#';
                    const anexoNome = anexoUrl !== '#'
                        ? anexoUrl.replace('https://intranet-picture-profile.s3.amazonaws.com/', '')
                        : 'Não disponível';
                    anexoElement.href = anexoUrl;
                    anexoElement.innerText = anexoNome;
                }

                document.getElementById("modal-descricao").innerText = reembolso.descricao || 'Não definido';
                document.getElementById("modal-aprovador").innerText = reembolso.nome_aprovador || '--';
                document.getElementById("modal-data-aprovacao").innerText = formatDateTime(reembolso.data_aprovacao) || '--';

                // Exibe o modal
                document.getElementById("modal").style.display = "flex";
            }
        })
        .catch(error => console.error('Erro ao buscar detalhes do reembolso:', error));

    document.getElementById("modal").setAttribute("data-active-card", card.id);
}

// Função para formatar data e hora no formato hh:mm dd/mm/aaaa
function formatDateTime(dateString) {
    if (!dateString) return 'Não definido'; // Caso a data seja nula ou indefinida

    const date = new Date(dateString); // Cria um objeto Date

    // Verifica se a data é válida
    if (isNaN(date.getTime())) {
        return 'Não definido'; // Retorna "Não definido" se a data for inválida
    }

    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year} - ${hours}:${minutes}`;
}

// Função para fechar o modal
function closeModal() {
    document.getElementById("modal").style.display = "none";
}

// Função para aprovar a solicitação
function approveRequest() {
    const cardId = document.getElementById("modal").getAttribute("data-active-card");
    const card = document.getElementById(cardId);
    document.querySelector('.approved').appendChild(card);
    atualizarStatus(cardId, 'Aprovado');
    closeModal();
}

// Função para rejeitar a solicitação
function rejectRequest() {
    const cardId = document.getElementById("modal").getAttribute("data-active-card");
    const card = document.getElementById(cardId);
    document.querySelector('.rejected').appendChild(card);
    atualizarStatus(cardId, 'Rejeitado');
    closeModal();
}

// Função para atualizar o status no backend
function atualizarStatus(cardId, novoStatus) {
    fetch(`/update-status-reembolso/${cardId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: novoStatus })
    })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                alert('Erro ao atualizar o status: ' + data.error);
            }
        })
        .catch(error => console.error('Erro ao atualizar o status:', error));
}

// Verifica se o usuário tem permissão
if (parseInt(userType) === 1 || parseInt(userType) === 7) {
    // Usuário com permissão para mover os cards
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('dragstart', dragStart);
        card.addEventListener('dragend', dragEnd);
    });

    document.querySelectorAll('.column').forEach(column => {
        column.addEventListener('dragover', dragOver);
        column.addEventListener('drop', drop);
        column.addEventListener('dragleave', dragLeave);
    });
} else {
    console.log('Usuário sem permissão para mover os blocos.');
}

// Função chamada quando o usuário começa a arrastar um card
function dragStart(e) {
    e.dataTransfer.setData('text/plain', e.target.id);
    e.target.classList.add('dragging');
    e.dataTransfer.setDragImage(new Image(), 0, 0);

    const rect = e.target.getBoundingClientRect();
    e.target.style.position = 'absolute';
    e.target.style.zIndex = '1000';
    e.target.style.width = `${rect.width}px`;
    e.target.style.height = `${rect.height}px`;
    e.target.style.left = `${e.clientX - rect.width / 2}px`;
    e.target.style.top = `${e.clientY - rect.height / 2}px`;

    document.addEventListener('mousemove', moveCard);
}

// Função para mover o card junto com o cursor
function moveCard(e) {
    const draggingCard = document.querySelector('.dragging');
    if (draggingCard) {
        draggingCard.style.left = `${e.clientX - draggingCard.offsetWidth / 2}px`;
        draggingCard.style.top = `${e.clientY - draggingCard.offsetHeight / 2}px`;
    }
}

// Função chamada quando o usuário termina de arrastar um card
function dragEnd(e) {
    e.target.classList.remove('dragging');
    e.target.style.position = '';
    e.target.style.zIndex = '';
    e.target.style.left = '';
    e.target.style.top = '';
    e.target.style.width = '';
    e.target.style.height = '';

    document.removeEventListener('mousemove', moveCard);
}

// Função chamada continuamente enquanto um card está sendo arrastado sobre uma coluna
function dragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('drag-over');
}

// Função chamada quando o card é solto em uma nova coluna
function drop(e) {
    e.preventDefault();
    const cardId = e.dataTransfer.getData('text/plain');
    const card = document.getElementById(cardId);

    if (e.target.closest('.approved')) {
        document.querySelector('.approved').appendChild(card);
        atualizarStatus(cardId, 'Aprovado');
    } else if (e.target.closest('.rejected')) {
        document.querySelector('.rejected').appendChild(card);
        atualizarStatus(cardId, 'Rejeitado');
    } else if (e.target.closest('.pending')) {
        document.querySelector('.pending').appendChild(card);
        atualizarStatus(cardId, 'Pendente');
    } else {
        console.log('O card não foi solto em uma coluna válida.');
    }

    e.target.closest('.column').classList.remove('drag-over');
}

// Função chamada quando o card sai de cima de uma coluna sem ser solto
function dragLeave(e) {
    e.currentTarget.classList.remove('drag-over');
}
