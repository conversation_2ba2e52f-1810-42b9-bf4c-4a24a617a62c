// Função para abrir o popup
function openPopup(popupId) {
    document.getElementById(popupId).style.display = 'block';
}

// Função para fechar o popup
function closePopup(popupId) {
    document.getElementById(popupId).style.display = 'none';
}

// Função para adicionar event listeners para os popups
function addPopupEventListeners() {
    const cards = document.querySelectorAll('.card-ramais');
    cards.forEach(card => {
        card.addEventListener('click', function() {
            const popupId = this.getAttribute('data-popup-id');
            openPopup(popupId);
        });
    });

    const closeButtons = document.querySelectorAll('.close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const popupId = this.getAttribute('data-popup-id');
            closePopup(popupId);
        });
    });
}

// Listener para o link de Ramais
document.getElementById('ramaisLink').addEventListener('click', function(event) {
    event.preventDefault();
    fetch('/ramais-colaborador')
        .then(response => response.text())
        .then(data => {
            const mainContent = document.getElementById('mainContent');
            mainContent.innerHTML = data;
            addPopupEventListeners(); // Reativar event listeners após o carregamento do conteúdo
        })
        .catch(error => console.error('Erro ao carregar o conteúdo de ramais:', error));
});

// Ativar os event listeners ao carregar o conteúdo
document.addEventListener('DOMContentLoaded', function() {
    addPopupEventListeners();
});
