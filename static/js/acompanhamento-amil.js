document.addEventListener("DOMContentLoaded", function () {
    // Função para ordenar os assistentes no select (A a Z)
    function ordenarSelect() {
        var select = document.getElementById('assistente-select');
        var options = Array.from(select.options);
        options.sort(function (a, b) {
            return a.text.localeCompare(b.text);
        });

        // Limpar e reordenar as opções no select
        select.innerHTML = '';
        options.forEach(function (option) {
            select.add(option);
        });
    }

    // Função para ordenar os dados da tabela pela quantidade (do maior para o menor)
    function ordenarDadosPorQuantidade(dadosAssistente) {
        return dadosAssistente.sort(function (a, b) {
            return b.qtd_beneficiarios - a.qtd_beneficiarios;
        });
    }

    // Função para carregar detalhes no modal
    function carregarDetalhes(assistente, situacao) {
        $.ajax({
            url: '/detalhes_acompanhamento',
            method: 'GET',
            data: {
                assistente: assistente,
                situacao: situacao
            },
            success: function (response) {
                var modalBody = document.getElementById('modal-body');
                modalBody.innerHTML = '';

                if (response.length > 0) {
                    // Ordenar por "Mês de Referência" (mais recente para mais antigo)
                    response.sort(function (a, b) {
                        // Convertendo o formato de mês/ano para um formato que possa ser comparado
                        var dataA = a.mes_referencia.split('/').reverse().join('-');
                        var dataB = b.mes_referencia.split('/').reverse().join('-');
                        return new Date(dataB) - new Date(dataA); // Ordem decrescente (mais novo → mais antigo)
                    });

                    // Criar tabela com as colunas solicitadas
                    var tabela = `<table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Porte</th>
                                            <th>Operadora</th>
                                            <th>Tipo de Venda</th>
                                            <th>CNPJ Empresa</th>
                                            <th>Nome/Razão Social</th>
                                            <th>Número da Proposta</th>
                                            <th>Qtde Beneficiários</th>
                                            <th>Valor Beneficiários</th>
                                            <th>Nome do Corretor</th>
                                            <th>Mês de Referência</th>
                                            <th>Situação</th>
                                        </tr>
                                    </thead>
                                    <tbody>`;

                    // Preencher a tabela com os dados do back-end
                    response.forEach(function (item) {
                        tabela += `<tr>
                                    <td>${item.porte}</td>
                                    <td>${item.operadora}</td>
                                    <td>${item.tipo_venda}</td>
                                    <td>${item.cnpj_empresa}</td>
                                    <td>${item.nome_razao_social}</td>
                                    <td>${item.numero_proposta}</td>
                                    <td>${item.qtde_beneficiarios}</td>
                                    <td>${item.valor_beneficiarios}</td>
                                    <td>${item.nome_corretor}</td>
                                    <td>${item.mes_referencia}</td>
                                    <td>${item.situacao}</td>
                                   </tr>`;
                    });

                    tabela += `</tbody></table>`;
                    modalBody.innerHTML = tabela;
                } else {
                    modalBody.innerHTML = '<p>Não foram encontrados detalhes.</p>';
                }

                // Exibir o modal
                document.getElementById('infoModal').style.display = 'block';
            },
            error: function (xhr, status, error) {
                console.error('Erro ao carregar os detalhes: ', error);
            }
        });
    }

    // Função para atualizar a tabela com base no assistente selecionado
    function atualizarTabela(assistente, dadosAssistente) {
        var tabelaCorpo = document.querySelector('#assistente-table tbody');
        var tabelaHeader = document.querySelector('#assistente-header');

        // Atualizar o cabeçalho para mostrar o nome do assistente
        tabelaHeader.innerHTML = `Assistente: ${assistente}`;

        // Limpar a tabela
        tabelaCorpo.innerHTML = '';

        // Ordenar os dados pela quantidade (maior para menor)
        var dadosOrdenados = ordenarDadosPorQuantidade(dadosAssistente);

        // Atualizar a data de última atualização (assume que todos os registros do mesmo assistente têm a mesma data de atualização)
        if (dadosOrdenados.length > 0 && dadosOrdenados[0].ultima_atualizacao) {
            document.getElementById('data-atualizacao').innerHTML = `Última atualização: ${dadosOrdenados[0].ultima_atualizacao}`;
        } else {
            document.getElementById('data-atualizacao').innerHTML = 'Última atualização: Não disponível';
        }

        // Renderizar os dados agregados
        dadosOrdenados.forEach(function (item) {
            var tr = document.createElement('tr');
            tr.classList.add('tabela-linha');
            tr.setAttribute('data-status', item.situacao);

            // Exibir as colunas de situação, quantidade de beneficiários e soma de beneficiários
            tr.innerHTML = `
                <td>${item.situacao}</td>
                <td>${item.qtd_beneficiarios}</td>
                <td>${item.soma_beneficiarios}</td>`;  // Nova coluna para a soma dos beneficiários
            tabelaCorpo.appendChild(tr);
        });

        // Tornar as linhas clicáveis para abrir o modal (se aplicável)
        var linhas = document.querySelectorAll('.tabela-linha');
        linhas.forEach(function (linha) {
            linha.addEventListener('click', function () {
                var status = this.getAttribute('data-status');
                var assistente = document.getElementById('assistente-header').textContent.replace('Assistente: ', '');

                // Chamar a função para carregar os detalhes no modal
                carregarDetalhes(assistente, status);
            });
        });
    }

    // Inicializar o select em ordem alfabética
    ordenarSelect();

    // Atualizar a tabela inicialmente com o primeiro assistente
    var assistenteSelecionado = document.getElementById('assistente-select').value;
    var dadosIniciais = window.assistentesData[assistenteSelecionado];
    atualizarTabela(assistenteSelecionado, dadosIniciais);

    // Atualizar a tabela quando o assistente for alterado
    document.getElementById('assistente-select').addEventListener('change', function () {
        var assistenteSelecionado = this.value;
        var dadosAssistente = window.assistentesData[assistenteSelecionado];

        atualizarTabela(assistenteSelecionado, dadosAssistente);
    });

    // Inicializando DataTables para a tabela
    $('#assistente-table').DataTable({
        paging: false,
        searching: false,
        ordering: false,
        info: false
    });

    // Fechar o modal ao clicar no botão de fechar ou no botão "Fechar"
    var closeModalElements = document.querySelectorAll('.close-btn, #close-modal-btn');
    closeModalElements.forEach(function (el) {
        el.addEventListener('click', function () {
            document.getElementById('infoModal').style.display = 'none';
        });
    });
});
