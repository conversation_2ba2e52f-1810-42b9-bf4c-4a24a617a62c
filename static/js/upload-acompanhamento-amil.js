document.getElementById('upload-form').addEventListener('submit', function (event) {
    event.preventDefault(); // Evita o comportamento padrão de envio do formulário

    var form = event.target;
    var formData = new FormData(form);

    // Esconde mensagem de sucesso (caso esteja visível)
    document.getElementById('mensagem-sucesso').style.display = 'none';

    showLoadingScreen();  // Mostra a tela de carregamento

    // Envia os dados do formulário via fetch
    fetch('/upload_acompanhamento', {
        method: 'POST',
        body: formData
    }).then(response => {
        hideLoadingScreen();  // Esconde a tela de carregamento

        // Log para verificar o status da resposta
        console.log('Status da resposta:', response.status);

        // Verificar se a resposta foi bem-sucedida
        if (response.ok) {
            // Tentar extrair e logar o conteúdo da resposta JSON
            return response.json().then(data => {
                console.log('Dados da resposta JSON:', data);

                // Verificar se é uma resposta de sucesso (contém 'message')
                if (data.message) {
                    // Mostrar mensagem de sucesso
                    document.getElementById('mensagem-sucesso').style.display = 'block';

                    // Limpar o formulário
                    form.reset();

                    // Opcional: Rolar para o topo para garantir que a mensagem seja vista
                    window.scrollTo(0, 0);

                    // Opcional: Esconder a mensagem após alguns segundos (5 segundos neste exemplo)
                    setTimeout(function () {
                        document.getElementById('mensagem-sucesso').style.display = 'none';
                    }, 5000);

                    // Remover o redirecionamento automático para que o usuário possa ver a mensagem
                    // window.location.href = '/upload_acompanhamento'; // Comentado para não redirecionar
                } else if (data.error) {
                    console.error('Erro do servidor:', data.error);
                    alert('Erro do servidor: ' + data.error);
                }
            });
        } else {
            // Se a resposta não for ok (status diferente de 200), logar o problema
            console.error('Erro ao fazer upload. Status não OK:', response.status);
            alert('Erro ao fazer upload. Tente novamente. Código: ' + response.status);
        }
    }).catch(error => {
        hideLoadingScreen();  // Esconde a tela de carregamento em caso de erro
        console.error('Erro no fetch:', error);
        alert('Erro ao fazer upload. Tente novamente.');
    });
});

// Função para mostrar a tela de carregamento
function showLoadingScreen() {
    var loadingScreen = document.getElementById("loadingScreen");
    if (loadingScreen) {
        loadingScreen.style.display = 'flex'; // Mostra o loading screen
    }
}

// Função para esconder a tela de carregamento
function hideLoadingScreen() {
    var loadingScreen = document.getElementById("loadingScreen");
    if (loadingScreen) {
        loadingScreen.style.display = 'none'; // Esconde o loading screen
    }
}