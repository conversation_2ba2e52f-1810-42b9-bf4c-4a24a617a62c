document.addEventListener('DOMContentLoaded', function () {
    initTableFilters();
    sortColumn(0, 'asc');
    createAcessModal();
});

let activeFilters = {}; // Armazena os filtros ativos

function initTableFilters() {
    const headers = document.querySelectorAll('.acessos-table th');

    headers.forEach((header, index) => {
        const button = header.querySelector('.filter-button');
        if (button) {
            button.addEventListener('click', function (event) {
                event.stopPropagation();
                toggleDropdown(event, index);
            });
        }
    });
}

function toggleDropdown(event, columnIndex) {
    const currentDropdown = document.querySelector('.dropdown-menu');
    if (currentDropdown && currentDropdown.dataset.columnIndex == columnIndex) {
        closeAllDropdowns();
        return;
    }
    closeAllDropdowns();
    const dropdown = createDropdown(columnIndex);
    dropdown.dataset.columnIndex = columnIndex;
    document.body.appendChild(dropdown);
    positionDropdown(dropdown, event.target);

    dropdown.addEventListener('click', function (event) {
        event.stopPropagation();
    });

    document.addEventListener('click', closeAllDropdowns, { once: true });
}

function createDropdown(columnIndex) {
    const dropdown = document.createElement('div');
    dropdown.classList.add('dropdown-menu', 'show');
    const uniqueValues = getUniqueColumnValues(columnIndex);

    dropdown.innerHTML = `
        <div class="sort-options">
            <button onclick="sortColumn(${columnIndex}, 'asc')"><i class="bi bi-sort-alpha-down"></i></button><p>Classificar de A a Z</p>
        </div>
        <div class="sort-options">
            <button onclick="sortColumn(${columnIndex}, 'desc')"><i class="bi bi-sort-alpha-down-alt"></i></button><p>Classificar de Z a A</p>
        </div>
        <input type="text" placeholder="Pesquisar..." oninput="filterDropdownSearch(this, ${columnIndex})">
        <ul class="filter-options">${generateFilterOptions(uniqueValues)}</ul>
        <div class="filter-actions">
            <button onclick="applyFilters(${columnIndex})">Aplicar</button>
            <button onclick="closeAllDropdowns()">Cancelar</button>
        </div>
    `;

    return dropdown;
}

function getUniqueColumnValues(columnIndex) {
    const values = new Set();
    document.querySelectorAll('.acessos-table tbody tr').forEach(row => {
        const cellText = row.cells[columnIndex]?.innerText.trim();
        if (cellText) values.add(cellText);
    });
    return [...values];
}

function generateFilterOptions(uniqueValues) {
    return uniqueValues.map(value => {
        const safeValue = value.replace(/"/g, '&quot;');
        return `<li>
            <input type="checkbox" id="filter-${safeValue}" name="filter-option" value="${safeValue}">
            <label for="filter-${safeValue}">${value}</label>
        </li>`;
    }).join('');
}

function positionDropdown(dropdown, referenceElement) {
    const tableRect = document.querySelector('.acessos-table').getBoundingClientRect();
    const buttonRect = referenceElement.getBoundingClientRect();
    const theadRect = document.querySelector('.acessos-table thead').getBoundingClientRect();

    dropdown.style.position = 'absolute';
    dropdown.style.top = `${theadRect.bottom}px`;

    let leftPosition = buttonRect.left + (buttonRect.width / 2) - (dropdown.offsetWidth / 2);
    leftPosition = Math.max(tableRect.left, leftPosition);
    leftPosition = Math.min(leftPosition, tableRect.right - dropdown.offsetWidth);

    dropdown.style.left = `${leftPosition}px`;
}

function sortColumn(columnIndex, order) {
    const table = document.querySelector('.acessos-table tbody');
    const rowsArray = Array.from(table.rows);

    const compareFunction = (rowA, rowB) => {
        const valueA = rowA.cells[columnIndex].innerText.toLowerCase();
        const valueB = rowB.cells[columnIndex].innerText.toLowerCase();

        if (order === 'asc') {
            return valueA.localeCompare(valueB);
        } else {
            return valueB.localeCompare(valueA);
        }
    };

    rowsArray.sort(compareFunction);
    rowsArray.forEach(row => table.appendChild(row));
}

function filterDropdownSearch(inputElement, columnIndex) {
    const searchValue = inputElement.value.toLowerCase();
    const options = inputElement.closest('.dropdown-menu').querySelectorAll('.filter-options li');
    options.forEach(option => {
        const matchesSearch = option.textContent.toLowerCase().includes(searchValue);
        option.style.display = matchesSearch ? '' : 'none';
    });
}

function applyFilters(columnIndex) {
    const checkboxes = document.querySelectorAll('.dropdown-menu .filter-options input[type="checkbox"]:checked');
    const filterValues = Array.from(checkboxes).map(cb => cb.value.toUpperCase());

    document.querySelectorAll('.acessos-table tbody tr').forEach(row => {
        const cellText = row.cells[columnIndex].innerText.toUpperCase();
        if (filterValues.length === 0 || filterValues.includes(cellText)) {
            row.style.display = "";
        } else {
            row.style.display = "none";
        }
    });

    closeAllDropdowns();
}

function closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown-menu');
    dropdowns.forEach(dropdown => dropdown.remove());
}

function searchTable() {
    const input = document.getElementById("searchInput");
    const filter = input.value.toUpperCase();
    const table = document.querySelector(".acessos-table tbody");
    const tr = table.getElementsByTagName("tr");

    for (let i = 0; i < tr.length; i++) {
        let displayRow = false;
        const td = tr[i].getElementsByTagName("td");

        for (let j = 0; j < td.length; j++) {
            if (td[j]) {
                const columnClass = td[j].classList[0]; // Pega a primeira classe da célula
                if (["column-nome", "column-login", "column-unidade", "column-codigo"].includes(columnClass)) {
                    const txtValue = td[j].textContent || td[j].innerText;
                    if (txtValue.toUpperCase().indexOf(filter) > -1) {
                        displayRow = true;
                        break;
                    }
                }
            }
        }
        tr[i].style.display = displayRow ? "" : "none";
    }
}

function copyPassword(element) {
    const passwordText = element.parentElement.querySelector('.senha-text');
    const passwordValue = passwordText.dataset.password;
    navigator.clipboard.writeText(passwordValue).then(() => {
        alert("Senha copiada para a área de transferência.");
    }).catch(err => {
        console.error("Erro ao copiar senha: ", err);
    });
}

function togglePasswordVisibility(element) {
    const passwordText = element.parentElement.querySelector('.senha-text');
    const isPasswordHidden = passwordText.innerText === '***********';

    if (isPasswordHidden) {
        passwordText.innerText = passwordText.dataset.password;
        element.classList.replace('fa-eye', 'fa-eye-slash');
    } else {
        passwordText.innerText = '***********';
        element.classList.replace('fa-eye-slash', 'fa-eye');
    }
}

function createAcessModal() {
    const modalHTML = `
        <div id="acessModal" class="modal" style="display: none;">
            <div class="modal-content">
                <span class="close" onclick="closeAcessModal()">&times;</span>
                <h2>Alterar Senha</h2>
                <form id="updateAcessForm" action="/atualizar_acesso" method="POST">
                    <input type="hidden" name="acesso_id" id="acesso_id">
                    <input type="hidden" name="portal_id" id="portal_id">
                    <div class="input-portal">
                        <label for="novo_nome_portal">Nome do Portal:</label>
                        <input type="text" id="novo_nome_portal" name="novo_nome_portal" required>
                    </div>
                    <div class="input-portal">
                        <label for="novo_link_portal">Link do Portal:</label>
                        <input type="text" id="novo_link_portal" name="novo_link_portal" required>
                    </div>
                    <div class="input-portal">
                        <label for="novo_login">Login:</label>
                        <input type="text" id="novo_login" name="novo_login" required>
                    </div>
                    <div class="input-portal">
                        <label for="nova_senha">Nova Senha:</label>
                        <input type="text" id="nova_senha" name="nova_senha" required>
                    </div>
                    <div class="input-portal">
                        <label for="nova_unidade">Unidade/Empresa:</label>
                        <input type="text" id="nova_unidade" name="nova_unidade">
                    </div>
                    <div class="input-portal">
                        <label for="nova_modalidade">Modalidade:</label>
                        <input type="text" id="nova_modalidade" name="nova_modalidade">
                    </div>
                    <div class="input-portal">
                        <label for="novo_codigo_acesso">Código de Acesso:</label>
                        <input type="text" id="novo_codigo_acesso" name="novo_codigo_acesso">
                    </div>
                    <div class="input-portal">
                        <label for="novos_outros">Outros:</label>
                        <input type="text" id="novos_outros" name="novos_outros">
                    </div>
                    <button class="gestao-btn-acessar" type="button" onclick="submitAcessForm()">Salvar</button>
                </form>
            </div>
        </div>`;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function openAcessModal(acessoId, portalId, nomePortal, linkPortal, login, senha, unidade, codigoAcesso, outros, modalidade) {
    // Certifique-se de que o modal foi criado antes de acessar os campos
    if (!document.getElementById('acessModal')) {
        createAcessModal();
    }

    // Preenche os campos do modal
    document.getElementById('acesso_id').value = acessoId;
    document.getElementById('portal_id').value = portalId;
    document.getElementById('novo_nome_portal').value = nomePortal || '';
    document.getElementById('novo_link_portal').value = linkPortal || '';
    document.getElementById('novo_login').value = login || '';
    document.getElementById('nova_senha').value = senha || '';
    document.getElementById('nova_unidade').value = unidade || '';
    document.getElementById('novo_codigo_acesso').value = codigoAcesso || '';
    document.getElementById('novos_outros').value = outros || '';
    document.getElementById('nova_modalidade').value = modalidade || '';

    // Exibe o modal com transição
    const modal = document.getElementById('acessModal');
    modal.style.display = 'block';
    setTimeout(() => {
        modal.classList.add('show');
        document.querySelector('.modal-content').classList.add('show');
    }, 10); // Pequeno delay para a transição funcionar corretamente
}

function closeAcessModal() {
    const modal = document.getElementById('acessModal');
    modal.classList.remove('show');
    document.querySelector('.modal-content').classList.remove('show');

    // Delay para que a animação ocorra antes de esconder completamente
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300); // O tempo aqui deve coincidir com a duração da transição
}

function submitAcessForm() {
    // Obtendo os valores de todos os campos obrigatórios
    const nomePortal = document.getElementById('novo_nome_portal').value;
    const linkPortal = document.getElementById('novo_link_portal').value;
    const login = document.getElementById('novo_login').value;
    const novaSenha = document.getElementById('nova_senha').value;

    // Verifica se os campos obrigatórios foram preenchidos
    if (nomePortal && linkPortal && login && novaSenha) {
        // Submete o formulário
        document.getElementById('updateAcessForm').submit();
    } else {
        // Alerta caso algum campo obrigatório não tenha sido preenchido
        alert('Por favor, preencha todos os campos obrigatórios.');
    }
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    activeFilters = {};

    const rows = document.querySelectorAll('.acessos-table tbody tr');
    rows.forEach(row => {
        row.style.display = "";
    });

    const checkboxes = document.querySelectorAll('.filter-options input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    closeAllDropdowns();
}

document.getElementById('clearFilterBtn').addEventListener('click', clearFilters);