document.addEventListener('DOMContentLoaded', function() {
    // Seleciona o elemento do calendário
    var calendarEl = document.getElementById('calendar');
    
    // Inicializa o FullCalendar
    var calendar = new FullCalendar.Calendar(calendarEl, {
      themeSystem: 'bootstrap5',
      locale: 'pt-br',
      initialView: 'dayGridMonth',
      headerToolbar: {
        left: 'prev,next today',
        center: 'title',
        right: 'dayGridMonth,timeGridWeek,timeGridDay'
      },
      // Define a tradução dos botões (caso necessário)
      buttonText: {
        today: 'Hoje',
        month: 'Mês',
        week: 'Semana',
        day: 'Dia'
      },
      // Carrega os eventos via AJAX do seu endpoint
      events: '/api/calendar_events',
      
      // Ao clicar em uma data, pode abrir o modal para criação de evento (opcional)
      dateClick: function(info) {
        document.getElementById('eventStart').value = info.dateStr;
        document.getElementById('eventEnd').value = info.dateStr;
        var eventModal = new bootstrap.Modal(document.getElementById('eventModal'));
        eventModal.show();
      },
      
      // Ao clicar em um evento, pode exibir detalhes ou abrir um modal para edição
      eventClick: function(info) {
        // Exemplo: exibe um alerta com informações do evento
        alert("Evento: " + info.event.title + "\nDescrição: " + info.event.extendedProps.descricao);
        // Aqui você pode abrir um modal para edição, por exemplo.
      }
    });
    
    // Renderiza o calendário
    calendar.render();
  
    // Exemplo de manipulação do formulário de novo evento (opcional)
    const eventForm = $('#eventForm');
    eventForm.on('submit', function(e) {
      e.preventDefault();
      try {
        const eventData = {
          title: $('#eventTitle').val().trim(),
          start: $('#eventStart').val().trim(),
          end: $('#eventEnd').val().trim(),
          allDay: $('#eventAllDay').is(':checked'),
          description: $('#eventDescription').val().trim()
        };
  
        if (!eventData.title || !eventData.start) {
          throw new Error('Por favor, preencha todos os campos obrigatórios');
        }
        
        // Aqui você pode fazer uma chamada AJAX para salvar o novo evento no backend
        console.log('Evento salvo:', eventData);
        
        // Opcional: adicione o novo evento ao calendário (caso queira atualizar sem recarregar)
        calendar.addEvent({
          title: eventData.title,
          start: eventData.start,
          end: eventData.end,
          allDay: eventData.allDay,
          extendedProps: { description: eventData.description }
        });
  
        // Fecha o modal e reseta o formulário
        var modalEl = document.getElementById('eventModal');
        var modalInstance = bootstrap.Modal.getInstance(modalEl);
        if (modalInstance) modalInstance.hide();
        eventForm[0].reset();
      } catch (error) {
        console.error('Erro ao criar evento:', error);
        alert('Erro ao criar evento: ' + error.message);
      }
    });
  });
  