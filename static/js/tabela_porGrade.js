document.addEventListener('DOMContentLoaded', function () {
    const gradeFilter = document.getElementById('gradeFilter');
    const operadoraFilter = document.getElementById('operadoraFilter');
    const modalidadeFilter = document.getElementById('modalidadeFilter');
    const buscarBtn = document.getElementById('buscarBtn');
    const loadingScreen = document.getElementById('loadingScreen');
    const exportExcelBtn = document.getElementById('exportExcel');
    const exportPdfBtn = document.getElementById('exportPdf');

    // Quando o usuário selecionar uma grade, buscar as operadoras e modalidades correspondentes
    gradeFilter.addEventListener('change', function () {
        const grade = gradeFilter.value;

        if (grade) {
            loadingScreen.style.display = 'block';

            fetch('/tabelas-comissoes/grade/filtros', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ grade: grade })
            })
                .then(response => response.json())
                .then(data => {
                    operadoraFilter.innerHTML = '<option value="">Selecione uma Operadora</option>';
                    data.operadoras.forEach(operadora => {
                        operadoraFilter.insertAdjacentHTML('beforeend', `<option value="${operadora}">${operadora}</option>`);
                    });

                    modalidadeFilter.innerHTML = '<option value="">Selecione uma Modalidade</option>';
                    data.modalidades.forEach(modalidade => {
                        modalidadeFilter.insertAdjacentHTML('beforeend', `<option value="${modalidade}">${modalidade}</option>`);
                    });
                })
                .catch(error => {
                    console.error('Erro ao buscar operadoras e modalidades:', error);
                    alert('Erro ao buscar operadoras e modalidades. Tente novamente.');
                })
                .finally(() => {
                    loadingScreen.style.display = 'none';
                });
        }
    });

    // Lógica do botão de buscar
    buscarBtn.addEventListener('click', function () {
        const grade = gradeFilter.value;
        const operadora = operadoraFilter.value;
        const modalidade = modalidadeFilter.value;

        if (!grade && !operadora && !modalidade) {
            alert("Nenhum filtro selecionado. Carregando dados padrão...");
        }

        loadingScreen.style.display = 'block';

        fetch('/tabelas-comissoes/grade', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ grade, operadora, modalidade })
        })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.error);
                } else {
                    const tabelaResultados = document.getElementById('tabelaResultados');
                    tabelaResultados.innerHTML = ''; // Limpar a tabela antes de inserir novos dados

                    // Classificando os dados por Grade (A-Z)
                    tabelasOrdenadas = data.tabelas.sort((a, b) => {
                        // Primeiro critério: grade (A-Z)
                        const gradeComparison = a.grade.localeCompare(b.grade);
                        if (gradeComparison !== 0) {
                            return gradeComparison;
                        }

                        // Segundo critério: operadora (A-Z)
                        const operadoraComparison = a.operadora.localeCompare(b.operadora);
                        if (operadoraComparison !== 0) {
                            return operadoraComparison;
                        }

                        // Terceiro critério: modalidade (A-Z)
                        return a.modalidade.localeCompare(b.modalidade);
                    });

                    tabelasOrdenadas.forEach(function (tabela) {
                        const row = `
                        <tr>
                            <td>${tabela.operadora}</td>
                            <td>${tabela.modalidade}</td>
                            <td>${tabela.grade}</td>
                            <td>${(tabela.totais * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_1 * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_2 * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_3 * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_4 * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_5 * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_6 * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_7 * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_8 * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_9 * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_10 * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_11 * 100).toFixed(2)}%</td>
                            <td>${(tabela.parcela_12 * 100).toFixed(2)}%</td>
                        </tr>
                    `;
                        tabelaResultados.insertAdjacentHTML('beforeend', row); // Inserir os dados
                    });
                }
            })
            .catch(error => {
                console.error('Erro ao buscar dados:', error);
                alert("Erro ao buscar dados, tente novamente.");
            })
            .finally(() => {
                loadingScreen.style.display = 'none';
            });
    });

    // Função para exportar para Excel
    exportExcelBtn.addEventListener('click', function () {
        const tabela = document.getElementById('tabelaResultados');
        const rows = Array.from(tabela.querySelectorAll('tr')); // Pegar todas as linhas da tabela

        // Criar uma matriz de arrays para armazenar os dados da tabela
        const data = [];

        // Adicionar o cabeçalho manualmente
        const header = [
            "Operadora", "Modalidade", "Grade", "Totais (%)", "Parcela 1 (%)", "Parcela 2 (%)", "Parcela 3 (%)",
            "Parcela 4 (%)", "Parcela 5 (%)", "Parcela 6 (%)", "Parcela 7 (%)", "Parcela 8 (%)", "Parcela 9 (%)",
            "Parcela 10 (%)", "Parcela 11 (%)", "Parcela 12 (%)"
        ];
        data.push(header);

        // Preencher as linhas com os dados da tabela
        rows.forEach(row => {
            const cells = Array.from(row.querySelectorAll('td')).map((cell, index) => {
                let value = cell.innerText;

                // Converter colunas de 4 até 16 para porcentagem
                if (index >= 3 && index <= 15) {
                    value = parseFloat(value.replace('%', '')) / 100; // Remover '%' e converter para número decimal
                }

                return value;
            });

            if (cells.length > 0) {
                data.push(cells); // Adicionar a linha aos dados
            }
        });

        // Criar a planilha Excel
        const ws = XLSX.utils.aoa_to_sheet(data); // Criar a planilha a partir da matriz de arrays
        const wb = XLSX.utils.book_new(); // Criar um novo workbook
        XLSX.utils.book_append_sheet(wb, ws, "TabelaComissoes"); // Adicionar a planilha ao workbook

        // Salvar o arquivo Excel
        XLSX.writeFile(wb, 'tabela_comissoes.xlsx');
    });

    // Função para exportar para PDF
    document.getElementById('exportPdfBtn').addEventListener('click', function () {
        const grade = document.getElementById('gradeFilter').value;
        const operadora = document.getElementById('operadoraFilter').value;
        const modalidade = document.getElementById('modalidadeFilter').value;

        fetch('/exportar-pdf', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ grade, operadora, modalidade })
        })
            .then(response => {
                if (response.ok) {
                    return response.blob();  // Converte a resposta para Blob (arquivo)
                } else {
                    alert('Erro ao gerar o PDF. Tente novamente.');
                }
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = 'tabela_filtrada.pdf';  // Nome do arquivo de download
                document.body.appendChild(a);
                a.click();  // Dispara o download
                window.URL.revokeObjectURL(url);  // Libera a memória usada pelo Blob
            })
            .catch(error => {
                console.error('Erro ao gerar PDF:', error);
                alert('Erro ao gerar o PDF. Tente novamente.');
            });
    });
});
