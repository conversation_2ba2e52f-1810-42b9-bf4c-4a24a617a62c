document.addEventListener("DOMContentLoaded", function () {
    console.log("DOM totalmente carregado e analisado.");

    // Função para carregar conteúdo dinâmico ao clicar no link de reembolso
    const reembolsoLink = document.getElementById('reembolsoLink');
    
    if (reembolsoLink) {
        reembolsoLink.addEventListener('click', function(event) {
            event.preventDefault();  // Previne a navegação padrão ao clicar no link
            console.log("Link de reembolso clicado");

            fetch('/reembolso-colaborador')  // Faz uma requisição GET para a rota do formulário
                .then(response => {
                    console.log('Resposta recebida do servidor:', response);
                    return response.text();
                })
                .then(data => {
                    const mainContent = document.getElementById('mainContent');
                    console.log('Conteúdo carregado:', data);
                    mainContent.innerHTML = data;  // Insere o conteúdo da resposta (HTML do formulário) na div mainContent
                    removeClasses();  // Remove quaisquer classes aplicadas anteriormente
                    mainContent.classList.add('reembolso-page');  // Adiciona a classe específica para o estilo da página de reembolso

                    // Adiciona a classe 'reembolsos' ao body
                    document.body.classList.add('reembolsos');

                    // Bloquear campos específicos
                    document.getElementById('nome').setAttribute('readonly', true);
                    document.getElementById('telefone').setAttribute('readonly', true);
                    document.getElementById('email').setAttribute('readonly', true);

                    // Agora que o conteúdo foi carregado, precisamos adicionar os event listeners novamente
                    attachFormEventListener();
                })
                .catch(error => console.error('Erro ao carregar o conteúdo de reembolso:', error));
        });
    } else {
        console.error("Elemento reembolsoLink não encontrado.");
    }

    // Função auxiliar para remover classes (baseada na sua estrutura existente)
    function removeClasses() {
        const mainContent = document.getElementById('mainContent');
        mainContent.classList.remove('sugestoes-page', 'chat-page', 'acessos-colaborador', 'reembolso-page', 'ramais-page');
    }

    // Função para anexar event listener ao formulário de reembolso
    function attachFormEventListener() {
        const formReembolso = document.getElementById("form-reembolso");
        console.log('Anexando event listener ao formulário:', formReembolso);

        if (formReembolso) {  // Verifique se o elemento existe
            formReembolso.addEventListener("submit", function (event) {
                event.preventDefault(); // Impede o envio padrão do formulário
                console.log("Formulário de reembolso foi submetido via AJAX.");

                let formValid = true;
                let errorMessage = '';

                // Validação dos campos obrigatórios
                if (document.getElementById('nome').value.trim() === '') {
                    formValid = false;
                    errorMessage += 'Por favor, preencha o campo Nome Completo.\n';
                    document.getElementById('nome').classList.add('input-error');
                } else {
                    document.getElementById('nome').classList.remove('input-error');
                }

                if (document.getElementById('departamento').value.trim() === '') {
                    formValid = false;
                    errorMessage += 'Por favor, preencha o campo Departamento.\n';
                    document.getElementById('departamento').classList.add('input-error');
                } else {
                    document.getElementById('departamento').classList.remove('input-error');
                }

                if (document.getElementById('email').value.trim() === '') {
                    formValid = false;
                    errorMessage += 'Por favor, preencha o campo E-mail.\n';
                    document.getElementById('email').classList.add('input-error');
                } else {
                    document.getElementById('email').classList.remove('input-error');
                }

                if (document.getElementById('telefone').value.trim() === '') {
                    formValid = false;
                    errorMessage += 'Por favor, preencha o campo Telefone.\n';
                    document.getElementById('telefone').classList.add('input-error');
                } else {
                    document.getElementById('telefone').classList.remove('input-error');
                }

                if (document.getElementById('data-despesa').value.trim() === '') {
                    formValid = false;
                    errorMessage += 'Por favor, selecione a Data da Despesa.\n';
                    document.getElementById('data-despesa').classList.add('input-error');
                } else {
                    document.getElementById('data-despesa').classList.remove('input-error');
                }

                if (document.getElementById('tipo-despesa').value === '') {
                    formValid = false;
                    errorMessage += 'Por favor, selecione o Tipo de Despesa.\n';
                    document.getElementById('tipo-despesa').classList.add('input-error');
                } else {
                    document.getElementById('tipo-despesa').classList.remove('input-error');
                }

                if (document.getElementById('valor').value.trim() === '' || isNaN(document.getElementById('valor').value) || parseFloat(document.getElementById('valor').value) <= 0) {
                    formValid = false;
                    errorMessage += 'Por favor, insira um Valor válido para a Despesa.\n';
                    document.getElementById('valor').classList.add('input-error');
                } else {
                    document.getElementById('valor').classList.remove('input-error');
                }

                if (document.getElementById('descricao').value.trim() === '') {
                    formValid = false;
                    errorMessage += 'Por favor, preencha o campo Descrição.\n';
                    document.getElementById('descricao').classList.add('input-error');
                } else {
                    document.getElementById('descricao').classList.remove('input-error');
                }

                // Validação do arquivo comprovante
                const allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png'];
                const comprovanteInput = document.getElementById('comprovante');
                const fileExtension = comprovanteInput.value.split('.').pop().toLowerCase();

                if (!allowedExtensions.includes(fileExtension)) {
                    formValid = false;
                    errorMessage += 'Por favor, anexe um arquivo de comprovante válido (PDF, JPG, PNG).\n';
                    comprovanteInput.classList.add('input-error');
                } else {
                    comprovanteInput.classList.remove('input-error');
                }

                if (formValid) {
                    if (confirm("Realmente deseja enviar essa solicitação de reembolso?")) {
                        const formData = new FormData(formReembolso);

                        fetch(formReembolso.action, {
                            method: "POST",
                            body: formData
                        })
                        .then(response => {
                            console.log('Resposta do envio do formulário:', response);
                            if (response.ok) {
                                return response.json();
                            } else {
                                throw new Error("Erro na resposta do servidor");
                            }
                        })
                        .then(data => {
                            console.log('Dados recebidos do servidor:', data);
                            if (data.Success) {
                                showAlert(data.message, 'success');  // Exibe a mensagem de sucesso
                                formReembolso.reset(); // Reseta o formulário
                            } else {
                                showAlert("Erro: " + data.message, 'danger');  // Exibe a mensagem de erro retornada pelo servidor
                            }
                        })
                        .catch(error => {
                            console.error("Erro ao enviar a solicitação de reembolso:", error);
                            showAlert("Erro ao enviar a solicitação de reembolso. Por favor, tente novamente.", 'danger');
                        });
                    } else {
                        showAlert("Envio do reembolso cancelado.", 'warning');
                    }
                } else {
                    showAlert(errorMessage, 'danger');
                }
            });
        } else {
            console.error("Formulário de reembolso não encontrado.");
        }
    }

    function showAlert(message, type) {
        const alertBox = document.getElementById('alertBox');
        alertBox.className = `alert alert-${type}`;
        alertBox.innerText = message;
        alertBox.classList.remove('d-none');

        setTimeout(() => {
            alertBox.classList.add('d-none');
        }, 5000); // Oculta o alerta após 5 segundos
    }
});
