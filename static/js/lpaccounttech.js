document.addEventListener('DOMContentLoaded', function () {
    // Inicialização do Swiper
    const swiper = new Swiper('.partners-swiper', {
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        breakpoints: {
            480: {
                slidesPerView: 2,
                spaceBetween: 20
            },
            768: {
                slidesPerView: 3,
                spaceBetween: 30
            },
            1024: {
                slidesPerView: 4,
                spaceBetween: 40
            },
            1200: {
                slidesPerView: 5,
                spaceBetween: 50
            }
        },
        effect: 'slide',
        speed: 800,
        grabCursor: true
    });

    // Rolagem suave para todos os links internos
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                // Offset para considerar o header fixo
                const headerOffset = 100;
                const elementPosition = targetElement.getBoundingClientRect().top;
                const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Animação de entrada dos elementos quando aparecem na viewport
    const observerOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                if (entry.target.classList.contains('stat-number')) {
                    animateNumber(entry.target);
                }
            }
        });
    }, observerOptions);

    // Elementos para animar
    const animatedElements = document.querySelectorAll('.about-card, .stat-item, .promo-card, .partner-card');
    animatedElements.forEach(el => {
        el.classList.add('animate-element');
        observer.observe(el);
    });

    // Animação dos números nas estatísticas
    function animateNumber(element) {
        const finalNumber = parseInt(element.textContent.replace(/[^0-9]/g, ''));
        let currentNumber = 0;
        const duration = 2000; // 2 segundos
        const steps = 60;
        const increment = finalNumber / steps;
        const stepDuration = duration / steps;

        const counter = setInterval(() => {
            currentNumber += increment;
            if (currentNumber >= finalNumber) {
                element.textContent = element.textContent.includes('+') ?
                    `+${finalNumber.toLocaleString()}` :
                    finalNumber.toLocaleString();
                clearInterval(counter);
            } else {
                element.textContent = element.textContent.includes('+') ?
                    `+${Math.floor(currentNumber).toLocaleString()}` :
                    Math.floor(currentNumber).toLocaleString();
            }
        }, stepDuration);
    }

    // Formulário de contato
    const form = document.getElementById('contactForm');

    form.addEventListener('submit', async function (e) {
        e.preventDefault();

        // Coleta os dados do formulário
        const formData = {
            fullName: document.getElementById('fullName').value,
            email: document.getElementById('email').value,
            phone: document.getElementById('phone').value,
            hasCnpj: document.querySelector('input[name="hasCnpj"]:checked').value,
            currentPlan: document.getElementById('currentPlan').value,
            city: document.getElementById('city').value,
            contactPreference: document.getElementById('contactPreference').value
        };

        try {
            // Envia os dados para o servidor
            const response = await fetch('/submit-form', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (response.ok) {
                // Mostra mensagem de sucesso
                alert(data.message);
                form.reset();
            } else {
                // Mostra mensagem de erro
                alert(data.message);
            }
        } catch (error) {
            alert('Erro ao enviar formulário. Por favor, tente novamente.');
            console.error('Erro:', error);
        }
    });

    // Máscara para o campo de telefone
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function (e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length <= 11) {
            value = value.replace(/^(\d{2})(\d)/g, '($1) $2');
            value = value.replace(/(\d)(\d{4})$/, '$1-$2');
        }
        e.target.value = value;
    });
}); 