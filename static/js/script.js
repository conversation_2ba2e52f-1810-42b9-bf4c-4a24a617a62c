function ShowPswd() {
    var inputPass = document.getElementById('password')
    var btnShowPass = document.getElementById('btn-senha')

    if (inputPass.type === 'password') {
        inputPass.setAttribute('type', 'text')
        btnShowPass.classList.replace('bi-eye-fill', 'bi-eye-slash-fill')
    }
    else {
        inputPass.setAttribute('type', 'password')
        btnShowPass.classList.replace('bi-eye-slash-fill', 'bi-eye-fill')
    }
}

document.addEventListener('DOMContentLoaded', function () {
    var togglePassword = document.getElementById('togglePassword');
    var toggleConfirmPassword = document.getElementById('toggleConfirmPassword');

    togglePassword.addEventListener('click', function () {
        var passwordInput = document.getElementById('senha');
        togglePasswordVisibility(passwordInput, this);
    });

    toggleConfirmPassword.addEventListener('click', function () {
        var confirmPasswordInput = document.getElementById('confirm_senha');
        togglePasswordVisibility(confirmPasswordInput, this);
    });

    function togglePasswordVisibility(input, icon) {
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.add('fa-eye-slash');
            icon.classList.remove('fa-eye');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }
});

function openUserModal() {
    document.getElementById('home-userModal').style.display = 'block';
    document.getElementById('home-float-box').style.display = 'none';
}

function closeUserModal() {
    document.getElementById('home-userModal').style.display = 'none';
    document.getElementById('home-float-box').style.display = 'block';
}

function openSupportForm() {
    var floatBox = document.getElementById('home-float-box');
    floatBox.innerHTML = `
    <h2>Formulário de Suporte</h2>
    <form id="support-form">
    <label for="username">Nome:</label>
    <input type="text" id="username" name="username" required><br>
    <label for="email">E-mail:</label>
    <input type="email" id="email" name="email" required><br>
    
    <label for="department">Departamento:</label>
    <input type="text" id="department" name="department" required><br>
    
    <label for="date">Data:</label>
    <input type="date" id="date" name="date" required><br>
    
    <label for="subject">Assunto:</label>
    <input type="text" id="subject" name="subject" required><br>
    
    <label for="description">Descrição:</label>
    <textarea id="description" name="description" required></textarea><br>
    
    <label for="file">Anexar Documento (imagem):</label>
    <input type="file" id="file" name="file" accept="image/*"><br>
    
    <button type="submit">Enviar</button>
</form>
`;
}

document.addEventListener('DOMContentLoaded', function () {
    var supportLink = document.getElementById('home-link-support');
    if (supportLink) {
        supportLink.addEventListener('click', function (event) {
            event.preventDefault();
            openSupportForm();
        });
    }
});

function openMultinotasForm() {
    var floatBox = document.getElementById('home-float-box');
    floatBox.innerHTML = `<h2>Envio de Documentos</h2>
    <form id="multinotas-form">
        <p><strong>Documentos a serem enviados:</strong></p>

        <label for="cnpj">CNPJ:</label><br>
        <input type="text" id="cnpj" name="cnpj" oninput="buscarRazaoSocialPorCNPJ()"><br>
        
        <label for="razao_social">Razão Social:</label><br>
        <input type="text" id="razao_social" name="razao_social" readonly><br>
        
        <label for="contrato_social">Contrato Social e/ou última alteração contratual consolidada:</label><br>
        <input type="file" id="contrato_social" name="contrato_social"><br>
        
        <label for="nota_fiscal">Nota Fiscal:</label><br>
        <input type="file" id="nota_fiscal" name="nota_fiscal"><br>
        
        <label for="cartao_cnpj">Cartão de CNPJ:</label><br>
        <input type="file" id="cartao_cnpj" name="cartao_cnpj"><br>
        
        <label for="comprovante_bancario">Folha de Cheque/Cópia Cartão Banco/Proposta Abertura de Conta:</label><br>
        <input type="file" id="comprovante_bancario" name="comprovante_bancario"><br>
        
        <label for="susep">SUSEP Pessoa Jurídica (CASO TENHA):</label><br>
        <input type="file" id="susep" name="susep"><br>
        
        <label for="comprovante_endereco_empresa">Comprovante de endereço em nome da Empresa/Corretora:</label><br>
        <input type="file" id="comprovante_endereco_empresa" name="comprovante_endereco_empresa"><br>
        
        <label for="cpf_rg_socios">CPF e RG do(s) sócio(s):</label><br>
        <input type="file" id="cpf_rg_socios" name="cpf_rg_socios" multiple><br>
        
        <label for="comprovante_residencia_socios">Comprovante de residência do(s) sócio(s):</label><br>
        <input type="file" id="comprovante_residencia_socios" name="comprovante_residencia_socios" multiple><br>
        
        <label for="declaracao_simples">Declaração Opção pelo Simples Digitalizado:</label><br>
        <input type="file" id="declaracao_simples" name="declaracao_simples"><br>
        
        <label for="comprovante_cpf">Comprovante de situação cadastral no CPF:</label><br>
        <input type="file" id="comprovante_cpf" name="comprovante_cpf"><br>
        
        <label for="ccm">CCM (Cadastro de Contribuinte Mobiliário):</label><br>
        <input type="file" id="ccm" name="ccm"><br>
        
        <label for="pis">PIS (Programa de Integração Social):</label><br>
        <input type="file" id="pis" name="pis"><br>
        
        <input type="submit" value="Enviar Documentos">

    </form>`;
}

document.addEventListener('DOMContentLoaded', function () {
    var multinotasLink = document.getElementById('home-link-multinotas');
    if (multinotasLink) {
        multinotasLink.addEventListener('click', function (event) {
            event.preventDefault();
            openMultinotasForm();
        });
    }
});
// --------------------------------------------------------------------------------------------------- MENU  --------------------------------------------------------------------------------------------------- //

// Função para alternar a visibilidade do dropdown do usuário
function toggleDropdown_user() {
    console.log("toggleDropdown_user ativado"); // Log para verificar se a função foi chamada
    document.getElementById("userDropdown").classList.toggle("show");
    // Feche o menuDropdown se estiver aberto quando o userDropdown for aberto
    var menuDropdown = document.getElementById("menuDropdown");
    if (menuDropdown.classList.contains("show")) {
        menuDropdown.classList.remove("show");
        document.querySelector(".menu-icon i").classList.remove("bi-x-lg");
        document.querySelector(".menu-icon i").classList.add("bi-list");
    }
}

// Função para alternar a visibilidade do menu dropdown e mudar o ícone
function toggleDropdown_m() {
    console.log("toggleDropdown_m ativado"); // Log para verificar se a função foi chamada
    var overlay = document.getElementById("overlay");
    var menuDropdown = document.getElementById("menuDropdown");
    var menuIcon = document.querySelector(".menu-icon i");

    // Verifica se o menu dropdown já está visível
    if (menuDropdown.classList.contains("show")) {
        console.log("Fechando menu dropdown"); // Log para verificar a ação de fechar o menu
        // Se estiver visível, remove a classe 'show' para esconder o menu
        menuDropdown.classList.remove("show");
        // Remove a classe 'show' do overlay para escondê-lo
        overlay.classList.remove("show");
        // Altera o ícone de 'x' para 'menu'
        menuIcon.classList.remove("bi-x-lg");
        menuIcon.classList.add("bi-list");
    } else {
        console.log("Abrindo menu dropdown"); // Log para verificar a ação de abrir o menu
        // Se não estiver visível, adiciona a classe 'show' para mostrar o menu
        menuDropdown.classList.add("show");
        // Adiciona a classe 'show' ao overlay para mostrá-lo
        overlay.classList.add("show");
        // Altera o ícone de 'menu' para 'x'
        menuIcon.classList.remove("bi-list");
        menuIcon.classList.add("bi-x-lg");
    }

    // Fecha o userDropdown se estiver aberto quando o menuDropdown for aberto
    var userDropdown = document.getElementById("userDropdown");
    if (userDropdown.classList.contains("show")) {
        userDropdown.classList.remove("show");
    }
}

// Função para fechar o menu ao clicar fora dele
document.getElementById("overlay").addEventListener("click", function () {
    console.log("Overlay clicado"); // Log para verificar se o clique no overlay foi detectado
    var overlay = document.getElementById("overlay"); // Seleciona o overlay
    var menuDropdown = document.getElementById("menuDropdown"); // Seleciona o menu dropdown
    var menuIcon = document.querySelector(".menu-icon i"); // Seleciona o ícone do menu

    // Remove a classe 'show' do overlay para escondê-lo
    overlay.classList.remove("show");
    // Remove a classe 'show' do menu dropdown para escondê-lo
    menuDropdown.classList.remove("show");
    // Altera o ícone de 'x' para 'menu'
    menuIcon.classList.remove("bi-x-lg");
    menuIcon.classList.add("bi-list");
});

// Gerenciando ambos os dropdowns com um único manipulador de evento
window.onclick = function (event) {
    console.log("Clique detectado fora do menu ou do dropdown do usuário"); // Log para verificar o clique fora do menu ou dropdown do usuário
    if (!event.target.matches('.home-user-icon-container, .fas.fa-user-circle')) {
        var userDropdowns = document.getElementsByClassName("home-user-dropdown-content");
        var i;
        for (i = 0; i < userDropdowns.length; i++) {
            var openDropdown = userDropdowns[i];
            if (openDropdown.classList.contains('show')) {
                openDropdown.classList.remove('show');
            }
        }
    }

    if (!event.target.matches('.menu-icon, .bi')) {
        var menuDropdowns = document.getElementsByClassName("header-dropdown-content");
        for (var i = 0; i < menuDropdowns.length; i++) {
            var openDropdown = menuDropdowns[i];
            if (openDropdown.classList.contains('show')) {
                openDropdown.classList.remove('show');
            }
        }
        // Mude o ícone do menu para o padrão se o menu estiver aberto
        var menuIcon = document.querySelector(".menu-icon i");
        if (menuIcon.classList.contains("bi-x-lg")) {
            menuIcon.classList.remove("bi-x-lg");
            menuIcon.classList.add("bi-list");
        }
    }
};
// --------------------------------------------------------------------------------------------------- MENU  --------------------------------------------------------------------------------------------------- //

// ------------------------------------------------------------------------------------------------- SUB MENU  ------------------------------------------------------------------------------------------------- //
// Função para ajustar a largura e posição dos submenus-dropdown
function adjustSubmenuPosition() {
    var submenuDropdowns = document.querySelectorAll('.submenu-dropdown');
    var initPage = document.getElementById('init-page');
    var initPageRect = initPage.getBoundingClientRect();
    var pnContainer = document.querySelector('.pn-container');
    var pnContainerRect = pnContainer.getBoundingClientRect();

    submenuDropdowns.forEach(function (submenu) {
        var parentRect = submenu.parentElement.getBoundingClientRect();
        var leftOffset = initPageRect.left - parentRect.left - 10; // Subtraindo 10 pixels adicionais

        submenu.style.left = leftOffset + 'px';
        submenu.style.width = pnContainerRect.width + 'px'; // Ajustando a largura do submenu-dropdown
    });
}

// Chame a função ao carregar a página e ao redimensionar a janela
window.onload = adjustSubmenuPosition;
window.onresize = adjustSubmenuPosition;

// Função para alternar o submenu
function toggleSubmenu(event, submenuId) {
    console.log("toggleSubmenu ativado para: " + submenuId);
    event.preventDefault();
    event.stopPropagation();

    var submenu = document.getElementById(submenuId);
    var submenus = document.getElementsByClassName("submenu");
    var link = event.currentTarget;
    var icon = link.querySelector('.bi-chevron-right') || link.querySelector('.bi-chevron-down');

    for (var i = 0; i < submenus.length; i++) {
        if (submenus[i] !== submenu) {
            submenus[i].classList.remove('show');
            var siblingLink = submenus[i].previousElementSibling;
            if (siblingLink) {
                var siblingIcon = siblingLink.querySelector('.bi-chevron-down');
                if (siblingIcon) {
                    siblingIcon.classList.remove('bi-chevron-down');
                    siblingIcon.classList.add('bi-chevron-right');
                }
            }
        }
    }

    var isSubmenuOpen = submenu.classList.contains('show');

    if (isSubmenuOpen) {
        console.log("Fechando submenu: " + submenuId);
        submenu.classList.remove('show');
        icon.classList.remove('bi-chevron-down');
        icon.classList.add('bi-chevron-right');
    } else {
        console.log("Abrindo submenu: " + submenuId);
        submenu.classList.add('show');
        icon.classList.remove('bi-chevron-right');
        icon.classList.add('bi-chevron-down');
    }

    adjustSubmenuPosition();
}

// Fechar submenus ao clicar fora deles
window.onclick = function (event) {
    if (!event.target.matches('.dropdown-link, .dropdown-link *, .submenu, .submenu *, .submenu-dropdown, .submenu-dropdown *')) {
        var submenus = document.getElementsByClassName("submenu");
        for (var i = 0; i < submenus.length; i++) {
            submenus[i].classList.remove('show');
            var currentIcon = submenus[i].previousElementSibling.querySelector('.bi-chevron-down');
            if (currentIcon) {
                currentIcon.classList.remove('bi-chevron-down');
                currentIcon.classList.add('bi-chevron-right');
            }
        }

        var submenuDropdowns = document.getElementsByClassName("submenu-dropdown");
        for (var j = 0; j < submenuDropdowns.length; j++) {
            submenuDropdowns[j].classList.remove('show');
            var dropdownIcon = submenuDropdowns[j].previousElementSibling.querySelector('.bi-chevron-down');
            if (dropdownIcon) {
                dropdownIcon.classList.remove('bi-chevron-down');
                dropdownIcon.classList.add('bi-chevron-right');
            }
        }
    }
};

// ------------------------------------------------------------------------------------------------- SUB MENU  ------------------------------------------------------------------------------------------------- //

document.addEventListener("DOMContentLoaded", function () {
    const changePasswordButton = document.querySelector(".change-password-btn");

    changePasswordButton.addEventListener("click", function (event) {
        event.preventDefault(); // Previne a ação padrão do botão (se houver).

        const currentPassword = document.getElementById("current-password").value;
        const newPassword = document.getElementById("new-password").value;
        const confirmPassword = document.getElementById("confirm-password").value;

        // Configuração dos dados a serem enviados.
        const formData = new FormData();
        formData.append('current-password', currentPassword);
        formData.append('new-password', newPassword);
        formData.append('confirm-password', confirmPassword);

        // Função async para enviar os dados para o servidor.
        async function enviarDados() {
            try {
                const response = await fetch('/change_password', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                // Tratamento da resposta.
                if (data.success) {
                    alert(data.message); // Mensagem de sucesso.
                    document.getElementById("form-change-pswd").reset();
                } else {
                    alert(data.message); // Mensagem de erro.
                }
            } catch (error) {
                console.error('Erro ao alterar a senha:', error);
                alert('Ocorreu um erro ao tentar alterar a senha. Por favor, tente novamente.');
            }
        }

        enviarDados();
    });
});

document.addEventListener('DOMContentLoaded', function () {
    var senhaInput = document.getElementById('senha') || document.getElementById('new-password');

    if (senhaInput) {
        senhaInput.addEventListener('input', function () {
            const senha = this.value;
            document.getElementById('req-length').style.color = senha.length >= 6 && senha.length <= 64 ? '#009900' : 'red';
            document.getElementById('req-letter').style.color = /[a-zA-Z]/.test(senha) ? '#009900' : 'red';
            document.getElementById('req-number').style.color = /[0-9]/.test(senha) ? '#009900' : 'red';
            document.getElementById('req-special').style.color = /[!@#$%&*]/.test(senha) ? '#009900' : 'red';
        });
    }
});

document.addEventListener("DOMContentLoaded", function () {
    document.getElementById('resendEmail').addEventListener('click', function (event) {
        event.preventDefault(); // Impede que o navegador siga o link

        fetch('/resend_confirmation_email', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Falha na solicitação: ' + response.statusText);
                }
                return response.json();
            })
            .then(data => {
                alert(data.message);
            })
            .catch(error => {
                console.error('Erro encontrado:', error);
                alert('Erro ao reenviar e-mail: ' + error.message);
            });
    });
});

document.addEventListener("DOMContentLoaded", function () {
    document.getElementById('resendEmailCE').addEventListener('click', function (event) {
        event.preventDefault(); // Impede que o navegador siga o link

        fetch('/resend_confirmation_email', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Falha na solicitação: ' + response.statusText);
                }
                return response.json();
            })
            .then(data => {
                alert(data.message);
            })
            .catch(error => {
                console.error('Erro encontrado:', error);
                alert('Erro ao reenviar e-mail: ' + error.message);
            });
    });
});

document.getElementById('cpf').addEventListener('input', function (e) {
    var input = e.target.value.replace(/\D/g, '');
    var tamanhoMaximo = 11;

    // Limita o tamanho do input
    if (input.length > tamanhoMaximo) {
        input = input.slice(0, tamanhoMaximo);
    }

    // Aplica a máscara de formatação do CPF: xxx.xxx.xxx-xx
    if (input.length > 9) {
        input = `${input.slice(0, 3)}.${input.slice(3, 6)}.${input.slice(6, 9)}-${input.slice(9, 11)}`;
    } else if (input.length > 6) {
        input = `${input.slice(0, 3)}.${input.slice(3, 6)}.${input.slice(6, 9)}`;
    } else if (input.length > 3) {
        input = `${input.slice(0, 3)}.${input.slice(3, 6)}`;
    }

    e.target.value = input;
});

function validarCPF(cpf) {
    cpf = cpf.replace(/\D/g, '');

    if (cpf.length !== 11 || [
        '00000000000', '11111111111', '22222222222', '33333333333',
        '44444444444', '55555555555', '66666666666', '77777777777',
        '88888888888', '99999999999'
    ].includes(cpf)) {
        return false;
    }

    let soma = 0, resto;
    for (let i = 1; i <= 9; i++)
        soma += parseInt(cpf.charAt(i - 1)) * (11 - i);
    resto = (soma * 10) % 11;

    if ((resto === 10) || (resto === 11)) resto = 0;
    if (resto !== parseInt(cpf.charAt(9))) return false;

    soma = 0;
    for (let i = 1; i <= 10; i++)
        soma += parseInt(cpf.charAt(i - 1)) * (12 - i);
    resto = (soma * 10) % 11;

    if ((resto === 10) || (resto === 11)) resto = 0;
    if (resto !== parseInt(cpf.charAt(10))) return false;

    return true;
}

document.getElementById('cpf').addEventListener('input', function () {
    const cpf = this.value;
    const cpfError = document.getElementById('cpf-error');
    if (cpf.length === 14) { // Considerando a formatação com pontos e traço
        if (!validarCPF(cpf)) {
            cpfError.style.display = 'block';
        } else {
            cpfError.style.display = 'none';
        }
    } else {
        cpfError.style.display = 'none';
    }
});

function aplicarMascaraCPF(inputCPF) {
    var input = inputCPF.value.replace(/\D/g, '');
    var tamanhoMaximo = 11;
    if (input.length > tamanhoMaximo) {
        input = input.slice(0, tamanhoMaximo);
    }
    if (input.length > 9) {
        input = `${input.slice(0, 3)}.${input.slice(3, 6)}.${input.slice(6, 9)}-${input.slice(9, 11)}`;
    } else if (input.length > 6) {
        input = `${input.slice(0, 3)}.${input.slice(3, 6)}.${input.slice(6, 9)}`;
    } else if (input.length > 3) {
        input = `${input.slice(0, 3)}.${input.slice(3, 6)}`;
    }
    inputCPF.value = input;
}

// Adiciona o listener de input a todos os campos de CPF
document.querySelectorAll('.cpf-input').forEach(inputCPF => {
    inputCPF.addEventListener('input', function (e) {
        aplicarMascaraCPF(e.target);
    });
});

// Função Cadastro da Proposta //
function openCadastroForm() {
    var floatBox = document.getElementById('home-float-box');
    floatBox.innerHTML = `
    <h2>Formulário de Adesão</h2>
    <form id="form-adesao" action="/caminho/para/processar-formulario" method="post" enctype="multipart/form-data">
<label for="nome-corretor">Nome do Corretor:</label>
<input type="text" id="nome-corretor" name="nomeCorretor" required>

<label for="tipo-contratacao">Tipo de Contratação:</label>
<select id="tipo-contratacao" name="tipoContratacao">
    <option value="PF">PF</option>
    <option value="PJ">PJ</option>
</select>

<label for="administradora">Administradora:</label>
<input type="text" id="administradora" name="administradora">

<label for="coparticipacao">Coparticipação:</label>
<select id="coparticipacao" name="coparticipacao">
    <option value="sim">Sim</option>
    <option value="nao">Não</option>
</select>

<label for="plano-odontologico">Plano Odontológico Incluso:</label>
<select id="plano-odontologico" name="planoOdontologico">
    <option value="sim">Sim</option>
    <option value="nao">Não</option>
</select>

<label for="contato-responsavel">Contato Responsável:</label>
<input type="text" id="contato-responsavel" name="contatoResponsavel" required>

<label for="email">E-mail:</label>
<input type="email" id="email" name="email" required>

<label for="telefone">Telefone:</label>
<input type="text" id="telefone" name="telefone" required>

<!-- Campos adicionais conforme necessário -->

<h3>Documentos para Implantação</h3>
<label for="documentos">Selecionar Arquivos:</label>
<input type="file" id="documentos" name="documentos" multiple required>

<button type="submit">Enviar Adesão</button>
    </form>
`;
}

function openEstudosForm() {
    console.log('Executando openEstudosForm...');
    var floatBox = document.getElementById('home-float-box');
    floatBox.style.overflowY = 'auto';
    floatBox.style.maxHeight = '700px';
    floatBox.innerHTML = `
    <div class="form-estudos">
    <form> 
    <header>
        <h1>Sistema de Gerenciamento de Estudos de Saúde</h1>
    </header>
    <section id="solicitacao-estudo">
        <h2>Solicitação de Estudo</h2>
        <label for="adm">ADM:</label>
    <input type="text" id="adm" name="adm"><br><br>

    <label for="empresa">Empresa:</label>
    <input type="text" id="empresa" name="empresa"><br><br>

    <label for="cnpj">CNPJ:</label>
    <input type="text" id="cnpj" name="cnpj"><br><br>

    <label for="planoAtual">Plano Atual:</label>
    <input type="text" id="planoAtual" name="planoAtual"><br><br>

    <label for="vidas">Vidas:</label>
    <input type="number" id="vidas" name="vidas"><br><br>

    <label for="tipoEstudo">Tipo de Estudo:</label>
    <input type="text" id="tipoEstudo" name="tipoEstudo"><br><br>

    <label for="porte">Porte:</label>
    <input type="text" id="porte" name="porte"><br><br>

    <label for="dataRecebimento">Data de Recebimento:</label>
    <input type="date" id="dataRecebimento" name="dataRecebimento"><br><br>

    <label for="mes">Mês:</label>
    <input type="text" id="mes" name="mes"><br><br>

    <label for="ano">Ano:</label>
    <input type="number" id="ano" name="ano"><br><br>

    <label for="origemEstudo">Origem do Estudo:</label>
    <input type="text" id="origemEstudo" name="origemEstudo"><br><br>

    <label for="executivo">Executivo:</label>
    <input type="text" id="executivo" name="executivo"><br><br>

    <label for="dataEnvioExecutivo">Data de Envio ao Executivo:</label>
    <input type="date" id="dataEnvioExecutivo" name="dataEnvioExecutivo"><br><br>

    <label for="prazoEstimadoOperadora">Prazo Estimado Operadora (dias):</label>
    <input type="number" id="prazoEstimadoOperadora" name="prazoEstimadoOperadora"><br><br>

    <label for="dataLimiteOperadora">Data Limite Operadora:</label>
    <input type="date" id="dataLimiteOperadora" name="dataLimiteOperadora"><br><br>

    <label for="planosCotados">Planos Cotados:</label>
    <input type="text" id="planosCotados" name="planosCotados"><br><br>

    <label for="observacoes">Observações:</label>
    <textarea id="observacoes" name="observacoes"></textarea><br><br>

<input type="submit" value="Enviar Solicitação">
        </div>
    </section>            
</form>
    </div>`;
}

function openReembolsoForm() {
    var floatBox = document.getElementById('home-float-box');
    floatBox.classList.add('scrollable');
    floatBox.innerHTML = `
      <h2>Formulário de Reembolso</h2>
      <form id="form-reembolso" action="/caminho/para/processar-reembolso" method="post">
        <label for="data">Data:</label>
        <input type="date" id="data" name="data" required>
  
        <label for="tipo-despesa">Tipo de Despesa:</label>
        <input type="text" id="tipo-despesa" name="tipoDespesa" required>
        
        <label for="departamento-despesa">Departamento da Despesa:</label>
        <input type="text" id="departamento-despesa" name="departamentoDespesa" required>
  
        <label for="motivo-visita">Motivo da Visita:</label>
        <textarea id="motivo-visita" name="motivoVisita" required></textarea>
        
        <label for="colaborador">Colaborador:</label>
        <input type="text" id="colaborador" name="colaborador" required>
  
        <h3>Dados Bancários para Reembolso</h3>
        
        <label for="banco">Banco:</label>
        <input type="text" id="banco" name="banco" required>
        
        <label for="agencia">Agência:</label>
        <input type="text" id="agencia" name="agencia" required>
        
        <label for="conta">Conta:</label>
        <input type="text" id="conta" name="conta" required>
  
        <label for="cpf">CPF:</label>
        <input type="text" id="cpf" name="cpf" required>
        
        <label for="valor-individual">Valor Individual/Gasto:</label>
        <input type="number" id="valor-individual" name="valorIndividual" step="0.01" required>
  
        <label for="descricao-servico">Descrição do Serviço Realizado:</label>
        <textarea id="descricao-servico" name="descricaoServico" required></textarea>
  
        <label for="valor-reembolso">Valor a Ser Reembolsado:</label>
        <input type="number" id="valor-reembolso" name="valorReembolso" step="0.01" required>
        
        <button type="submit">Enviar Reembolso</button>
      </form>
    `;
}

function openOrganograma() {
    var floatBox = document.getElementById('brazil-float-box');
    floatBox.innerHTML = `<div id="carrosselOrganograma" class="carrossel-organograma">
        <div class="carrossel-slides"></div>
        <a class="prev" onclick="mudarSlide(-1)">❮</a>
        <a class="next" onclick="mudarSlide(1)">❯</a>
        <div class="indicadores-slide"></div>
    </div>`;
    var slidesContainer = document.querySelector('.carrossel-slides');
    var indicadoresContainer = document.querySelector('.indicadores-slide');

    // Criando e inserindo as imagens e os indicadores de navegação
    for (var i = 1; i <= 19; i++) {
        var img = document.createElement('img');
        img.src = `/static/images/organograma/${i}.jpg`;
        slidesContainer.appendChild(img);

        var span = document.createElement('span');
        span.classList.add('indicador');
        (function (index) {
            span.addEventListener('click', function () {
                moverParaSlide(index);
            });
        })(i - 1);
        indicadoresContainer.appendChild(span);
    }

    // Iniciando o carrossel
    slides = document.querySelectorAll('.carrossel-slides img');
    var indicadores = document.querySelectorAll('.indicador');
    mostrarSlides(0); // Mostra o slide inicial

    // Adiciona a troca automática de slides
    setInterval(function () {
        mudarSlide(1);
    }, 10000); // Muda a imagem a cada 10 segundos
}

function mostrarSlides(n) {
    var i;

    if (n >= slides.length) { n = 0; }
    if (n < 0) { n = slides.length - 1; }

    for (i = 0; i < slides.length; i++) {
        slides[i].style.display = "none";
    }

    var indicadores = document.querySelectorAll('.indicador');
    for (i = 0; i < indicadores.length; i++) {
        indicadores[i].className = indicadores[i].className.replace(" active", "");
    }

    slides[n].style.display = "block";
    indicadores[n].className += " active";
    currentSlide = n; // Atualiza o índice do slide atual
}

function mudarSlide(n) {
    mostrarSlides(currentSlide += n);
}

function moverParaSlide(n) {
    mostrarSlides(n);
}

// Mascara de telefone.
function aplicarMascaraTelefone(element) {
    // Remove caracteres não numéricos
    let numero = element.value.replace(/\D/g, '');

    // Limita a entrada a 11 dígitos
    numero = numero.substring(0, 11);

    // Aplica a máscara
    numero = numero.replace(/^(\d{2})(\d)/g, "($1) $2")
        .replace(/(\d)(\d{4})$/, "$1-$2");

    element.value = numero;
}

// Java Script (Gestão - Clique nos cards)
function loadContent(content) {
    const contentContainer = document.getElementById('content-container');
    fetch(`/${content}`)
        .then(response => response.text())
        .then(html => {
            contentContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Erro ao carregar o conteúdo:', error);
        });
}

document.querySelectorAll('.card-gestao').forEach(card => {
    card.addEventListener('click', () => {
        const content = card.getAttribute('data-content');
        loadContent(content);
    });
});

