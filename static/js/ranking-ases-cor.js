$(document).ready(function () {
    console.log("Initializing DataTable...");

    // Função para atualizar a legenda de última atualização
    function updateLastExecutionTime() {
        $.ajax({
            url: "/api/last-execution",
            method: "GET",
            success: function (response) {
                if (response.status === 'success' && response.data) {
                    $('#last-update').html(`Última atualização: ${response.data.data}`);
                }
            },
            error: function (xhr, status, error) {
                console.log("Error fetching last execution time: ", error);
            }
        });
    }

    // Atualiza a legenda quando a página carrega
    updateLastExecutionTime();

    // Função de ordenação customizada para DataTables que entende o formato de moeda
    jQuery.extend(jQuery.fn.dataTableExt.oSort, {
        "currency-pre": function (a) {
            return parseFloat(a.replace(/[\R$,]/g, '').replace(/\./g, '').replace(',', '.'));
        },
        "currency-asc": function (a, b) {
            return a - b;
        },
        "currency-desc": function (a, b) {
            return b - a;
        }
    });

    function loadTables() {
        $.ajax({
            url: "/api/ranking_ases_corretores",
            method: "GET",
            success: function (data) {
                console.log("Ranking data loaded: ", data);

                $('#tables-container').empty();

                var tableId = 'ranking-table';
                var rowsHtml = '';

                data.forEach(item => {
                    let valor = parseFloat(item.vltotal.replace(',', ''));
                    if (valor >= 1000) {  // Filtrar valores menores que 1000
                        let metaNacional = item.meta_nacional;
                        let metaInternacional = item.meta_internacional;

                        // Calcular percentual atingido
                        let parcialGramado = metaNacional ? (valor / metaNacional * 100).toFixed(2) : 0;
                        let parcialMendonza = metaInternacional ? (valor / metaInternacional * 100).toFixed(2) : 0;

                        rowsHtml += `
                            <tr>
                                <td>${item.corretor}</td>
                                <td>${item.assistente}</td>
                                <td>${item.canal}</td>
                                <td class="text-accounting">R$ ${valor.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                                <td>${parcialGramado}%</td>
                                <td>${parcialMendonza}%</td>
                            </tr>
                        `;
                    }
                });

                var tableHtml = `
                    <div class="col-md-12 mb-4 table-container" style="display: flex; justify-content: center;">
                        <table id="${tableId}" class="display" style="width:100%">
                            <thead class="ranking-header">
                                <tr>
                                    <th class="ranking-column-header text-center" style="text-align: center; width: 850px;">Corretor</th>
                                    <th class="ranking-column-header text-center" style="text-align: center; width: 750px;">Assistente</th>
                                    <th class="ranking-column-header text-center" style="text-align: center; width: 200px;">Canal</th>
                                    <th class="ranking-column-header text-center" style="text-align: center; width: 250px;">Total</th>
                                    <th class="ranking-column-header text-center" style="text-align: center; width: 150px;">Parcial Gramado</th>
                                    <th class="ranking-column-header text-center" style="text-align: center; width: 150px;">Parcial Mendonza</th>
                                </tr>
                            </thead>
                            <tbody class="ranking-body">
                                ${rowsHtml}
                            </tbody>
                            <tfoot class="ranking-footer">
                                <tr>
                                    <th class="ranking-column-footer"></th>
                                    <th class="ranking-column-footer"></th>
                                    <th class="ranking-column-footer text-center" style="text-align: center;">Total Geral:</th>
                                    <th class="ranking-column-footer text-accounting" style="text-align: center;">
                                        R$ ${data.reduce((sum, item) => {
                    let valor = parseFloat(item.vltotal.replace(',', ''));
                    return valor >= 1000 ? sum + valor : sum;
                }, 0).toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                    </th>
                                    <th class="ranking-column-footer"></th>
                                    <th class="ranking-column-footer"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                `;
                $('#tables-container').append(tableHtml);

                var table = $('#' + tableId).DataTable({
                    paging: false,
                    searching: true,
                    info: false,
                    dom: 'Bfrtip',
                    buttons: [],
                    ordering: true,
                    order: [[3, 'desc']],
                    columnDefs: [
                        { targets: 3, type: 'currency' },  // Definir a coluna "Total" como moeda
                        { targets: 4, type: 'num' },  // Definir a coluna "Parcial Gramado" como numérica
                        { targets: 5, type: 'num' }   // Definir a coluna "Parcial Mendonza" como numérica
                    ]
                });

                // Adicionar um filtro por canal
                $('#tables-container').prepend(`
                    <div class="filter-container">
                        <label for="filter-canal">Filtrar por Canal:</label>
                        <select id="filter-canal">
                            <option value="">Todos</option>
                            ${[...new Set(data.map(item => item.canal))].map(canal => `<option value="${canal}">${canal}</option>`).join('')}
                        </select>
                    </div>
                `);

                $('#filter-canal').on('change', function () {
                    var selectedCanal = $(this).val();
                    if (selectedCanal) {
                        table.column(2).search('^' + selectedCanal + '$', true, false).draw();  // Filtrar pela coluna "Canal" (índice 2)
                    } else {
                        table.column(2).search('').draw();
                    }
                });

                // Estilos das linhas do corpo da tabela
                $('#' + tableId + ' tbody tr').css('background-color', '#013977').css('color', 'white');
                $('#' + tableId + ' tbody tr:nth-child(even)').css('background-color', '#f9f9f9').css('color', 'black');
                $('#' + tableId + ' tbody tr:hover').css('background-color', '#f1f1f1').css('color', 'black');
                $('#' + tableId + ' tbody tr').css('border-bottom', '1px solid white');

                // Estilos do cabeçalho da tabela
                $('#' + tableId + ' thead th').css('background-color', '#F2753D').css('color', 'white');
                $('#' + tableId + ' thead th[colspan="3"]').css({
                    'text-align': 'center', // Centraliza o texto do cabeçalho da operadora
                    'white-space': 'nowrap',
                    'overflow': 'hidden',
                    'text-overflow': 'ellipsis',
                    'margin-right': '10px'
                });

                // Estilos do rodapé da tabela
                $('#' + tableId + ' tfoot th').css('background-color', '#F2753D').css('color', 'white');
            },
            error: function (xhr, status, error) {
                console.log("Error fetching ranking data: ", xhr.responseText);
            }
        });
    }

    loadTables();
});
