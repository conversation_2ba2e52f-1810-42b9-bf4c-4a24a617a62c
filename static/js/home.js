/* ---------------------------------------------------------------- CRIAR INFORME ----------------------------------------------------------------*/
// Inicializa CKEditor fora do $(document).ready() para evitar dependência do jQuery
document.addEventListener('DOMContentLoaded', function() {
    initializeEditor();

    // Adicione o listener ao formulário
    const form = document.getElementById('informativo-form');
    if (form) {
        form.addEventListener('submit', function(event) {
            event.preventDefault();
            submitForm(this);
        });
    }
});

function initializeEditor() {
    if (typeof CKEDITOR === 'undefined') {
        console.warn('CKEditor não encontrado!');
        return;
    }

    CKEDITOR.replace('conteudo');
    CKEDITOR.config.height = 300;

    // Após a inicialização, anexa o evento para verificar a contagem de caracteres
    CKEDITOR.instances.conteudo.on('instanceReady', function () {
        this.document.on('keyup', function () {
            updateCharacterCounter();
        });
        this.document.on('paste', function () {
            setTimeout(updateCharacterCounter, 0); // Assegura a contagem após a conclusão do evento de colar
        });
    });
}

// Função para atualizar o contador de caracteres
function updateCharacterCounter() {
    var content = CKEDITOR.instances.conteudo.getData();
    var plainTextContent = CKEDITOR.instances.conteudo.document.getBody().getText(); // Obtém o texto sem tags HTML
    var contentLength = plainTextContent.length;
    var counterElement = document.getElementById('conteudo-contador');

    // Atualiza o contador no HTML
    if (counterElement) {
        counterElement.textContent = `${contentLength}/5000`;
    }

    // Se o conteúdo exceder 5000 caracteres, corta para 5000 e impede novas entradas
    if (contentLength > 5000) {
        var trimmedContent = plainTextContent.substring(0, 5000);
        CKEDITOR.instances.conteudo.setData(trimmedContent);
        counterElement.textContent = `5000/5000`;
        CKEDITOR.instances.conteudo.fire('change');
    }
}

function submitForm(form) {
    const formData = new FormData(form);
    formData.set('conteudo', CKEDITOR.instances.conteudo.getData());

    const xhr = new XMLHttpRequest();
    xhr.open('POST', form.action, true);

    xhr.onload = function() {
        if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
                alert('Informativo publicado com sucesso!');
                window.location.href = '/';
            } else {
                alert('Erro ao publicar o informativo.');
            }
        } else {
            alert('Erro ao publicar o informativo.');
        }
    };

    xhr.onerror = function() {
        alert('Erro na conexão ao publicar o informativo.');
    };

    xhr.send(formData);
}
/* ---------------------------------------------------------------- CARREGAR INFORME ----------------------------------------------------------------*/
$(document).ready(function() {
    var informativosUrl = $('body').data('informativos-url');
    console.log("URL dos Informativos:", informativosUrl);

    function fetchInformativos() {
        $.ajax({
            url: informativosUrl,
            type: 'GET',
            success: function(response) {
                $('#informativos-list').html(response);
                console.log("Informativos carregados com sucesso.");
            },
            error: function(xhr, status, error) {
                console.error('Erro ao buscar informativos:', status, error);
            }
        });
    }

    initializeEditor();
    fetchInformativos();  // Chama ao carregar a página
});

/* ---------------------------------------------------------------- APAGAR INFORME ----------------------------------------------------------------*/
function confirmDelete(element) {
    var informativoId = element.getAttribute('data-id');
    if (confirm('Realmente deseja excluir este informativo?')) {
        $.ajax({
            url: '/excluir_informativo/' + informativoId,
            type: 'DELETE', // Aqui deve ser type, não method
            success: function(response) {
                alert('Informativo excluído com sucesso!');
                location.reload(); // Recarrega a página para refletir a mudança
            },
            error: function(xhr) {
                alert('Erro ao excluir informativo: ' + xhr.responseText);
            }
        });
    }
}

$(document).ready(function() {
    $('#informativosCarousel').carousel({
        interval: false, // Desabilita a rotação automática
        wrap: true      // Habilita loop contínuo
    });
});

/* ---------------------------------------------------------------- Modal ----------------------------------------------------------------*/
