$(document).ready(function () {
    let table;
    let periodosSelecionados = [];
    let dadosCompletos = [];

    // Função para atualizar a legenda de última atualização
    function updateLastExecutionTime() {
        $.ajax({
            url: "/api/last-execution",
            method: "GET",
            success: function (response) {
                if (response.status === 'success' && response.data) {
                    $('#last-update').html(`Última atualização: ${response.data.data}`);
                }
            },
            error: function (xhr, status, error) {
                console.log("Error fetching last execution time: ", error);
            }
        });
    }

    // Atualiza a legenda quando a página carrega
    updateLastExecutionTime();

    // Cria div de debug
    const debugContainer = $('<div>')
        .attr('id', 'debug-container')
        .addClass('mt-4 p-3 border')
        .css('display', 'none')
        .appendTo('.ranking-container');

    // Inicializa o seletor de mês/ano
    function initMonthYearPicker() {
        // Inicialmente não faz nada - a configuração real acontece após carregar os dados
        console.log("Seletor de períodos será inicializado quando os dados forem carregados");
    }

    // Função para obter dados da API
    function carregarDados() {
        console.log("Carregando dados da API...");

        // Exibe mensagem de carregamento
        $('#metasOperadorasTable tbody').html('<tr><td colspan="5" class="text-center">Carregando dados...</td></tr>');

        // Limpa o debug
        debugContainer.html('<h4>Debug de Dados</h4><div id="raw-response"></div>');

        $.ajax({
            url: '/api/metas-vidas-operadora',
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                console.log("Resposta da API:", response);

                // Mostra resposta bruta para debug
                $('#raw-response').html(
                    '<h5>Resposta da API:</h5>' +
                    '<pre>' + JSON.stringify(response, null, 2) + '</pre>'
                );

                if (response.status === 'success' && response.data) {
                    dadosCompletos = response.data;
                    console.log("Dados carregados:", dadosCompletos);

                    if (dadosCompletos.length === 0) {
                        $('#metasOperadorasTable tbody').html('<tr><td colspan="5" class="text-center">Nenhum dado encontrado</td></tr>');
                        return;
                    }

                    // Verifica e exibe um exemplo de estrutura de dados
                    if (dadosCompletos.length > 0) {
                        $('#debug-container').append(
                            '<h5>Exemplo de item bruto:</h5>' +
                            '<pre>' + JSON.stringify(dadosCompletos[0], null, 2) + '</pre>'
                        );
                    }

                    // Ajusta a estrutura dos dados conforme a API retorna
                    dadosCompletos = dadosCompletos.map(item => {
                        // Mapeia os nomes das colunas retornadas pela API para o formato esperado
                        const formattedItem = {
                            operadora: item['Operadora'] || "Desconhecida",
                            meta: parseInt(item['Meta de Vidas']) || 0,
                            realizado: parseInt(item['Vidas Vendidas']) || 0,
                            periodo: item['Mês/Ano'] || "",
                        };

                        // Calcula o percentual (se não estiver já calculado)
                        const diferencaMeta = item['Diferença da Meta'];
                        if (diferencaMeta) {
                            // Remove o símbolo de porcentagem e converte para número
                            const diferencaValor = parseFloat(diferencaMeta.replace('%', ''));

                            // A diferença já é em relação à meta, então precisamos fazer o cálculo correto
                            // Se a diferença é -10%, significa que atingimos 90% da meta
                            formattedItem.percentual = 100 + diferencaValor;
                        } else if (formattedItem.meta > 0) {
                            formattedItem.percentual = (formattedItem.realizado / formattedItem.meta) * 100;
                        } else {
                            formattedItem.percentual = 0;
                        }

                        // Define o status com base no percentual
                        formattedItem.status = formattedItem.percentual >= 100 ? 'Atingida' : 'Não Atingida';

                        return formattedItem;
                    });

                    // Exibe exemplo de item após ajuste
                    if (dadosCompletos.length > 0) {
                        $('#debug-container').append(
                            '<h5>Exemplo após ajuste:</h5>' +
                            '<pre>' + JSON.stringify(dadosCompletos[0], null, 2) + '</pre>'
                        );
                    }

                    // Agrupa os períodos únicos para o filtro
                    const periodosUnicos = [...new Set(dadosCompletos.map(item => item.periodo))];

                    // Ordena os períodos do mais recente para o mais antigo
                    periodosUnicos.sort((a, b) => {
                        // Converte para o formato "MM/YYYY" e depois compara
                        const [mesA, anoA] = a.split('/');
                        const [mesB, anoB] = b.split('/');

                        // Compara anos primeiro
                        if (anoA !== anoB) {
                            return anoB - anoA; // Ordem decrescente (mais recente primeiro)
                        }

                        // Se os anos são iguais, compara os meses
                        return mesB - mesA; // Ordem decrescente de meses
                    });

                    $('#debug-container').append(
                        '<h5>Períodos disponíveis (ordenados):</h5>' +
                        '<pre>' + JSON.stringify(periodosUnicos, null, 2) + '</pre>'
                    );

                    // Atualiza o seletor de períodos com os períodos disponíveis
                    atualizarSeletorPeriodos(periodosUnicos);

                    // Atualiza a tabela com os dados
                    atualizarTabela();
                } else {
                    console.error("Erro na resposta da API:", response.message || "Formato de resposta inválido");
                    $('#metasOperadorasTable tbody').html('<tr><td colspan="5" class="text-center">Erro ao carregar dados: ' +
                        (response.message || "Formato de resposta inválido") + '</td></tr>');
                }
            },
            error: function (xhr, status, error) {
                console.error("Erro na requisição:", error);
                console.error("Status:", status);
                console.error("Response:", xhr.responseText);

                $('#raw-response').html(
                    '<h5>Erro na requisição:</h5>' +
                    '<p>Status: ' + status + '</p>' +
                    '<p>Erro: ' + error + '</p>' +
                    '<pre>' + xhr.responseText + '</pre>'
                );

                $('#metasOperadorasTable tbody').html('<tr><td colspan="5" class="text-center">Erro na requisição: ' + error + '</td></tr>');
            }
        });
    }

    // Função para atualizar o seletor de períodos
    function atualizarSeletorPeriodos(periodos) {
        // Limpa opções atuais
        $('#monthYearPicker').empty();

        // Ordena os períodos do mais recente para o mais antigo (caso ainda não estejam ordenados)
        periodos.sort((a, b) => {
            // Converte para o formato "MM/YYYY" e depois compara
            const [mesA, anoA] = a.split('/');
            const [mesB, anoB] = b.split('/');

            // Compara anos primeiro
            if (anoA !== anoB) {
                return anoB - anoA; // Ordem decrescente (mais recente primeiro)
            }

            // Se os anos são iguais, compara os meses
            return mesB - mesA; // Ordem decrescente de meses
        });

        // Adiciona as opções com os períodos da API
        periodos.forEach(periodo => {
            const meses = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
                'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];

            // Tenta obter mês e ano do formato MM/YYYY
            let label = periodo;
            try {
                const [mes, ano] = periodo.split('/');
                if (mes && ano && !isNaN(mes) && !isNaN(ano)) {
                    const mesIndex = parseInt(mes) - 1;
                    if (mesIndex >= 0 && mesIndex < 12) {
                        label = `${meses[mesIndex]}/${ano}`;
                    }
                }
            } catch (e) {
                console.error("Erro ao formatar período:", e);
            }

            $('#monthYearPicker').append($('<option>', {
                value: periodo,
                text: label
            }));
        });

        // Destroy o multiselect existente se houver
        if ($('#monthYearPicker').hasClass('multiselect-initialized')) {
            $('#monthYearPicker').multiselect('destroy');
        }

        // Atualiza o multiselect com a configuração correta
        $('#monthYearPicker').multiselect({
            includeSelectAllOption: true,
            selectAllText: 'Todos os períodos',
            nonSelectedText: 'Selecione os períodos',
            nSelectedText: 'períodos selecionados',
            allSelectedText: 'Todos os períodos selecionados',
            buttonClass: 'btn btn-outline-primary',
            maxHeight: 300,
            enableFiltering: true,
            filterPlaceholder: 'Buscar período',
            enableCaseInsensitiveFiltering: true,
            buttonText: function (options, select) {
                if (options.length === 0) {
                    return 'Selecione os períodos';
                }
                else if (options.length > 1) {
                    return options.length + ' períodos selecionados';
                }
                else {
                    // Exibe o texto do período selecionado
                    return $(options[0]).text();
                }
            }
        }).addClass('multiselect-initialized');

        // Adiciona o evento de mudança
        $('#monthYearPicker').off('change').on('change', function () {
            periodosSelecionados = $(this).val() || [];
            atualizarTabela();
        });

        // Seleciona o primeiro período por padrão
        if (periodos.length > 0) {
            // Obtém o mês atual no formato MM/YYYY
            const dataAtual = new Date();
            const mesAtual = String(dataAtual.getMonth() + 1).padStart(2, '0');
            const anoAtual = dataAtual.getFullYear();
            const periodoAtual = `${mesAtual}/${anoAtual}`;

            // Procura o período atual na lista
            if (periodos.includes(periodoAtual)) {
                periodosSelecionados = [periodoAtual];
            } else {
                // Se não encontrar o período atual, seleciona o mais recente
                periodosSelecionados = [periodos[0]];
            }

            $('#monthYearPicker').val(periodosSelecionados);
            $('#monthYearPicker').multiselect('refresh');
            atualizarTabela();
        }
    }

    // Inicializa a tabela DataTable
    function initDataTable(dados) {
        console.log("Inicializando tabela com dados:", dados);

        if (table) {
            table.destroy();
        }

        if (!dados || dados.length === 0) {
            $('#metasOperadorasTable tbody').html('<tr><td colspan="5" class="text-center">Nenhum dado disponível para o período selecionado</td></tr>');
            return;
        }

        table = $('#metasOperadorasTable').DataTable({
            data: dados,
            columns: [
                { data: 'operadora' },
                { data: 'meta', render: $.fn.dataTable.render.number('.', ',', 0) },
                { data: 'realizado', render: $.fn.dataTable.render.number('.', ',', 0) },
                {
                    data: 'percentual',
                    render: function (data) {
                        return parseFloat(data).toFixed(2) + '%';
                    }
                },
                {
                    data: 'status',
                    render: function (data) {
                        if (data === 'Atingida') {
                            return '<span class="mensagem-verde">' + data + '</span>';
                        } else {
                            return '<span class="mensagem-vermelha">' + data + '</span>';
                        }
                    }
                }
            ],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Portuguese-Brasil.json'
            },
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            // Configura o footer para mostrar totais
            footerCallback: function (row, data, start, end, display) {
                const api = this.api();

                // Remove formatação para calcular
                const intVal = function (i) {
                    return typeof i === 'string' ?
                        i.replace(/[\$,]/g, '') * 1 :
                        typeof i === 'number' ? i : 0;
                };

                // Total meta
                const totalMeta = api
                    .column(1)
                    .data()
                    .reduce((a, b) => intVal(a) + intVal(b), 0);

                // Total realizado
                const totalRealizado = api
                    .column(2)
                    .data()
                    .reduce((a, b) => intVal(a) + intVal(b), 0);

                // Calcula percentual total
                const percentualTotal = totalMeta > 0 ?
                    (totalRealizado / totalMeta) * 100 : 0;

                // Status total
                const statusTotal = percentualTotal >= 100 ? 'Atingida' : 'Não Atingida';
                const statusClass = percentualTotal >= 100 ? 'mensagem-verde' : 'mensagem-vermelha';

                // Atualiza o footer
                $(api.column(0).footer()).html('Total Geral');
                $(api.column(1).footer()).html(totalMeta.toLocaleString('pt-BR'));
                $(api.column(2).footer()).html(totalRealizado.toLocaleString('pt-BR'));
                $(api.column(3).footer()).html(percentualTotal.toFixed(2) + '%');
                $(api.column(4).footer()).html('<span class="' + statusClass + '">' + statusTotal + '</span>');
            }
        });

        // Move os botões para o container
        $('.dt-buttons').detach().appendTo('#buttons-container');
    }

    // Filtra os dados conforme períodos selecionados
    function atualizarTabela() {
        console.log("Atualizando tabela com períodos:", periodosSelecionados);

        if (dadosCompletos.length === 0) {
            console.log("Sem dados para exibir");
            return;
        }

        // Se nenhum período selecionado, limpa a tabela e não exibe nada
        if (periodosSelecionados.length === 0) {
            console.log("Nenhum período selecionado, não exibindo dados");
            $('#metasOperadorasTable tbody').html('<tr><td colspan="5" class="text-center">Selecione um período para visualizar os dados</td></tr>');
            // Limpa também os gráficos
            $('#graficos-metas-operadoras').empty().html('<p>Selecione um período para visualizar os gráficos</p>');
            // Remove o texto informativo se existir
            $('#info-periodos').remove();
            return;
        }

        let dadosFiltrados = [];

        // Filtra os dados pelos períodos selecionados
        if (periodosSelecionados.includes('all')) {
            // Se "todos" estiver selecionado, mostra todos
            dadosFiltrados = dadosCompletos;
        } else {
            // Filtra pelos períodos selecionados
            dadosFiltrados = dadosCompletos.filter(item => {
                return periodosSelecionados.includes(item.periodo);
            });

            // Se não encontrar nada com os filtros, exibe mensagem
            if (dadosFiltrados.length === 0) {
                console.log("Nenhum dado encontrado com os filtros selecionados");
                $('#metasOperadorasTable tbody').html('<tr><td colspan="5" class="text-center">Nenhum dado disponível para o período selecionado</td></tr>');
                $('#graficos-metas-operadoras').empty().html('<p>Nenhum dado disponível para o período selecionado</p>');
                // Remove o texto informativo se existir
                $('#info-periodos').remove();
                return;
            }
        }

        console.log("Dados filtrados:", dadosFiltrados);

        // Atualiza ou remove o texto informativo sobre a soma dos períodos
        $('#info-periodos').remove();
        if (periodosSelecionados.length > 1) {
            // Cria texto informativo
            $('<div id="info-periodos" class="alert alert-info mt-2 mb-2">')
                .html('<strong>Nota:</strong> Os valores estão sendo somados para todos os períodos selecionados.')
                .insertBefore('#metasOperadorasTable');
        }

        // Se há mais de um período selecionado, agrega os dados
        if (periodosSelecionados.length > 1) {
            console.log("Múltiplos períodos selecionados, somando dados por operadora");

            // Objeto para armazenar os dados somados por operadora
            const dadosSomados = {};

            // Percorre todos os dados filtrados
            dadosFiltrados.forEach(item => {
                const operadora = item.operadora;

                // Se a operadora ainda não está no objeto, inicializa
                if (!dadosSomados[operadora]) {
                    dadosSomados[operadora] = {
                        operadora: operadora,
                        meta: 0,
                        realizado: 0
                    };
                }

                // Soma os valores
                dadosSomados[operadora].meta += item.meta;
                dadosSomados[operadora].realizado += item.realizado;
            });

            // Converte o objeto em array e calcula percentuais
            dadosFiltrados = Object.values(dadosSomados).map(item => {
                // Calcula o percentual
                item.percentual = item.meta > 0 ? (item.realizado / item.meta) * 100 : 0;

                // Define o status
                item.status = item.percentual >= 100 ? 'Atingida' : 'Não Atingida';

                return item;
            });

            console.log("Dados somados:", dadosFiltrados);
        }

        // Reinicializa a tabela com os dados filtrados
        initDataTable(dadosFiltrados);

        // Atualiza os gráficos
        atualizarGraficos(dadosFiltrados);
    }

    // Atualiza os gráficos conforme dados filtrados
    function atualizarGraficos(dados) {
        const container = $('#graficos-metas-operadoras');
        container.empty();

        if (dados.length === 0) {
            container.html('<p>Nenhum dado disponível para o período selecionado.</p>');
            return;
        }

        // Criar div para o gráfico
        container.html('<div id="grafico-barras-operadoras" style="width: 100%; height: 400px;"></div>');

        // Preparar dados para o gráfico
        const operadoras = [];
        const metasDados = [];
        const realizadoDados = [];

        dados.forEach(item => {
            operadoras.push(item.operadora);
            metasDados.push(item.meta);
            realizadoDados.push(item.realizado);
        });

        // Criar gráfico usando Chart.js (se disponível)
        if (typeof Chart !== 'undefined') {
            new Chart(document.getElementById('grafico-barras-operadoras').getContext('2d'), {
                type: 'bar',
                data: {
                    labels: operadoras,
                    datasets: [
                        {
                            label: 'Meta',
                            data: metasDados,
                            backgroundColor: 'rgba(54, 162, 235, 0.5)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Realizado',
                            data: realizadoDados,
                            backgroundColor: 'rgba(255, 99, 132, 0.5)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        } else {
            container.append('<p>Biblioteca Chart.js não disponível.</p>');
        }
    }

    // Inicializa os componentes
    initMonthYearPicker();
    carregarDados();
}); 