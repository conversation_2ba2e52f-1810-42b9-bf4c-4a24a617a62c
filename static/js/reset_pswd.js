document.addEventListener('DOMContentLoaded', function () {
    var togglePassword = document.getElementById('togglePassword');
    var toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
    var resetPasswordForm = document.getElementById('reset-password-form');
    var errorMessage = document.getElementById('error-message');

    togglePassword.addEventListener('click', function () {
        var passwordInput = document.getElementById('new-password');
        togglePasswordVisibility(passwordInput, this);
    });

    toggleConfirmPassword.addEventListener('click', function () {
        var confirmPasswordInput = document.getElementById('confirm-password');
        togglePasswordVisibility(confirmPasswordInput, this);
    });

    function togglePasswordVisibility(input, icon) {
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.add('fa-eye-slash');
            icon.classList.remove('fa-eye');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    resetPasswordForm.addEventListener('submit', function (event) {
        var newPassword = document.getElementById('new-password').value;
        var confirmPassword = document.getElementById('confirm-password').value;

        if (newPassword !== confirmPassword) {
            event.preventDefault();
            errorMessage.textContent = 'As senhas não coincidem.';
        }
    });

    var senhaInput = document.getElementById('new-password');

    if (senhaInput) {
        senhaInput.addEventListener('input', function () {
            const senha = this.value;
            document.getElementById('req-length').style.color = senha.length >= 6 && senha.length <= 64 ? '#009900' : 'red';
            document.getElementById('req-letter').style.color = /[a-zA-Z]/.test(senha) ? '#009900' : 'red';
            document.getElementById('req-number').style.color = /[0-9]/.test(senha) ? '#009900' : 'red';
            document.getElementById('req-special').style.color = /[!@#$%&*]/.test(senha) ? '#009900' : 'red';
        });
    }
});
