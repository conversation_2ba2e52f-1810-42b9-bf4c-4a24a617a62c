// Função para obter os parâmetros da URL
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(window.location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// Função para formatar a string de data YYYY-MM-DD para d/m/Y
function formatDateToDMY(dateStr) {
    const [year, month, day] = dateStr.split('-');
    return `${day}/${month}/${year}`;
}

// Inicializa o Flatpickr para seleção de intervalo de datas
function initializeFlatpickr(startDateStr, endDateStr) {
    flatpickr("#dateRangePicker", {
        mode: "range",
        dateFormat: "d/m/Y",
        locale: "pt",  // Usando o locale corrigido
        defaultDate: [formatDateToDMY(startDateStr), formatDateToDMY(endDateStr)],  // Define a seleção inicial
        onClose: function (selectedDates) {
            if (selectedDates.length === 2) {
                const startDate = selectedDates[0].toISOString().split('T')[0];
                const endDate = selectedDates[1].toISOString().split('T')[0];
                filtrarPorDatas(startDate, endDate); // Atualiza o período de filtro com as novas datas
            }
        }
    });

    // Atualiza o input com as datas no formato d/m/Y
    document.getElementById("dateRangePicker").value = `${formatDateToDMY(startDateStr)} até ${formatDateToDMY(endDateStr)}`;
}

// Calcula o início e o fim da semana atual (domingo a sábado)
function getCurrentWeekRange() {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 (Domingo) até 6 (Sábado)

    // Calcula o início (domingo)
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - dayOfWeek);

    // Calcula o fim (sábado)
    const endOfWeek = new Date(today);
    endOfWeek.setDate(today.getDate() + (6 - dayOfWeek));

    // Formata como YYYY-MM-DD
    const startDateStr = startOfWeek.toISOString().split('T')[0];
    const endDateStr = endOfWeek.toISOString().split('T')[0];

    return {
        start: startDateStr,
        end: endDateStr
    };
}

// Verifica se há datas nos parâmetros da URL
const startDateParam = getUrlParameter('start_date');
const endDateParam = getUrlParameter('end_date');

// Define as datas padrão: semana atual ou datas da URL
let defaultStartDate, defaultEndDate;

if (startDateParam && endDateParam) {
    // Usa as datas da URL se existirem
    defaultStartDate = startDateParam;
    defaultEndDate = endDateParam;
} else {
    // Usa a semana atual se não houver datas na URL
    const weekRange = getCurrentWeekRange();
    defaultStartDate = weekRange.start;
    defaultEndDate = weekRange.end;
}

// Inicializa o Flatpickr com as datas calculadas ou da URL
initializeFlatpickr(defaultStartDate, defaultEndDate);

// Função para abrir o modal com os detalhes da solicitação
function openModal(card) {
    // Limpar o modal antes de preencher os novos dados
    limparModal();

    const solicitacaoId = card.id;  // Pegamos o ID do card

    fetch(`/detalhes-fastmoney/${solicitacaoId}`)
        .then(response => response.json())
        .then(solicitacao => {
            if (solicitacao.error) {
                alert(solicitacao.error);
            } else {
                // Preencher os detalhes da solicitação
                document.getElementById("modal-corretor").innerText = solicitacao.corretor;
                document.getElementById("modal-supervisor").innerText = solicitacao.supervisor;
                document.getElementById("modal-cod_proposta").innerText = solicitacao.cod_proposta;
                document.getElementById("modal-segurado").innerText = solicitacao.segurado || 'N/A';
                document.getElementById("modal-operadora").innerText = solicitacao.operadora;
                document.getElementById("modal-valor").innerText = solicitacao.valor_proposta;
                document.getElementById("modal-modalidade").innerText = solicitacao.modalidade;
                document.getElementById("modal-tipo_adiantamento").innerText = solicitacao.tipo_adiantamento === 'primeira_parcela' ? 'Devolução de Primeira' : 'Fast Money';
                document.getElementById("modal-acordo").innerText = solicitacao.acordo;
                document.getElementById("modal-tabela").innerText = solicitacao.tabela_padrao;
                document.getElementById("modal-sub_produto").innerText = solicitacao.sub_produto;

                // Adicionar motivo e comentário de rejeição
                if (solicitacao.status === 'Rejeitado') {
                    document.getElementById('motivoRejeicaoDetalhes').innerText = solicitacao.motivo_rejeicao || 'Não informado';
                    document.getElementById('comentarioRejeicaoDetalhes').innerText = solicitacao.comentario_rejeicao || 'Não informado';
                    document.getElementById('rejection-section').style.display = 'block';  // Mostra a seção
                } else {
                    document.getElementById('rejection-section').style.display = 'none';  // Oculta a seção
                }

                // Preencher os links de comprovante e boleto
                const comprovanteUrl = solicitacao.comprovante_link;
                if (comprovanteUrl) {
                    const comprovanteNome = comprovanteUrl.replace('https://intranet-picture-profile.s3.amazonaws.com/', '');
                    document.getElementById("modal-comprovante").href = comprovanteUrl;
                    document.getElementById("modal-comprovante").innerText = comprovanteNome; 
                } else {
                    document.getElementById("modal-comprovante").removeAttribute('href');
                    document.getElementById("modal-comprovante").innerText = "Nenhum comprovante disponível";
                }

                const boletoUrl = solicitacao.boleto_link;
                if (boletoUrl) {
                    const boletoNome = boletoUrl.replace('https://intranet-picture-profile.s3.amazonaws.com/', '');
                    document.getElementById("modal-boleto").href = boletoUrl;
                    document.getElementById("modal-boleto").innerText = boletoNome; 
                } else {
                    document.getElementById("modal-boleto").removeAttribute('href');
                    document.getElementById("modal-boleto").innerText = "Nenhum boleto disponível";
                }

                // Verifica o status da solicitação e exibe o ribbon correspondente
                const ribbonContainer = document.getElementById("ribbon-container");
                const ribbonText = document.getElementById("ribbon-text");

                if (solicitacao.status === "Aprovado") {
                    ribbonContainer.className = 'ribbon ribbon-approved';
                    ribbonText.innerText = 'Aprovada';
                    ribbonContainer.style.display = 'block';
                } else if (solicitacao.status === "Rejeitado") {
                    ribbonContainer.className = 'ribbon ribbon-rejected';
                    ribbonText.innerText = 'Rejeitada';
                    ribbonContainer.style.display = 'block';
                } else {
                    ribbonContainer.className = 'ribbon ribbon-pending';
                    ribbonText.innerText = 'Pendente';
                    ribbonContainer.style.display = 'block';
                }

                // Exibe ou oculta os botões de Aprovar/Rejeitar com base no status
                const approveBtn = document.querySelector(".approve-btn");
                const rejectBtn = document.querySelector(".reject-btn");

                if (approveBtn && rejectBtn) {
                    if (solicitacao.status === "Aprovado" || solicitacao.status === "Rejeitado") {
                        approveBtn.style.display = 'none';
                        rejectBtn.style.display = 'none';
                    } else {
                        approveBtn.style.display = 'inline-block';
                        rejectBtn.style.display = 'inline-block';
                        // Adiciona evento ao botão "Rejeitar"
                        rejectBtn.onclick = function () {
                            showRejectionFields();  // Exibe os campos de rejeição
                        };
                    }
                }

                // Exibe o modal
                document.getElementById("modal").style.display = "flex"; 
            }
        })
        .catch(error => console.error('Erro ao buscar detalhes da solicitação:', error));

    document.getElementById("modal").setAttribute("data-active-card", card.id);
}

// Função para limpar o modal
function limparModal() {
    document.getElementById("modal-corretor").innerText = "";
    document.getElementById("modal-supervisor").innerText = "";
    document.getElementById("modal-cod_proposta").innerText = "";
    document.getElementById("modal-segurado").innerText = "N/A";
    document.getElementById("modal-operadora").innerText = "";
    document.getElementById("modal-valor").innerText = "";
    document.getElementById("modal-modalidade").innerText = "";
    document.getElementById("modal-tipo_adiantamento").innerText = "";
    document.getElementById("modal-acordo").innerText = "";
    document.getElementById("modal-tabela").innerText = "";
    document.getElementById("modal-sub_produto").innerText = "";
    document.getElementById("modal-comprovante").innerText = "Nenhum comprovante disponível";
    document.getElementById("modal-comprovante").removeAttribute("href");
    document.getElementById("modal-boleto").innerText = "Nenhum boleto disponível";
    document.getElementById("modal-boleto").removeAttribute("href");
    document.getElementById("comentarioRejeicao").value = "";
    document.getElementById("rejection-section").style.display = "none"; // Esconde a seção de rejeição

    // Limpa o ribbon no modal
    document.getElementById("ribbon-container").style.display = "none";
    document.getElementById("ribbon-text").innerText = "";
}

// Função para fechar o modal e limpar o conteúdo
function closeModal() {
    document.getElementById('modal').style.display = 'none';
    document.getElementById('rejection-section').style.display = 'none';  // Oculta os campos novamente ao fechar o modal
}

// Função para aprovar a solicitação e mover o card para a coluna de aprovados
function approveRequest() {
    const cardId = document.getElementById("modal").getAttribute("data-active-card");
    const card = document.getElementById(cardId);

    // Move o card para a coluna de aprovados
    const approvedColumn = document.querySelector('.approved');
    approvedColumn.appendChild(card);

    // Atualiza o status no backend
    atualizarStatus(cardId, 'Aprovado');

    // Fecha o modal
    closeModal();
}

// Função para rejeitar a solicitação
function rejectRequest() {
    const cardId = document.getElementById("modal").getAttribute("data-active-card");
    const card = document.getElementById(cardId);
    document.querySelector('.rejected').appendChild(card);

    atualizarStatus(cardId, 'Rejeitado');  // Atualiza o status no backend
    closeModal();
}

// Função para atualizar o status no backend e também no front-end
function atualizarStatus(cardId, novoStatus) {
    fetch(`/update-status-fastmoney/${cardId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: novoStatus })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Status atualizado com sucesso!');

            // Atualiza o ribbon visualmente sem precisar de F5
            const card = document.getElementById(cardId);
            const ribbon = card.querySelector('.ribbon');
            
            if (novoStatus === 'Aprovado') {
                ribbon.classList.remove('bg-danger', 'bg-primary');
                ribbon.classList.add('bg-success');
                ribbon.innerText = 'Aprovada';
            } else if (novoStatus === 'Rejeitado') {
                ribbon.classList.remove('bg-success', 'bg-primary');
                ribbon.classList.add('bg-danger');
                ribbon.innerText = 'Rejeitada';
            } else if (novoStatus === 'Pendente') {
                ribbon.classList.remove('bg-success', 'bg-danger');
                ribbon.classList.add('bg-primary');
                ribbon.innerText = 'Pendente';
            }
            
            // Move o card para a coluna correta
            if (novoStatus === 'Aprovado') {
                document.querySelector('.approved').appendChild(card);
            } else if (novoStatus === 'Rejeitado') {
                document.querySelector('.rejected').appendChild(card);
            } else {
                document.querySelector('.pending').appendChild(card);
            }
        } else {
            alert('Erro ao atualizar o status: ' + data.error);
        }
    })
    .catch(error => console.error('Erro ao atualizar o status:', error));
}


// Função para filtrar as solicitações por datas selecionadas
function filtrarPorDatas(startDate, endDate) {
    window.location.href = `/gestao-fastmoney?start_date=${startDate}&end_date=${endDate}`;
}

// Função para atualizar os cards após o filtro
function atualizarCards(data) {
    // Limpa as colunas atuais
    document.querySelector('.pending').innerHTML = '';
    document.querySelector('.approved').innerHTML = '';
    document.querySelector('.rejected').innerHTML = '';

    // Reinsere os cards filtrados com base no status
    data.forEach(solicitacao => {
        const status = solicitacao[11];
        const column = document.querySelector(`.${status.toLowerCase()}`);
        if (column) {
            const cardHTML = `
                <div class="card" draggable="true" id="${solicitacao[0]}">
                    <h3>Solicitação de Fast Money</h3>
                    <p><strong>Corretor:</strong> ${solicitacao[2]}</p>
                    <p><strong>Supervisor:</strong> ${solicitacao[13]}</p>
                    <p><strong>Valor:</strong> R$ ${solicitacao[5]}</p>
                    <p><strong>Data de Solicitação:</strong> ${solicitacao[12]}</p>
                    <button class="view-details" onclick="openModal(this.parentElement)">Ver Detalhes</button>
                </div>
            `;
            column.insertAdjacentHTML('beforeend', cardHTML);
        }
    });
}

// Verifica se o usuário tem permissão
if (parseInt(userType) === 1 || parseInt(userType) === 7) {
    // Usuário com permissão para mover os cards
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('dragstart', dragStart);
        card.addEventListener('dragend', dragEnd);
    });

    document.querySelectorAll('.column').forEach(column => {
        column.addEventListener('dragover', dragOver);
        column.addEventListener('drop', drop);
        column.addEventListener('dragleave', dragLeave);
    });
} else {
    console.log('Usuário sem permissão para mover os blocos.');
}

// Função chamada quando o usuário começa a arrastar um card
function dragStart(e) {
    e.dataTransfer.setData('text/plain', e.target.id);
    e.target.classList.add('dragging');
    e.dataTransfer.setDragImage(new Image(), 0, 0);

    const rect = e.target.getBoundingClientRect();
    e.target.style.position = 'absolute';
    e.target.style.zIndex = '1000';
    e.target.style.width = `${rect.width}px`;
    e.target.style.height = `${rect.height}px`;
    e.target.style.left = `${e.clientX - rect.width / 2}px`;
    e.target.style.top = `${e.clientY - rect.height / 2}px`;

    document.addEventListener('mousemove', moveCard);
}

// Função para mover o card junto com o cursor
function moveCard(e) {
    const draggingCard = document.querySelector('.dragging');
    if (draggingCard) {
        draggingCard.style.left = `${e.clientX - draggingCard.offsetWidth / 2}px`;
        draggingCard.style.top = `${e.clientY - draggingCard.offsetHeight / 2}px`;
    }
}

// Função chamada quando o usuário termina de arrastar um card
function dragEnd(e) {
    e.target.classList.remove('dragging');
    e.target.style.position = '';
    e.target.style.zIndex = '';
    e.target.style.left = '';
    e.target.style.top = '';
    e.target.style.width = '';
    e.target.style.height = '';

    document.removeEventListener('mousemove', moveCard);
}

// Função chamada continuamente enquanto um card está sendo arrastado sobre uma coluna
function dragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('drag-over');
}

// Função chamada quando o card é solto em uma nova coluna
function drop(e) {
    e.preventDefault();
    const cardId = e.dataTransfer.getData('text/plain');
    const card = document.getElementById(cardId);

    // Verifica se o card foi solto na coluna de aprovados, rejeitados ou pendentes
    if (e.target.closest('.approved')) {
        document.querySelector('.approved').appendChild(card);
        atualizarStatus(cardId, 'Aprovado');
    } else if (e.target.closest('.rejected')) {
        document.querySelector('.rejected').appendChild(card);
        atualizarStatus(cardId, 'Rejeitado');
    } else if (e.target.closest('.pending')) {
        document.querySelector('.pending').appendChild(card);
        atualizarStatus(cardId, 'Pendente');
    } else {
        console.log('O card não foi solto em uma coluna válida.');
    }

    // Remove a classe de estilo de arrasto
    e.target.closest('.column').classList.remove('drag-over');
}

// Função chamada quando o card sai de cima de uma coluna sem ser solto
function dragLeave(e) {
    e.currentTarget.classList.remove('drag-over');
}

//JAVA PARA NOVA PAGINA DE FAST-MONEY//
// Função chamada quando o usuário começa a arrastar um card
function drag(ev) {
    ev.dataTransfer.setData("text", ev.target.id);
}

// Permite que os elementos sejam soltos
function allowDrop(ev) {
    ev.preventDefault();
}

// Função chamada quando o card é solto em uma nova coluna
function drop(ev) {
    ev.preventDefault();
    const cardId = ev.dataTransfer.getData("text");
    const card = document.getElementById(cardId);
    ev.target.closest('.column').appendChild(card);  // Move o card para a nova coluna

    // Atualiza o status do card conforme a coluna
    if (ev.target.closest('.approved')) {
        atualizarStatus(cardId, 'Aprovado');
    } else if (ev.target.closest('.rejected')) {
        atualizarStatus(cardId, 'Rejeitado');
    } else if (ev.target.closest('.pending')) {
        atualizarStatus(cardId, 'Pendente');
    } else {
        console.log('O card não foi solto em uma coluna válida.');
    }
}

// Função para atualizar o status no backend e também no front-end
function atualizarStatus(cardId, novoStatus) {
    fetch(`/update-status-fastmoney/${cardId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: novoStatus })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Status atualizado com sucesso!');

            // Atualiza o ribbon visualmente sem precisar de F5
            const card = document.getElementById(cardId);
            const ribbon = card.querySelector('.ribbon');
            
            if (novoStatus === 'Aprovado') {
                ribbon.classList.remove('bg-danger', 'bg-primary');
                ribbon.classList.add('bg-success');
                ribbon.innerText = 'Aprovada';
            } else if (novoStatus === 'Rejeitado') {
                ribbon.classList.remove('bg-success', 'bg-primary');
                ribbon.classList.add('bg-danger');
                ribbon.innerText = 'Rejeitada';
            } else if (novoStatus === 'Pendente') {
                ribbon.classList.remove('bg-success', 'bg-danger');
                ribbon.classList.add('bg-primary');
                ribbon.innerText = 'Pendente';
            }
            
            // Move o card para a coluna correta
            if (novoStatus === 'Aprovado') {
                document.querySelector('.approved').appendChild(card);
            } else if (novoStatus === 'Rejeitado') {
                document.querySelector('.rejected').appendChild(card);
            } else {
                document.querySelector('.pending').appendChild(card);
            }
        } else {
            alert('Erro ao atualizar o status: ' + data.error);
        }
    })
    .catch(error => console.error('Erro ao atualizar o status:', error));
}

function showRejectionFields() {
    const rejectionSection = document.getElementById('rejection-fields');
    
    if (rejectionSection.style.display === 'none' || rejectionSection.style.display === '') {
        rejectionSection.style.display = 'block';  // Exibe os campos de rejeição
    } else {
        rejectionSection.style.display = 'none';  // Esconde os campos se já estiverem visíveis
    }

    // Oculta os botões de Aprovar e Rejeitar durante o processo de rejeição
    document.querySelector('.approve-btn').style.display = 'none';
    document.querySelector('.reject-btn').style.display = 'none';
}

function submitRejection() {
    const motivoRejeicao = document.getElementById('motivoRejeicao').value;
    const comentarioRejeicao = document.getElementById('comentarioRejeicao').value;

    if (!motivoRejeicao || !comentarioRejeicao) {
        alert('Por favor, preencha todos os campos.');
        return;
    }

    const fastmoneyId = document.getElementById('modal').getAttribute('data-active-card');  // Obtenha o ID do card

    fetch(`/update-status-fastmoney/${fastmoneyId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: 'Rejeitado',
            motivo: motivoRejeicao,
            comentario: comentarioRejeicao
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Solicitação rejeitada com sucesso!');
            closeModal();
        } else {
            alert('Erro ao rejeitar a solicitação.');
        }
    })
    .catch(error => console.error('Erro ao enviar rejeição:', error));
}

document.getElementById('seletivo-form').addEventListener('submit', function(event) {
    event.preventDefault();
    const codigoBarras = document.getElementById('codigo_barras').value;
    const senhaBoleto = document.getElementById('senha_boleto').value;

    // Lógica para enviar o código de barras e a senha para o backend
    fetch('/upload_boleto', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            codigo_barras: codigoBarras,
            senha_boleto: senhaBoleto
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Código de barras enviado com sucesso!');
        } else {
            alert('Erro ao enviar o código de barras: ' + data.error);
        }
    })
    .catch(error => console.error('Erro ao enviar o código de barras:', error));
});

document.getElementById('boleto-form').addEventListener('submit', function(event) {
    event.preventDefault();
    const formData = new FormData();
    formData.append('boleto', document.getElementById('boleto').files[0]);
    formData.append('codigo_barras', document.getElementById('codigo_barras').value);
    formData.append('senha_boleto', document.getElementById('senha_boleto').value);

    fetch('/upload_boleto', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Boleto enviado com sucesso!');
        } else {
            alert('Erro ao enviar o boleto: ' + data.error);
        }
    })
    .catch(error => console.error('Erro ao enviar o boleto:', error));
});

document.getElementById('uploadForm').addEventListener('submit', function(event) {
    event.preventDefault();
    const formData = new FormData(this);

    fetch('/upload_boleto', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.codigo_barras) {
            document.getElementById('codigo_barras').value = data.codigo_barras;
            document.getElementById('render_codigo_barras').innerText = data.codigo_barras;
        } else if (data.solicitar_senha) {
            alert('PDF protegido por senha. Por favor, insira a senha.');
        } else if (data.solicitar_codigo_barras) {
            alert('Código de barras não encontrado. Por favor, insira o código de barras manualmente.');
        } else {
            alert('Erro ao ler o código de barras: ' + data.error);
        }
    })
    .catch(error => console.error('Erro no upload do boleto:', error));
});