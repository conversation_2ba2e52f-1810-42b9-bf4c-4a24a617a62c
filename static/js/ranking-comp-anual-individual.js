$(document).ready(function () {
    console.log("Initializing DataTable for Annual Comparison...");

    // Função para atualizar a legenda de última atualização
    function updateLastExecutionTime() {
        $.ajax({
            url: "/api/last-execution",
            method: "GET",
            success: function (response) {
                if (response.status === 'success' && response.data) {
                    $('#last-update').html(`Última atualização: ${response.data.data}`);
                }
            },
            error: function (xhr, status, error) {
                console.log("Error fetching last execution time: ", error);
            }
        });
    }

    // Atualiza a legenda quando a página carrega
    updateLastExecutionTime();

    // Função para carregar as opções de meses/anos no seletor
    function loadMonthYearOptions() {
        $.ajax({
            url: "/api/meses_anos", // Endpoint para obter os meses/anos
            method: "GET",
            success: function (data) {
                console.log("Month/Year options loaded: ", data);
                $('#monthYearPairPicker').empty(); // <PERSON><PERSON> as opções atuais
    
                // Criamos um array para armazenar os pares de meses
                let monthYearPairs = [];
    
                // Filtra os dados para criar pares de meses do mesmo mês do ano anterior
                for (let i = 0; i < data.length; i++) {
                    let currentMonthYear = data[i];
                    let [currentMonth, currentYear] = currentMonthYear.split('/');
                    let previousYear = (parseInt(currentYear) - 1).toString();
                    let previousMonthYear = `${currentMonth}/${previousYear}`;
    
                    if (data.includes(previousMonthYear)) {
                        // Adiciona o par no formato: [mês atual, mês anterior, texto, valor]
                        monthYearPairs.push({
                            current: currentMonthYear,
                            previous: previousMonthYear,
                            text: `${previousMonthYear} e ${currentMonthYear}`,
                            value: `${previousMonthYear}-${currentMonthYear}`,
                            // Adicionamos o ano e mês para facilitar a ordenação
                            sortYear: parseInt(currentYear),
                            sortMonth: parseInt(currentMonth)
                        });
                    }
                }
    
                // Ordena os pares por ano e mês decrescente
                monthYearPairs.sort((a, b) => {
                    // Primeiro compara os anos (ordem decrescente)
                    if (a.sortYear !== b.sortYear) {
                        return b.sortYear - a.sortYear;
                    }
                    // Se os anos são iguais, compara os meses (ordem decrescente)
                    return b.sortMonth - a.sortMonth;
                });
    
                // Adiciona as opções ordenadas ao seletor
                monthYearPairs.forEach(pair => {
                    $('#monthYearPairPicker').append($('<option>', {
                        value: pair.value,
                        text: pair.text
                    }));
                });
    
                $('#monthYearPairPicker').multiselect('rebuild'); // Reconstrói o seletor
                selectMostRecentMonthYear(); // Seleciona automaticamente o mês mais recente
            },
            error: function (xhr, status, error) {
                console.log("Error fetching month/year options: ", xhr.responseText);
            }
        });
    }
    

    // Função para selecionar automaticamente o mês mais recente
    function selectMostRecentMonthYear() {
        let options = $('#monthYearPairPicker option');
        if (options.length > 0) {
            $(options[0]).prop('selected', true); // Seleciona a primeira opção (mais recente)
            $('#monthYearPairPicker').multiselect('refresh'); // Atualiza o seletor
            $('#comparativoTable').DataTable().ajax.reload(); // Recarrega a tabela com os dados do mês selecionado
        }
    }

    // Inicializa o seletor de par de meses/anos com o plugin Multiselect
    $('#monthYearPairPicker').multiselect({
        enableFiltering: true,
        includeSelectAllOption: false,
        buttonWidth: '400px',
        nonSelectedText: 'Selecione Par de Meses/Ano',
        // Ação ao mudar a seleção
        onChange: function (option, checked) {
            console.log("Month/Year pair changed: ", $('#monthYearPairPicker').val());
            if ($('#monthYearPairPicker').val().length > 1) {
                $('#monthYearPairPicker option:not(:selected)').prop('disabled', true); // Desativa opções não selecionadas
            } else {
                $('#monthYearPairPicker option').prop('disabled', false); // Ativa todas as opções
                $('#comparativoTable').DataTable().ajax.reload(); // Recarrega a tabela ao alterar a seleção
            }
        },
        // Ação ao mostrar o dropdown
        onDropdownShow: function () {
            if ($('#monthYearPairPicker option:selected').length >= 1) {
                $('#monthYearPairPicker option:not(:selected)').prop('disabled', true); // Desativa opções não selecionadas
            }
        },
        // Ação ao esconder o dropdown
        onDropdownHide: function () {
            $('#monthYearPairPicker option').prop('disabled', false); // Ativa todas as opções
        }
    });

    loadMonthYearOptions(); // Chama a função para carregar as opções de meses/anos

    // Inicializa a DataTable
    $('#comparativoTable').DataTable({
        "ajax": {
            "url": "/api/consulta_comp_mes_individual", // Endpoint para obter os dados comparativos
            "dataSrc": "", // Fonte de dados é a resposta direta do AJAX
            "data": function (d) {
                let selectedPair = $('#monthYearPairPicker').val(); // Obtém o par selecionado
                if (selectedPair) {
                    let [mesAnoAnterior, mesAnoAtual] = selectedPair[0].split("-"); // Divide o valor no par de meses/anos
                    d.mesAnoAnterior = mesAnoAnterior; // Adiciona ao objeto de dados enviado
                    d.mesAnoAtual = mesAnoAtual; // Adiciona ao objeto de dados enviado
                    $(".mes-anterior-header").text(mesAnoAnterior); // Atualiza o cabeçalho da coluna
                    $(".mes-atual-header").text(mesAnoAtual); // Atualiza o cabeçalho da coluna
                }
            },
            "error": function (xhr, error, thrown) {
                console.log("Error fetching data: ", xhr.responseText);
            }
        },
        "columns": [
            { "data": "assistente", "width": "950px" }, // Coluna Assistente
            { "data": "valor_anterior", "width": "200px", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ ') }, // Coluna Valor Anterior
            { "data": "valor_atual", "width": "200px", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ ') }, // Coluna Valor Atual
            {
                "data": "diferenca_percentual",
                "width": "100px",
                render: function (data) {
                    return typeof data === 'number' ? data.toFixed(2) + '%' : 'N/A'; // Renderiza a diferença percentual
                }
            }, // Coluna Dif %
            { "data": "diferenca_valor", "width": "200px", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ ') } // Coluna Dif em Valores
        ],
        "order": [[3, 'asc']], // Ordena inicialmente pela coluna "Dif em %" em ordem crescente (contagem inicia no 0)
        "dom": 'Bfrtip', // Layout dos botões
        "buttons": ['copy', 'csv', 'excel', 'pdf', 'print'], // Botões disponíveis
        "paging": false, // Desativa paginação
        "searching": false, // Desativa busca
        "info": false, // Desativa informações de paginação
        "footerCallback": function (row, data, start, end, display) {
            var api = this.api(), data;

            // Função para converter valores para inteiros
            var intVal = function (i) {
                return typeof i === 'string' ?
                    parseFloat(i.replace(/[\$,R$]/g, '').replace(',', '.')) || 0 :
                    typeof i === 'number' ?
                        i : 0;
            };

            // Calcula o total da coluna "Valor Anterior"
            var totalAnterior = api
                .column(1, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            // Calcula o total da coluna "Valor Atual"
            var totalAtual = api
                .column(2, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            // Calcula o total da coluna "Dif em Valores"
            var totalDiferencaValor = api
                .column(4, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            // Atualiza os rodapés das colunas com os totais calculados
            $(api.column(1).footer()).html('R$ ' + totalAnterior.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            $(api.column(2).footer()).html('R$ ' + totalAtual.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            $(api.column(4).footer()).html('R$ ' + totalDiferencaValor.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
        }
    }).buttons().container().appendTo('#buttons-container'); // Adiciona os botões ao contêiner abaixo da tabela

    console.log("DataTable initialized.");
});
