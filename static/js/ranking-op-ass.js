$(document).ready(function () {
    console.log("Initializing DataTable...");

    // Função para atualizar a legenda de última atualização
    function updateLastExecutionTime() {
        $.ajax({
            url: "/api/last-execution",
            method: "GET",
            success: function (response) {
                if (response.status === 'success' && response.data) {
                    $('#last-update').html(`Última atualização: ${response.data.data}`);
                }
            },
            error: function (xhr, status, error) {
                console.log("Error fetching last execution time: ", error);
            }
        });
    }

    // Atualiza a legenda quando a página carrega
    updateLastExecutionTime();

    function loadTables() {
        $.ajax({
            url: "/api/ranking_op_ass",
            method: "GET",
            success: function (data) {
                console.log("Ranking data loaded: ", data);

                $('#tables-container').empty();

                // Garantir a ordem correta das operadoras recebidas
                var operadorasOrdenadas = Object.keys(data);

                // Iterar pelas operadoras na ordem correta
                operadorasOrdenadas.forEach(function (operadora, index) {
                    var tableId = 'table-' + index;
                    var rowsHtml = '';
                    var items = data[operadora];

                    // Gerar HTML das linhas
                    items.forEach(item => {
                        let valor = parseFloat(item.vltotal.replace(',', ''));
                        rowsHtml += `
                            <tr>
                                <td>${item.assistente}</td>
                                <td class="text-accounting">R$ ${valor.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                            </tr>
                        `;
                    });

                    var tableHtml = `
                        <div class="col-md-6 mb-4">
                            <table id="${tableId}" class="display" style="width:100%">
                                <thead class="ranking-header">
                                    <tr><th colspan="2" class="text-truncate">${operadora}</th></tr>
                                    <tr>
                                        <th class="ranking-column-header text-center">Assistente</th>
                                        <th class="ranking-column-header text-center">Total</th>
                                    </tr>
                                </thead>
                                <tbody class="ranking-body">
                                    ${rowsHtml}
                                </tbody>
                                <tfoot class="ranking-footer">
                                    <tr>
                                        <th class="ranking-column-footer">Total Geral</th>
                                        <th class="ranking-column-footer text-accounting">
                                            R$ ${items.reduce((sum, item) => sum + parseFloat(item.vltotal.replace(',', '')), 0).toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                        </th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    `;
                    $('#tables-container').append(tableHtml);

                    // Aplicar DataTables sem reordenação
                    $('#' + tableId).DataTable({
                        paging: false,
                        searching: false,
                        info: false,
                        dom: 'Bfrtip',
                        buttons: [],
                        ordering: false  // Desativar a ordenação para manter a ordem do back-end
                    });

                    // Ajustar estilos diretamente via JavaScript
                    $('#' + tableId + ' tbody tr').css('background-color', '#013977').css('color', 'white');
                    $('#' + tableId + ' thead th').css('background-color', '#F2753D').css('color', 'white');
                    $('#' + tableId + ' tfoot th').css('background-color', '#F2753D').css('color', 'white');
                    $('#' + tableId + ' tbody tr:nth-child(even)').css('background-color', '#f9f9f9').css('color', 'black');
                    $('#' + tableId + ' tbody tr:hover').css('background-color', '#f1f1f1').css('color', 'black');

                    // Adicionar borda branca entre as linhas
                    $('#' + tableId + ' tbody tr').css('border-bottom', '1px solid white');

                    // Centralizar e cortar texto no cabeçalho da operadora
                    $('#' + tableId + ' thead th[colspan="2"]').css({
                        'text-align': 'center',
                        'white-space': 'nowrap',
                        'overflow': 'hidden',
                        'text-overflow': 'ellipsis',
                        'margin-right': '10px'
                    });
                });
            },
            error: function (xhr, status, error) {
                console.log("Error fetching ranking data: ", xhr.responseText);
            }
        });
    }

    loadTables();
});
