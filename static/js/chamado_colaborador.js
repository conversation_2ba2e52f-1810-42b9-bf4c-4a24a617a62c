function waitForElement(selector, callback) {
    const element = document.querySelector(selector);
    if (element) {
        callback(element);
    } else {
        setTimeout(() => waitForElement(selector, callback), 100);
    }
}

document.addEventListener("DOMContentLoaded", function () {
    let slaCounts = { 'No Prazo': 0, 'Atrasado': 0, 'Encerrado': 0 };
    const now = new Date();

    function calcularSLA() {
        const chamadosTable = document.getElementById('chamados-table');
        if (!chamadosTable) {
            console.error("Elemento 'chamados-table' não encontrado.");
            return;
        }

        const rows = chamadosTable.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        slaCounts = { 'No Prazo': 0, 'Atrasado': 0, 'Encerrado': 0 };

        for (let row of rows) {
            const status = row.cells[4].textContent.trim(); // Ajuste para coluna de status
            const dataLimiteStr = row.querySelector('.sla').getAttribute('data-data-limite');
            const dataLimite = dataLimiteStr ? new Date(dataLimiteStr) : null;
            const slaCell = row.querySelector('.sla'); // Celula onde o SLA será mostrado

            // Verifica se o status é "Resolvido" e marca o SLA como "Encerrado"
            if (status === 'Resolvido') {
                slaCell.innerHTML = '<span class="badge badge-success">Encerrado</span>';
                slaCounts['Encerrado']++;
            }
            // Verifica se o status é "Aguardando Solicitante" e mantém "No Prazo" para SLA
            else if (status === 'Aguardando Solicitante') {
                slaCell.innerHTML = '<span class="badge badge-warning">No Prazo</span>';
                slaCounts['No Prazo']++;
            }
            // Calcula o SLA com base na data limite, se existir
            else if (dataLimite) {
                if (dataLimite < now) {
                    slaCell.innerHTML = '<span class="badge badge-danger">Atrasado</span>';
                    slaCounts['Atrasado']++;
                } else {
                    slaCell.innerHTML = '<span class="badge badge-warning">No Prazo</span>';
                    slaCounts['No Prazo']++;
                }
            } else {
                slaCell.innerHTML = 'N/A';
            }
        }

        // Log para confirmar contagens de SLA
        console.log("Contagens de SLA:", slaCounts);
    }

    function filterTable() {
        const chamadosTable = document.getElementById('chamados-table');
        if (!chamadosTable) {
            console.error("Elemento 'chamados-table' não encontrado para filtrar.");
            return;
        }

        const rows = chamadosTable.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        const selectedStatuses = $('#status-filter').val();
        const selectedSLA = $('#sla-filter').val();
        const searchTerm = document.getElementById('search-input') ? document.getElementById('search-input').value.toLowerCase() : '';

        for (let row of rows) {
            const status = row.cells[4].textContent.trim();
            const slaCell = row.cells[8];
            const sla = slaCell ? slaCell.textContent.trim() : '';
            const protocolo = row.cells[0].textContent.toLowerCase();
            const assunto = row.cells[1].textContent.toLowerCase();

            let shouldShow = true;

            if (searchTerm && !(protocolo.includes(searchTerm) || assunto.includes(searchTerm))) {
                shouldShow = false;
            }

            if (selectedStatuses && selectedStatuses.length > 0 && !selectedStatuses.includes(status)) {
                shouldShow = false;
            }

            if (selectedSLA && selectedSLA.length > 0 && !selectedSLA.includes(sla)) {
                shouldShow = false;
            }

            row.style.display = shouldShow ? '' : 'none';
        }
    }

    function setupFilters() {
        waitForElement('#status-filter', function () {
            $('#status-filter').select2({ placeholder: "Filtrar por Status", allowClear: true });
            $('#status-filter').on('change', filterTable);
        });

        waitForElement('#sla-filter', function () {
            $('#sla-filter').select2({ placeholder: "Filtrar por SLA", allowClear: true });
            $('#sla-filter').on('change', filterTable);
        });

        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', filterTable);
        }
    }

    function initializeChamadoTrackingPage() {
        waitForElement('#chamados-table', function () {
            calcularSLA();
            filterTable();
            setupFilters();
        });
    }

    // Inicialização de SLA e filtros na página atual
    if (document.getElementById('chamados-table')) {
        calcularSLA();
        filterTable();
        setupFilters();
    }
});