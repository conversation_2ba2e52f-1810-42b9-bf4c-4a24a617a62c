// Listener para o link "Fast e Adiantamento"
document.addEventListener('DOMContentLoaded', function () {
    const fastmoneyLink = document.getElementById('fastmoneyLink');

    if (fastmoneyLink) {
        fastmoneyLink.addEventListener('click', function (event) {
            event.preventDefault();
            console.log("Link 'Fast e Adiantamento' clicado, iniciando fetch para '/fastmoney'.");

            fetch('/fastmoney')  // Ajuste para usar a rota Flask
                .then(response => {
                    if (!response.ok) {
                        console.error('Erro na resposta do fetch:', response.statusText);
                        throw new Error('Erro ao carregar o conteúdo de Fast Money: ' + response.statusText);
                    }
                    console.log('Resposta recebida com sucesso do servidor para /fastmoney.');
                    return response.text();
                })
                .then(data => {
                    const mainContent = document.getElementById('mainContent');
                    if (mainContent) {
                        console.log('Inserindo conteúdo de Fast Money na página.');
                        mainContent.innerHTML = data;
                        mainContent.classList.add('fastmoney-page');

                        // Inicializar a página Fast Money
                        initializeFastMoneyPage();
                    } else {
                        console.error('Elemento mainContent não encontrado no DOM.');
                    }
                })
                .catch(error => console.error('Erro ao carregar conteúdo de Fast Money:', error));
        });
    } else {
        console.error('Link "Fast e Adiantamento" não encontrado no DOM.');
    }
});

// Função para inicializar a página Fast Money
function initializeFastMoneyPage() {
    console.log("Inicializando a página Fast Money.");

    // Adicionar a lógica para habilitar/desabilitar o upload de documentos
    const comprovanteSim = document.getElementById('comprovante_sim');
    const comprovanteNao = document.getElementById('comprovante_nao');
    const uploadSection = document.getElementById('upload-section');
    const comprovanteInput = document.getElementById('comprovante');
    const boletoInput = document.getElementById('boleto');
    const comprovanteName = document.getElementById('comprovante-name');
    const boletoName = document.getElementById('boleto-name');
    const removeComprovanteBtn = document.getElementById('remove-comprovante');
    const removeBoletoBtn = document.getElementById('remove-boleto');

    // Função para alternar a visibilidade dos campos de upload
    function toggleUploadFields() {
        if (comprovanteSim.checked) {
            uploadSection.classList.remove('hidden');
        } else {
            uploadSection.classList.add('hidden');
            // Limpar valores quando ocultar os campos
            comprovanteInput.value = "";
            boletoInput.value = "";
            comprovanteName.textContent = "";
            boletoName.textContent = "";
            removeComprovanteBtn.classList.add('hidden');
            removeBoletoBtn.classList.add('hidden');
        }
    }

    // Adiciona o nome do arquivo de comprovante carregado
    comprovanteInput.addEventListener('change', function () {
        const fileName = comprovanteInput.files[0] ? comprovanteInput.files[0].name : '';
        comprovanteName.textContent = fileName;
        if (fileName) {
            removeComprovanteBtn.classList.remove('hidden');
        }
    });

    // Adiciona o nome do arquivo de boleto carregado
    boletoInput.addEventListener('change', function () {
        const fileName = boletoInput.files[0] ? boletoInput.files[0].name : '';
        boletoName.textContent = fileName;
        if (fileName) {
            removeBoletoBtn.classList.remove('hidden');
        }
    });

    // Função para remover comprovante
    removeComprovanteBtn.addEventListener('click', function () {
        comprovanteInput.value = "";
        comprovanteName.textContent = "";
        removeComprovanteBtn.classList.add('hidden');
    });

    // Função para remover boleto
    removeBoletoBtn.addEventListener('click', function () {
        boletoInput.value = "";
        boletoName.textContent = "";
        removeBoletoBtn.classList.add('hidden');
    });

    // Escutadores de eventos para os radio buttons
    comprovanteSim.addEventListener('change', toggleUploadFields);
    comprovanteNao.addEventListener('change', toggleUploadFields);

    // Inicializa os campos de upload com base no valor inicial do radio button
    toggleUploadFields();

    const formAdiantamento = document.getElementById("form-adiantamento");

    if (formAdiantamento) {
        console.log("Formulário de adiantamento encontrado. Configurando listener para submissão.");

        formAdiantamento.addEventListener("submit", function (event) {
            event.preventDefault(); // Impede o envio padrão do formulário
            console.log("Submissão do formulário de adiantamento interceptada.");

            let formValid = true;
            let errorMessage = '';

            // Validações adicionais podem ser incluídas aqui

            if (formValid) {
                console.log("Formulário validado com sucesso. Pronto para envio.");

                if (confirm("Realmente deseja enviar essa solicitação de adiantamento?")) {
                    const formData = new FormData(formAdiantamento);

                    console.log("Enviando formulário via fetch para:", formAdiantamento.action);

                    fetch(formAdiantamento.action, {
                        method: "POST",
                        body: formData
                    })
                        .then(response => {
                            console.log('Resposta do servidor recebida:', response);
                            if (response.ok) {
                                return response.json();
                            } else {
                                throw new Error("Erro na resposta do servidor");
                            }
                        })
                        .then(data => {
                            console.log('Dados recebidos do servidor:', data);
                            if (data.Success) {
                                console.log("Solicitação de adiantamento enviada com sucesso.");
                                showAlert(data.message, 'success');
                                formAdiantamento.reset();
                            } else {
                                console.warn("Erro na solicitação de adiantamento:", data.message);
                                showAlert("Erro: " + data.message, 'danger');
                            }
                        })
                        .catch(error => {
                            console.error("Erro ao enviar a solicitação de adiantamento:", error);
                            showAlert("Erro ao enviar a solicitação de adiantamento. Por favor, tente novamente.", 'danger');
                        });
                } else {
                    console.log("Envio do adiantamento cancelado pelo usuário.");
                    showAlert("Envio do adiantamento cancelado.", 'warning');
                }
            } else {
                console.log("Formulário inválido. Exibindo mensagens de erro.");
                showAlert(errorMessage, 'danger');
            }
        });
    } else {
        console.error("Formulário de adiantamento não encontrado.");
    }

    function showAlert(message, type) {
        const alertBox = document.getElementById('alertBox');
        if (alertBox) {
            console.log(`Exibindo alerta: ${message} (tipo: ${type})`);
            alertBox.className = `alert alert-${type}`;
            alertBox.innerText = message;
            alertBox.classList.remove('d-none');

            setTimeout(() => {
                console.log("Ocultando alerta.");
                alertBox.classList.add('d-none');
            }, 5000);
        } else {
            console.error("Elemento alertBox não encontrado.");
        }
    }

    const codigoPropostaInput = document.getElementById('codigo_proposta');

    if (codigoPropostaInput) {
        codigoPropostaInput.addEventListener('change', function () {
            const codigoProposta = codigoPropostaInput.value.trim();

            if (codigoProposta) {
                console.log(`Buscando dados para o código de proposta: ${codigoProposta}`);
                fetch(`/api/adiantamento/${codigoProposta}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Erro ao buscar os dados da proposta');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Dados recebidos da API:', data);
                        if (data && data.Success) {
                            const resultado = data.result;

                            // Verifica se a proposta está cancelada
                            if (resultado.status && resultado.status.toLowerCase() === "cancelada") {
                                alert("Proposta cancelada, não é possível realizar o adiantamento.");
                                return; // Bloqueia o preenchimento dos campos
                            }

                            // Verifica se o subproduto é "multinotas"
                            if (resultado.subProduto.toLowerCase().includes("multinotas")) {
                                alert("Proposta não elegível para fast money, alterar o subproduto com o departamento de cadastro.");
                                return; // Bloqueia o preenchimento dos campos
                            }

                            // Preenchendo todos os campos conforme os dados recebidos da API
                            document.getElementById('valor_proposta').value = resultado.vlBoleto;
                            document.getElementById('corretor').value = resultado.corretor;
                            document.getElementById('supervisor').value = resultado.supervisor;
                            document.getElementById('operadora').value = resultado.nomeOperadora;
                            document.getElementById('segurado').value = resultado.segurado;
                            document.getElementById('acordo').value = resultado.comissionamentoGradeProducao;
                            document.getElementById('tabela_padrao').value = resultado.comissionamentoGradeCorretor;
                            document.getElementById('subproduto').value = resultado.subProduto;
                            document.getElementById('modalidade').value = resultado.modalidade; // Campo "Modalidade"

                        } else {
                            alert('Dados da proposta não encontrados.');
                        }
                    })
                    .catch(error => {
                        console.error('Erro ao buscar os dados da proposta:', error);
                        alert('Erro de conexão com a base de dados do Trindade');
                    });
            }
        });
    } else {
        console.error('Elemento códigoPropostaInput não encontrado.');
    }
};

document.addEventListener('DOMContentLoaded', function () {
    console.log("DOM completamente carregado e analisado. Inicializando a página Fast Money.");
    initializeFastMoneyPage();
});
