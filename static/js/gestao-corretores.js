let pagina_atual = 1;
let total_paginas = 1;

document.addEventListener('DOMContentLoaded', () => {
    // Carregar a primeira página de corretores ao carregar a página
    document.getElementById('search-button').addEventListener('click', () => {
        fetchCorretores(pagina_atual);
    });
});

// Função para buscar os corretores paginados
function fetchCorretores(pagina) {
    const nome = document.getElementById('filter-nome').value;
    const email = document.getElementById('filter-email').value;
    const statusContrato = document.getElementById('filter-status').value;

    document.getElementById('loading').style.display = 'block';

    fetch(`/api/corretores?pagina=${pagina}&registrosPorPagina=100&nome=${nome}&email=${email}&status_contrato=${statusContrato}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('loading').style.display = 'none';

            if (data.errorMessage) {
                console.error(data.errorMessage);
                return;
            }

            const corretoresList = document.getElementById('corretores-list');
            corretoresList.innerHTML = '';  // Limpa o conteúdo anterior

            data.result.collection.forEach(corretor => {
                const row = document.createElement('tr');

                // Preenchendo as colunas da tabela com os dados do corretor
                row.innerHTML = `
                    <td>${corretor.nome || 'Nome não informado'}</td>
                    <td>${corretor.email || 'E-mail não informado na capa'}</td>
                    <td>${corretor.dtLGPD ? 'Situação Regular' : 'Situação Irregular'}</td>
                    <td>${new Date(corretor.dtInclusao).toLocaleDateString()}</td>
                    <td>${corretor.dtLGPD ? 'Assinado' : 'Não Assinado'}</td>
                    <td>${corretor.detalhes.dtUltimaProducao ? new Date(corretor.detalhes.dtUltimaProducao).toLocaleDateString() : '---'}</td>
                    <td>${corretor.detalhes.assistente || '---'}</td>
                    <td>${corretor.detalhes.gradeEsforcoProprio || '---'}</td>
                    <td>${corretor.detalhes.producaoTipo || '---'}</td>
                    <td>${corretor.pessoaFisica ? 'Física' : 'Jurídica'}</td>
                    <td>${corretor.ativo ? 'Sim' : 'Não'}</td>
                    <td>
                        <button onclick="openModal(${corretor.id})">Ver Detalhes</button>
                    </td>
                `;
                corretoresList.appendChild(row);
            });

            total_paginas = data.result.paginaInfo.totalPaginas;
            pagina_atual = data.result.paginaInfo.paginaAtual;

            document.getElementById('pagination-info').textContent = `Página ${pagina_atual} de ${total_paginas}`;
        })
        .catch(error => {
            console.error('Erro ao buscar corretores:', error);
        });
}

// Função para navegar pelas páginas
function navigatePage(pagina) {
    if (pagina >= 1 && pagina <= total_paginas) {
        fetchCorretores(pagina);
    }
}

// Função para abrir o modal e buscar os detalhes de um corretor específico
function openModal(corretorId) {
    const modal = document.getElementById('modal');
    modal.style.display = 'block';

    // Exibir o indicador de carregamento no modal
    document.getElementById('loading').style.display = 'block';

    // Fazer a requisição para obter os detalhes do corretor
    fetch(`/api/corretor-detalhes/${corretorId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('loading').style.display = 'none';

            if (data.errorMessage) {
                document.getElementById('error-message').style.display = 'block';
                return;
            }

            // Populando os campos gerais com fallback para 'não cadastrada' caso faltem informações
            const detalhes = data.detalhes?.result?.[0] || {};
            document.getElementById('modal-nome').textContent = detalhes.assistente || 'Informação não cadastrada';
            document.getElementById('modal-email').textContent = detalhes.responsavelEmail || 'Informação não cadastrada';
            document.getElementById('modal-status-contrato').textContent = detalhes.ativo ? 'Ativo' : 'Inativo';
            document.getElementById('modal-data-inclusao').textContent = detalhes.dtInclusao ? new Date(detalhes.dtInclusao).toLocaleDateString() : 'Informação não cadastrada';
            document.getElementById('modal-lgpd').textContent = detalhes.dtLGPD ? 'Assinado' : 'Situação Irregular';
            document.getElementById('modal-ultima-producao').textContent = detalhes.dtUltimaProducao ? new Date(detalhes.dtUltimaProducao).toLocaleDateString() : 'Informação não cadastrada';

            // Preencher dados de endereços, telefones, documentos e contas bancárias com fallback
            preencherDetalhesModal(
                data.enderecos?.result || [],
                data.telefones?.result || [],
                data.documentos?.result || [],
                data.contasBancarias?.result || []
            );
        })
        .catch(error => {
            console.error('Erro ao buscar detalhes do corretor:', error);
            document.getElementById('error-message').style.display = 'block';
        });
}

// População dos Dados no Modal (Endereços, Telefones, Documentos e Contas Bancárias)
function preencherDetalhesModal(enderecos = [], telefones = [], documentos = [], contasBancarias = []) {
    // Preencher Endereços
    const enderecosContent = document.getElementById('enderecos-content');
    enderecosContent.innerHTML = '';  // Limpa o conteúdo anterior
    if (enderecos.length > 0) {
        enderecos.forEach(endereco => {
            const enderecoDiv = document.createElement('div');
            enderecoDiv.textContent = `${endereco.logradouro}, ${endereco.numero || 's/n'} - ${endereco.cidade}, ${endereco.uf}`;
            enderecosContent.appendChild(enderecoDiv);
        });
    } else {
        enderecosContent.textContent = 'Informação não cadastrada';
    }

    // Preencher Telefones
    const telefonesContent = document.getElementById('telefones-content');
    telefonesContent.innerHTML = '';  // Limpa o conteúdo anterior
    if (telefones.length > 0) {
        telefones.forEach(telefone => {
            const telefoneDiv = document.createElement('div');
            telefoneDiv.textContent = `${telefone.telefoneClassificacao}: (${telefone.ddd}) ${telefone.telefone}`;
            telefonesContent.appendChild(telefoneDiv);
        });
    } else {
        telefonesContent.textContent = 'Informação não cadastrada';
    }

    // Preencher Documentos
    const documentosContent = document.getElementById('documentos-content');
    documentosContent.innerHTML = '';  // Limpa o conteúdo anterior
    if (documentos.length > 0) {
        documentos.forEach(documento => {
            const documentoDiv = document.createElement('div');
            documentoDiv.textContent = `${documento.documentoTipo}: ${documento.documento || 'Sem Número'}`;
            documentosContent.appendChild(documentoDiv);
        });
    } else {
        documentosContent.textContent = 'Informação não cadastrada';
    }

    // Preencher Contas Bancárias
    const contasContent = document.getElementById('contas-content');
    contasContent.innerHTML = '';  // Limpa o conteúdo anterior
    if (contasBancarias.length > 0) {
        contasBancarias.forEach(conta => {
            const contaDiv = document.createElement('div');
            contaDiv.textContent = `${conta.banco}: Agência ${conta.agencia} / Conta ${conta.conta}`;
            contasContent.appendChild(contaDiv);
        });
    } else {
        contasContent.textContent = 'Informação não cadastrada';
    }
}

function openTab(event, tabName) {
    // Esconde todo o conteúdo das abas
    var tabcontent = document.getElementsByClassName("tabcontent");
    for (var i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }

    // Remove a classe "active" de todos os botões das abas
    var tablinks = document.getElementsByClassName("tablinks");
    for (var i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }

    // Exibe a aba atual
    document.getElementById(tabName).style.display = "block";

    // Adiciona a classe "active" ao botão da aba atual
    event.currentTarget.className += " active";
}

// Defina a aba "Geral" como padrão visível ao abrir o modal
document.addEventListener('DOMContentLoaded', (event) => {
    document.getElementById("Geral").style.display = "block";  // Aba "Geral" visível por padrão
});

function closeModal() {
    const modal = document.getElementById('modal'); // Encontre o modal pelo ID
    modal.style.display = 'none'; // Esconde o modal
}