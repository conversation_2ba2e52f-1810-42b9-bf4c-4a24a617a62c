var calendar;

document.addEventListener('DOMContentLoaded', function () {
  userType = document.querySelector('.brazil-container').getAttribute('data-user-type');
  userId = document.querySelector('.brazil-container').getAttribute('data-user-id');

  console.log('Tipo:', userType, 'ID:', userId);

  var calendarEl = document.getElementById('calendario');
  calendar = new FullCalendar.Calendar(calendarEl, {
    headerToolbar: {
      left: 'prev,next today',
      center: 'title',
      right: 'dayGridMonth,timeGridWeek,timeGridDay'
    },
    initialView: 'timeGridWeek',
    locale: 'pt-BR',
    navLinks: true,
    selectable: true,
    selectMirror: true,
    editable: true,
    dayMaxEvents: true,
    eventTimeFormat: { // como exibir o tempo do evento
      hour: '2-digit',
      minute: '2-digit',
      meridiem: false
    },
    validRange: {
      start: new Date().toISOString().slice(0, 10), // Restringe datas passadas
    },
    events: function (fetchInfo, successCallback, failureCallback) {
      var salaId = document.getElementById('selecaoSala').value;
      fetch(`/listar_eventos?sala_id=${salaId}`)
        .then(response => response.json())
        .then(events => {
          const formattedEvents = events.map(event => {
            let end = new Date(event.end);
            if (event.allDay && (new Date(event.start)).toISOString().slice(0, 10) !== end.toISOString().slice(0, 10)) {
              end.setDate(end.getDate() + 1);
            }
            return {
              id: event.id,
              title: event.title,
              start: event.start,
              end: end.toISOString(),
              allDay: event.allDay,
            };
          });
          successCallback(formattedEvents);
        })
        .catch(error => failureCallback(error));
    },
    select: function (info) {
      if (userType !== "6") {
        openCustomModalForNewEvent(info);
      } else {
        alert("Você não tem permissão para criar eventos.");
      }
    },
    eventClick: function (info) {
      fetch(`/buscar_evento/${info.event.id}`)
        .then(response => response.json())
        .then(data => openCustomModalWithEventData(data, userType))
        .catch(error => alert('Erro ao buscar detalhes do evento: ' + error));
    },
  });

  calendar.render();

  document.getElementById('selecaoSala').addEventListener('change', function () {
    calendar.refetchEvents();
  });
});

function openCustomModalForNewEvent(info) {
  prepareModal(); // Prepara o modal para um novo evento
  document.getElementById('customModal').style.display = 'flex';

  // Formatar datas para todo o dia
  var startDate = new Date(info.startStr);
  var endDate = info.end ? new Date(new Date(info.endStr).getTime() - 1000) : startDate;

  // Ajusta o horário de início para 00:00 e de fim para 23:59
  var startTime = startDate.toISOString().slice(0, 10) + 'T00:00';
  var endTime = endDate.toISOString().slice(0, 10) + 'T23:59';

  // Definir valores padrão para um novo evento
  document.getElementById('eventTitle').value = '';
  document.getElementById('eventStart').value = startTime;
  document.getElementById('eventEnd').value = endTime;
  document.getElementById('allDay').checked = true; // Define Dia Inteiro como padrão
  var saveButton = document.getElementById('saveEventButton');
  saveButton.innerText = 'Salvar Evento';
  saveButton.onclick = () => saveEvent();
}

function openCustomModalWithEventData(evento, userType) {
  prepareModal(); // Prepara o modal com os dados do evento
  document.getElementById('customModal').style.display = 'flex';

  // Preencha o modal com os dados do evento existente
  document.getElementById('eventTitle').value = evento.title;
  document.getElementById('eventStart').value = evento.data_inicio.slice(0, 16);
  document.getElementById('eventEnd').value = evento.data_fim.slice(0, 16);
  document.getElementById('allDay').checked = evento.allDay;
  document.getElementById('usuarioNome').innerText = 'Usuário: ' + evento.usuario_nome;
  document.getElementById('salaNome').innerText = 'Sala: ' + evento.sala_nome;

  var saveButton = document.getElementById('saveEventButton');
  var deleteButton = document.getElementById('deleteEventButton');

  // A lógica para exibir o botão de salvar para novos eventos permanece igual
  if (!evento.id) {
    saveButton.style.display = 'block';
    saveButton.innerText = 'Salvar Evento';
    saveButton.onclick = () => saveEvent();
  } else {
    saveButton.style.display = 'none';
  }

  // Lógica para os botões de atualizar e excluir, baseada no userType e no usuario_id do evento
  if (userType === "1" || evento.usuario_id.toString() === userId) {
    if (evento.id) {
      saveButton.style.display = 'block';
      saveButton.innerText = 'Atualizar Evento';
      saveButton.onclick = () => saveEvent(evento.id);
    }

    deleteButton.style.display = 'block';
    deleteButton.onclick = () => deleteEvent(evento.id);
  } else {
    deleteButton.style.display = 'none';
  }
}

function prepareModal() {
  var modal = document.getElementById('customModal');
  if (!modal) {
    var modalHTML = `
<div id="customModal" class="modal">
  <div class="event-container">
    <div class="modal-header">
        <span class="close-button" onclick="closeCustomModal()">&times;</span>
    </div>

    <div class="modal-body">
        <form id="eventForm">
            <div class="form-group">
                <label for="eventTitle">Título do Evento:</label>
                <input type="text" class="form-control" id="eventTitle" required>
            </div>
            <div class="form-group">
                <label for="eventStart">Início:</label>
                <input type="datetime-local" class="form-control" id="eventStart" required>
            </div>
            <div class="form-group">
                <label for="eventEnd">Fim:</label>
                <input type="datetime-local" class="form-control" id="eventEnd" required>
            </div>
            <div class="form-check">
                <input type="checkbox" class="form-check-input" id="allDay">
                <label class="form-check-label" for="allDay">Dia Inteiro</label>
            </div>
            <p id="usuarioNome"></p>
            <p id="salaNome"></p>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="saveEventButton">Salvar Evento</button>
        <button type="button" class="btn btn-danger" id="deleteEventButton" style="display: none;">Excluir Evento</button>
    </div>
  </div>
</div>

          `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
  } else {
    // Resetar o formulário e limpar valores
    document.getElementById('eventForm').reset();
    document.getElementById('eventTitle').value = '';
    document.getElementById('eventStart').value = '';
    document.getElementById('eventEnd').value = '';
    document.getElementById('allDay').checked = false;
    document.getElementById('usuarioNome').innerText = '';
    document.getElementById('salaNome').innerText = '';
    document.getElementById('deleteEventButton').style.display = 'none';
  }
}

function deleteEvent(eventId) {
  if (!confirm('Tem certeza que deseja excluir este evento?')) return;

  fetch(`/excluir_evento/${eventId}`, { method: 'DELETE' })
    .then(response => {
      if (!response.ok) {
        throw new Error('Falha ao excluir evento: ' + response.statusText);
      }
      return response.json();
    })
    .then(data => {
      alert('Evento excluído com sucesso!');
      closeCustomModal();
      calendar.refetchEvents();
    })
    .catch(error => {
      alert('Erro ao excluir o evento: ' + error.message);
    });
}

function saveEvent(eventId) {
  var title = document.getElementById('eventTitle').value;
  var start = document.getElementById('eventStart').value;
  var end = document.getElementById('eventEnd').value;
  var allDay = document.getElementById('allDay').checked;
  var salaId = document.getElementById('selecaoSala').value;

  var eventData = {
    title: title,
    sala_id: salaId,
    data_inicio: start,
    data_fim: end,
    allDay: allDay
  };

  var url = eventId ? `/atualizar_evento/${eventId}` : '/adicionar_evento';
  var method = eventId ? 'PUT' : 'POST';

  fetch(url, {
    method: method,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(eventData)
  })
    .then(response => {
      if (!response.ok) {
        throw new Error('Falha na requisição: ' + response.statusText);
      }
      return response.json();
    })
    .then(data => {
      alert('Evento salvo com sucesso!');
      closeCustomModal();
      calendar.refetchEvents();
    })
    .catch(error => {
      alert('Erro ao salvar o evento: ' + error.message);
    });
}

function closeCustomModal() {
  document.getElementById('customModal').style.display = 'none';
}

function redirectToBrazilAndOpenOrganograma() {
  sessionStorage.setItem('openOrganograma', 'true');
  window.location.href = '/organizacao-brazil';
}

