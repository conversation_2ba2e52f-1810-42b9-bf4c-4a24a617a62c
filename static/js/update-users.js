document.addEventListener('DOMContentLoaded', function () {
    var atualizarLinks = document.querySelectorAll('.atualizar-link');
    atualizarLinks.forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            var userId = this.getAttribute('data-user-id');
            var nome = this.closest('tr').querySelector('input[name="nome"]').value;
            var email = this.closest('tr').querySelector('input[name="email"]').value;
            var telefone = this.closest('tr').querySelector('input[name="telefone"]').value;
            var profileImage = document.getElementById('profile_image').files[0];

            atualizarUsuario(userId, nome, email, telefone, profileImage);
        });
    });
});

function atualizarUsuario(userId, nome, email, telefone, profileImage) {
    const dados = new FormData();
    dados.append('user_id', userId);
    dados.append('nome', nome);
    dados.append('email', email);
    dados.append('telefone', telefone);
    if (profileImage) {
        dados.append('profile_image', profileImage);
    }

    fetch('/alteracao_cadastral', {
        method: 'POST',
        body: dados,
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        alert(data.message);
        location.reload();
    })
    .catch(error => {
        console.error('Erro na atualização:', error);
        alert('Erro ao processar a solicitação.');
    });
}

