document.addEventListener('DOMContentLoaded', function () {
    // Seleciona todas as linhas clicáveis na tabela
    document.querySelectorAll('.clickable-row').forEach(function (row) {
        row.addEventListener('click', function () {
            const grupo = this.querySelector('[data-label="grupo"]').textContent.trim();
            const operadora = this.querySelector('[data-label="operadora"]').textContent.trim();
            const modalidade = this.querySelector('[data-label="modalidade"]').textContent.trim();

            openComissoesModal(grupo, operadora, modalidade);
        });
    });

    // Fecha o modal ao clicar no botão
    document.getElementById('closeModalBtn').addEventListener('click', function () {
        document.getElementById('comissoesModal').style.display = 'none';
    });

    document.getElementById('gradeSelect').addEventListener('change', function () {
        const grupo = this.dataset.grupo;
        const operadora = this.dataset.operadora;
        const modalidade = this.dataset.modalidade;
        const grade = this.value;

        loadComissoesTable(grupo, operadora, modalidade, 'total', grade);
    });

    // Inicializa os filtros da tabela
    initTableFilters();

    // Carrega os filtros de Operadora e Modalidade
    loadFilters(); // <--- Adiciona essa linha para carregar os filtros

    // Evento de busca
    const searchInput = document.getElementById("searchInput");
    searchInput.addEventListener('keyup', searchTable);

    // Adiciona evento ao botão de exportação
    const exportExcelBtn = document.getElementById('exportExcelBtn');
    if (exportExcelBtn) {
        exportExcelBtn.addEventListener('click', function () {
            window.location.href = '/export_usuarios';
        });
    }
});

function openComissoesModal(grupo, operadora, modalidade) {
    const modal = document.getElementById('comissoesModal');
    const gradeSelect = document.getElementById('gradeSelect');

    // Armazena os valores para uso posterior
    gradeSelect.dataset.grupo = grupo;
    gradeSelect.dataset.operadora = operadora;
    gradeSelect.dataset.modalidade = modalidade;

    // Codifica os valores para garantir que a URL seja válida
    const encodedGrupo = encodeURIComponent(grupo);
    const encodedOperadora = encodeURIComponent(operadora);
    const encodedModalidade = encodeURIComponent(modalidade);

    // Abre o modal
    modal.style.display = 'block';

    // Mapeamento de nomes
    const gradeMapping = {
        'total': 'Total',
        'franquia-2021': 'Franquia',
        'co-working': 'Co-Working',
        'brh-solution': 'BRH Solution',
        'confiance': 'Confiance',
        'ext-susep': 'Ext-Susep',
        'yolo': 'Yolo BRH'
    };

    // Seleciona a grade "Total" automaticamente
    const defaultGrade = 'total';

    // Faz a requisição para obter as grades
    fetch(`/grade-comissoes?grupo=${encodedGrupo}&operadora=${encodedOperadora}&modalidade=${encodedModalidade}`)
        .then(response => response.json())
        .then(data => {
            if (data.grades && data.grades.length > 0) {
                // Ordena as grades para garantir que 'total' fique em primeiro
                data.grades.sort((a, b) => (a.toLowerCase() === 'total' ? -1 : 1));

                // Renderiza as opções do select com o mapeamento aplicado
                gradeSelect.innerHTML = data.grades.map(grade => {
                    const displayName = gradeMapping[grade] || grade; // Usa o mapeamento ou o nome original se não houver mapeamento
                    return `<option value="${grade}">${displayName}</option>`;
                }).join('');

                // Carrega a tabela usando a grade "Total" (ou a primeira disponível)
                loadComissoesTable(grupo, operadora, modalidade, defaultGrade, data.grades[0]);
            }
        })
        .catch(error => {
            console.error('Erro ao carregar as grades:', error);
        });
}

function fetchGrades(grupo, operadora, modalidade) {
    const gradeSelect = document.getElementById('gradeSelect');
    gradeSelect.innerHTML = '';

    fetch(`/grade-comissoes?grupo=${grupo}&operadora=${operadora}&modalidade=${modalidade}`)
        .then(response => response.json())
        .then(data => {
            if (data.grades && data.grades.length > 0) {
                // Ordena as grades para garantir que 'total' fique em primeiro
                data.grades.sort((a, b) => (a.toLowerCase() === 'total' ? -1 : 1));

                // Renderiza as opções do select
                gradeSelect.innerHTML = data.grades.map(grade => `<option value="${grade}">${grade}</option>`).join('');

                // Carrega a tabela usando a primeira grade disponível (sempre "total")
                loadComissoesTable(grupo, operadora, modalidade, 'total', data.grades[0]);
            }
        })
        .catch(error => {
            console.error('Erro ao carregar as grades:', error);
        });
}

function loadComissoesTable(grupo, operadora, modalidade, grade1, grade2) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    const tableContainer = document.getElementById('comissoesTableContainer');
    const tableBody = document.getElementById('comissoesTable').querySelector('tbody');

    loadingIndicator.style.display = 'block';
    tableContainer.style.display = 'none';
    tableBody.innerHTML = '';

    fetch(`/consulta_tabela?grupo=${encodeURIComponent(grupo)}&operadora=${encodeURIComponent(operadora)}&modalidade=${encodeURIComponent(modalidade)}&grade1=${encodeURIComponent(grade1)}&grade2=${encodeURIComponent(grade2)}`)
        .then(response => response.json())
        .then(data => {
            if (data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="100%">Nenhum dado encontrado para os parâmetros selecionados.</td></tr>';
                loadingIndicator.style.display = 'none';
                tableContainer.style.display = 'block';
                return;
            }

            // Identifica as parcelas que possuem valores em alguma linha
            const parcelasComValores = new Set();
            data.forEach(row => {
                row.slice(2).forEach((value, index) => {
                    if (value !== null && value !== '') {
                        parcelasComValores.add(index + 1); // Armazena o número da parcela
                    }
                });
            });

            // Converte o Set em uma lista e ordena
            const parcelasList = Array.from(parcelasComValores).sort((a, b) => a - b);

            // Define as colunas, incluindo apenas as parcelas com valores
            const columns = ['Comissionáveis', 'Totais', ...parcelasList.map(i => `${i}ª_Parcela`)];

            // Renderiza o cabeçalho da tabela
            const headerHtml = `
                <tr>
                    ${columns.map(col => `<th>${col.replace('_', ' ')}</th>`).join('')}
                </tr>
            `;
            document.querySelector('#comissoesTable thead').innerHTML = headerHtml;

            // Renderiza as linhas da tabela com valores convertidos em porcentagens
            const rows = data.map(row => {
                const rowHtml = `
                    <tr>
                        ${row.slice(0, 1).map(value => `<td>${value !== null && value !== '' ? value : '-'}</td>`).join('')}
                        ${row.slice(1, 2).map(value => `<td>${value !== null && value !== '' ? (parseFloat(value) * 100).toFixed(2) + '%' : '-'}</td>`).join('')}
                        ${parcelasList.map(i => {
                            const value = row[i + 1]; // +1 porque a indexação começa em 2 para as parcelas
                            return `<td>${value !== null && value !== '' ? (parseFloat(value) * 100).toFixed(2) + '%' : '-'}</td>`;
                        }).join('')}
                    </tr>
                `;
                return rowHtml;
            });

            // Adiciona as linhas e o spread ao corpo da tabela
            tableBody.innerHTML = rows.join('') + calculateSpreadRow(data, parcelasList.length);
            loadingIndicator.style.display = 'none';
            tableContainer.style.display = 'block';
        })
        .catch(error => {
            console.error('Erro ao carregar a tabela de comissões:', error);
            loadingIndicator.style.display = 'none';
        });
}

function calculateSpreadRow(data, maxParcelas) {
    // Encontra a linha que contém os totais
    const totais = data.find(row => row[0].toLowerCase() === 'total') || [];

    // Calcula o spread para cada parcela
    const spreadValues = Array.from({ length: maxParcelas }, (_, index) => {
        // Ajustamos para pegar a parcela correta
        const totalVal = parseFloat(totais[index + 1]) || 0;

        // Calculamos a soma dos valores comissionáveis para esta parcela
        const comissionavel = data
            .filter(row => row[0].toLowerCase() !== 'total') // Excluímos a linha de totais
            .reduce((acc, row) => acc + (parseFloat(row[index + 1]) || 0), 0); // Somamos os valores das parcelas

        // Calcula o spread como diferença entre total e comissionável
        const spread = ((totalVal - comissionavel) * 100).toFixed(2) + '%';
        return spread !== 'NaN%' ? spread : '-'; // Tratamos valores inválidos
    });

    // Retorna a linha de spread completa
    return `
        <tr>
            <td>Spread</td>
            ${spreadValues.map(val => `<td>${val}</td>`).join('')}
        </tr>
    `;
}

function initTableFilters() {
    const headers = document.querySelectorAll('.comissoes-table th');

    headers.forEach((header, index) => {
        const button = header.querySelector('.filter-button');
        if (button) {
            button.addEventListener('click', function (event) {
                event.stopPropagation(); // Impede a propagação do evento para fechar o dropdown
                toggleDropdown(event, index);
            });
        }
    });
}

function toggleDropdown(event, columnIndex) {
    // Verifica se o dropdown já está aberto
    const currentDropdown = document.querySelector('.dropdown-menu');
    if (currentDropdown && currentDropdown.dataset.columnIndex == columnIndex) {
        closeAllDropdowns(); // Fecha o dropdown se já estiver aberto
        return; // Sai da função se o dropdown for o mesmo que já está aberto
    }
    closeAllDropdowns(); // Fecha outros dropdowns abertos
    const dropdown = createDropdown(columnIndex);
    dropdown.dataset.columnIndex = columnIndex; // Armazena o índice da coluna no dropdown
    document.body.appendChild(dropdown);
    positionDropdown(dropdown, event.target);

    dropdown.addEventListener('click', function (event) {
        event.stopPropagation();
    });

    // Adiciona um listener para fechar o dropdown quando clicar fora
    document.addEventListener('click', closeAllDropdowns, { once: true });
}

function createDropdown(columnIndex) {
    const dropdown = document.createElement('div');
    dropdown.classList.add('dropdown-menu', 'show');
    const uniqueValues = getUniqueColumnValues(columnIndex);

    dropdown.innerHTML = `
<div class="sort-options">
<button onclick="sortColumn(${columnIndex}, 'asc')"><i class="bi bi-sort-alpha-down"></i></button><p>Classificar de A a Z</p>
</div>
<div class="sort-options">
<button onclick="sortColumn(${columnIndex}, 'desc')"><i class="bi bi-sort-alpha-down-alt"></i></button><p>Classificar de Z a A</p>
</div>
<input type="text" placeholder="Pesquisar..." oninput="filterDropdownSearch(this, ${columnIndex})">
<ul class="filter-options">${generateFilterOptions(uniqueValues)}</ul>
<div class="filter-actions">
<button onclick="applyFilters(${columnIndex})">Aplicar</button>
<button onclick="closeAllDropdowns()">Cancelar</button>
</div>
`;

    return dropdown;
}

function getUniqueColumnValues(columnIndex) {
    const values = new Set();
    document.querySelectorAll(`.comissoes-table tbody tr`).forEach(row => {
        const cellText = row.cells[columnIndex]?.innerText.trim();
        if (cellText) values.add(cellText);
    });
    return [...values];
}

function generateFilterOptions(uniqueValues) {
    return uniqueValues.map(value => {
        const safeValue = value.replace(/"/g, '&quot;');
        return `<li>
        <input type="checkbox" id="filter-${safeValue}" name="filter-option" value="${safeValue}">
        <label for="filter-${safeValue}">${value}</label>
    </li>`;
    }).join('');
}

function positionDropdown(dropdown, referenceElement) {
    const tableRect = document.querySelector('.comissoes-table').getBoundingClientRect();
    const buttonRect = referenceElement.getBoundingClientRect();
    const theadRect = document.querySelector('.comissoes-table thead').getBoundingClientRect();

    dropdown.style.position = 'absolute';

    dropdown.style.top = `${theadRect.bottom}px`;

    let leftPosition = buttonRect.left + (buttonRect.width / 2) - (dropdown.offsetWidth / 2);
    leftPosition = Math.max(tableRect.left, leftPosition);
    leftPosition = Math.min(leftPosition, tableRect.right - dropdown.offsetWidth);

    dropdown.style.left = `${leftPosition}px`;
}

function sortColumn(columnIndex, order) {
    const table = document.querySelector('.comissoes-table tbody');
    const rowsArray = Array.from(table.rows);

    const compareFunction = (rowA, rowB) => {
        const valueA = rowA.cells[columnIndex].innerText.toLowerCase();
        const valueB = rowB.cells[columnIndex].innerText.toLowerCase();

        if (order === 'asc') {
            return valueA.localeCompare(valueB);
        } else {
            return valueB.localeCompare(valueA);
        }
    };

    rowsArray.sort(compareFunction);
    rowsArray.forEach(row => table.appendChild(row));
}

function filterDropdownSearch(inputElement, columnIndex) {
    const searchValue = inputElement.value.toLowerCase();
    const options = inputElement.closest('.dropdown-menu').querySelectorAll('.filter-options li');
    options.forEach(option => {
        const matchesSearch = option.textContent.toLowerCase().includes(searchValue);
        option.style.display = matchesSearch ? '' : 'none';
    });
}

function applyFilters(columnIndex) {
    const checkboxes = document.querySelectorAll('.dropdown-menu .filter-options input[type="checkbox"]:checked');
    const filterValues = Array.from(checkboxes).map(cb => cb.value.toUpperCase());

    document.querySelectorAll('.comissoes-table tbody tr').forEach(row => {
        const cellText = row.cells[columnIndex].innerText.toUpperCase();
        if (filterValues.length === 0 || filterValues.includes(cellText)) {
            row.style.display = "";
        } else {
            row.style.display = "none";
        }
    });

    closeAllDropdowns();
}

function closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown-menu');
    dropdowns.forEach(dropdown => dropdown.remove());
}

function searchTable() {
    const input = document.getElementById("searchInput");
    const filter = input.value.toUpperCase();
    const table = document.querySelector(".comissoes-table");
    const tr = table.getElementsByTagName("tr");

    for (let i = 1; i < tr.length; i++) {
        let displayRow = false;

        const td = tr[i].getElementsByTagName("td");

        for (let j = 0; j < td.length; j++) {
            if (td[j]) {
                const txtValue = td[j].textContent || td[j].innerText;

                if (txtValue.toUpperCase().indexOf(filter) > -1) {
                    displayRow = true;
                    break;
                }
            }
        }

        tr[i].style.display = displayRow ? "" : "none";
    }
}

document.getElementById('clearFilterBtn').addEventListener('click', function () {
    // Limpa o campo de busca
    document.getElementById('searchInput').value = '';

    // Reseta os selects de filtro para "Todos"
    document.getElementById('filter-operadora').value = '';
    document.getElementById('filter-modalidade').value = '';

    // Reaplica a busca e os filtros (no caso, sem filtros aplicados)
    searchTable();
    filterTable();
});

function loadFilters() {
    const operadoras = [...new Set(Array.from(document.querySelectorAll('.comissoes-table tbody tr')).map(row => row.cells[0].innerText))];
    const modalidades = [...new Set(Array.from(document.querySelectorAll('.comissoes-table tbody tr')).map(row => row.cells[1].innerText))];

    const operadoraSelect = document.getElementById('filter-operadora');
    const modalidadeSelect = document.getElementById('filter-modalidade');

    operadoraSelect.innerHTML += operadoras.map(operadora => `<option value="${operadora}">${operadora}</option>`).join('');
    modalidadeSelect.innerHTML += modalidades.map(modalidade => `<option value="${modalidade}">${modalidade}</option>`).join('');
}

function filterTable() {
    const operadoraFilter = document.getElementById('filter-operadora').value.toUpperCase();
    const modalidadeFilter = document.getElementById('filter-modalidade').value.toUpperCase();

    const table = document.querySelector(".comissoes-table");
    const tr = table.getElementsByTagName("tr");

    for (let i = 1; i < tr.length; i++) {
        let tdOperadora = tr[i].getElementsByTagName("td")[0];
        let tdModalidade = tr[i].getElementsByTagName("td")[1];

        if (tdOperadora && tdModalidade) {
            let operadoraValue = tdOperadora.textContent || tdOperadora.innerText;
            let modalidadeValue = tdModalidade.textContent || tdModalidade.innerText;

            if (
                (operadoraFilter === "" || operadoraValue.toUpperCase().indexOf(operadoraFilter) > -1) &&
                (modalidadeFilter === "" || modalidadeValue.toUpperCase().indexOf(modalidadeFilter) > -1)
            ) {
                tr[i].style.display = "";
            } else {
                tr[i].style.display = "none";
            }
        }
    }
}
