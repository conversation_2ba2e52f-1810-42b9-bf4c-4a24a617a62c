document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM carregado para acessos_colaborador");

    // Função para carregar conteúdo dinâmico ao clicar no link de acessos
    const acessosLink = document.getElementById('acessosLink');
    
    if (acessosLink) {
        acessosLink.addEventListener('click', function(event) {
            event.preventDefault();  // Impede o comportamento padrão do link
            console.log("Link de acessos clicado");

            fetch('/acessos-colaborador')  // Chama a rota diretamente
                .then(response => response.text())
                .then(data => {
                    const mainContent = document.getElementById('mainContent');
                    console.log('Conteúdo carregado:', data);
                    mainContent.innerHTML = data; // Insere o conteúdo da página de acessos

                    // Linha de removeClasses removida
                    mainContent.classList.add('acessos-colaborador');
                    initTableFilters();
                    sortColumn(0, 'asc'); 
                    createPasswordModal();
                })
                .catch(error => console.error('Erro ao carregar o conteúdo de acessos:', error));
        });
    } else {
        console.error("Elemento acessosLink não encontrado.");
    }

    initTableFilters();
    sortColumn(0, 'asc'); 
    createPasswordModal();
});


let activeFilters = {}; // Armazena os filtros ativos

function initTableFilters() {
    const headers = document.querySelectorAll('.acessos-colaborador-table th');

    headers.forEach((header, index) => {
        const button = header.querySelector('.filter-button');
        if (button) {
            button.addEventListener('click', function (event) {
                event.stopPropagation();
                toggleDropdown(event, index);
            });
        }
    });
}

function toggleDropdown(event, columnIndex) {
    const currentDropdown = document.querySelector('.dropdown-menu');
    if (currentDropdown && currentDropdown.dataset.columnIndex == columnIndex) {
        closeAllDropdowns();
        return;
    }
    closeAllDropdowns();
    const dropdown = createDropdown(columnIndex);
    dropdown.dataset.columnIndex = columnIndex;
    document.body.appendChild(dropdown);
    positionDropdown(dropdown, event.target);

    dropdown.addEventListener('click', function (event) {
        event.stopPropagation();
    });

    document.addEventListener('click', closeAllDropdowns, { once: true });
}

function createDropdown(columnIndex) {
    const dropdown = document.createElement('div');
    dropdown.classList.add('dropdown-menu', 'show');
    const uniqueValues = getUniqueColumnValues(columnIndex);

    dropdown.innerHTML = `
        <div class="sort-options">
            <button onclick="sortColumn(${columnIndex}, 'asc')"><i class="bi bi-sort-alpha-down"></i></button><p>Classificar de A a Z</p>
        </div>
        <div class="sort-options">
            <button onclick="sortColumn(${columnIndex}, 'desc')"><i class="bi bi-sort-alpha-down-alt"></i></button><p>Classificar de Z a A</p>
        </div>
        <input type="text" placeholder="Pesquisar..." oninput="filterDropdownSearch(this, ${columnIndex})">
        <ul class="filter-options">${generateFilterOptions(uniqueValues)}</ul>
        <div class="filter-actions">
            <button onclick="applyFilters(${columnIndex})">Aplicar</button>
            <button onclick="closeAllDropdowns()">Cancelar</button>
        </div>
    `;

    return dropdown;
}

function getUniqueColumnValues(columnIndex) {
    const values = new Set();
    document.querySelectorAll('.acessos-colaborador-table tbody tr').forEach(row => {
        const cellText = row.cells[columnIndex]?.innerText.trim();
        if (cellText) values.add(cellText);
    });
    return [...values];
}

function generateFilterOptions(uniqueValues) {
    return uniqueValues.map(value => {
        const safeValue = value.replace(/"/g, '&quot;');
        return `<li>
            <input type="checkbox" id="filter-${safeValue}" name="filter-option" value="${safeValue}">
            <label for="filter-${safeValue}">${value}</label>
        </li>`;
    }).join('');
}

function positionDropdown(dropdown, referenceElement) {
    const tableRect = document.querySelector('.acessos-colaborador-table').getBoundingClientRect();
    const buttonRect = referenceElement.getBoundingClientRect();
    const theadRect = document.querySelector('.acessos-colaborador-table thead').getBoundingClientRect();

    dropdown.style.position = 'absolute';
    dropdown.style.top = `${theadRect.bottom}px`;

    let leftPosition = buttonRect.left + (buttonRect.width / 2) - (dropdown.offsetWidth / 2);
    leftPosition = Math.max(tableRect.left, leftPosition);
    leftPosition = Math.min(leftPosition, tableRect.right - dropdown.offsetWidth);

    dropdown.style.left = `${leftPosition}px`;
}

function sortColumn(columnIndex, order) {
    const table = document.querySelector('.acessos-colaborador-table tbody');
    const rowsArray = Array.from(table.rows);

    const compareFunction = (rowA, rowB) => {
        const valueA = rowA.cells[columnIndex].innerText.toLowerCase();
        const valueB = rowB.cells[columnIndex].innerText.toLowerCase();

        if (order === 'asc') {
            return valueA.localeCompare(valueB);
        } else {
            return valueB.localeCompare(valueA);
        }
    };

    rowsArray.sort(compareFunction);
    rowsArray.forEach(row => table.appendChild(row));
}

function filterDropdownSearch(inputElement, columnIndex) {
    const searchValue = inputElement.value.toLowerCase();
    const options = inputElement.closest('.dropdown-menu').querySelectorAll('.filter-options li');
    options.forEach(option => {
        const matchesSearch = option.textContent.toLowerCase().includes(searchValue);
        option.style.display = matchesSearch ? '' : 'none';
    });
}

function applyFilters(columnIndex) {
    const checkboxes = document.querySelectorAll('.dropdown-menu .filter-options input[type="checkbox"]:checked');
    const filterValues = Array.from(checkboxes).map(cb => cb.value.toUpperCase());

    document.querySelectorAll('.acessos-colaborador-table tbody tr').forEach(row => {
        const cellText = row.cells[columnIndex].innerText.toUpperCase();
        if (filterValues.length === 0 || filterValues.includes(cellText)) {
            row.style.display = "";
        } else {
            row.style.display = "none";
        }
    });

    closeAllDropdowns();
}

function closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown-menu');
    dropdowns.forEach(dropdown => dropdown.remove());
}

function searchTable() {
    const input = document.getElementById("searchInput");
    const filter = input.value.toUpperCase();
    const table = document.querySelector(".acessos-colaborador-table tbody");
    const tr = table.getElementsByTagName("tr");

    for (let i = 0; i < tr.length; i++) {
        let displayRow = false;
        const td = tr[i].getElementsByTagName("td");

        for (let j = 0; j < td.length; j++) {
            if (td[j]) {
                const columnClass = td[j].classList[0];
                if (["column-nome", "column-login", "column-unidade", "column-codigo"].includes(columnClass)) {
                    const txtValue = td[j].textContent || td[j].innerText;
                    if (txtValue.toUpperCase().indexOf(filter) > -1) {
                        displayRow = true;
                        break;
                    }
                }
            }
        }
        tr[i].style.display = displayRow ? "" : "none";
    }
}

function copyPassword(element) {
    const passwordText = element.parentElement.querySelector('.senha-text');
    const passwordValue = passwordText.dataset.password;
    navigator.clipboard.writeText(passwordValue).then(() => {
        alert("Senha copiada para a área de transferência.");
    }).catch(err => {
        console.error("Erro ao copiar senha: ", err);
    });
}

function togglePasswordVisibility(element) {
    const passwordText = element.parentElement.querySelector('.senha-text');
    const isPasswordHidden = passwordText.innerText === '***********';

    if (isPasswordHidden) {
        passwordText.innerText = passwordText.dataset.password;
        element.classList.replace('fa-eye', 'fa-eye-slash');
    } else {
        passwordText.innerText = '***********';
        element.classList.replace('fa-eye-slash', 'fa-eye');
    }
}

function createPasswordModal() {
    const modalHTML = `
        <div id="passwordModal" class="modal" style="display: none;">
            <div class="modal-content">
                <span class="close" onclick="closePasswordModal()">&times;</span>
                <h2>Alterar Senha</h2>
                <form id="updatePasswordForm" action="/atualizar_acesso" method="POST">
                    <input type="hidden" name="acesso_id" id="acesso_id">
                    <div class="input-portal">
                        <label for="new-password">Nova Senha:</label>
                        <input type="text" id="new-password" name="nova_senha" required>
                    </div>
                    <button class="gestao-btn-acessar" type="button" onclick="submitPasswordForm()">Salvar</button>
                </form>
            </div>
        </div>`;
    
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function openPasswordModal(acessoId) {
    document.getElementById('acesso_id').value = acessoId;
    document.getElementById('passwordModal').style.display = 'block';
}

function closePasswordModal() {
    document.getElementById('passwordModal').style.display = 'none';
}

function submitPasswordForm() {
    const newPassword = document.getElementById('new-password').value;

    if (newPassword) {
        document.getElementById('updatePasswordForm').submit();
    } else {
        alert('Por favor, insira uma nova senha.');
    }
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    activeFilters = {};

    const rows = document.querySelectorAll('.acessos-colaborador-table tbody tr');
    rows.forEach(row => {
        row.style.display = "";
    });

    const checkboxes = document.querySelectorAll('.filter-options input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    closeAllDropdowns();
}

document.getElementById('clearFilterBtn').addEventListener('click', clearFilters);
