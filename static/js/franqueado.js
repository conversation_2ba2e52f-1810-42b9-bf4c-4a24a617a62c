document.addEventListener('DOMContentLoaded', function () {
    const cards = document.querySelectorAll('.card[data-content]');
    cards.forEach(card => {
        card.addEventListener('click', function () {
            const contentType = card.getAttribute('data-content');
            loadContent(contentType);
        });
    });

    const docLinks = document.querySelectorAll('#documentos-container .sidebar ul li');
    docLinks.forEach(link => {
        link.addEventListener('click', function () {
            const topic = this.getAttribute('onclick').match(/'([^']+)'/)[1];
            loadDocumentContent(topic);
        });
    });

    const systemLinks = document.querySelectorAll('#sistemas-container .sidebar ul li');
    systemLinks.forEach(link => {
        link.addEventListener('click', function () {
            const topic = this.getAttribute('onclick').match(/'([^']+)'/)[1];
            loadsystemContent(topic);
        });
    });

    const chamadoLinks = document.querySelectorAll('#chamados-container .sidebar ul li');
    chamadoLinks.forEach(link => {
        link.addEventListener('click', function () {
            const topic = this.getAttribute('onclick').match(/'([^']+)'/)[1];
            loadChamadosContent(topic);
        });
    });

    const processLinks = document.querySelectorAll('#processos-container .sidebar ul li');
    processLinks.forEach(link => {
        link.addEventListener('click', function () {
            const topic = this.getAttribute('onclick').match(/'([^']+)'/)[1];
            if (topic === 'codigo-operadora') {
                loadFormContent();
            } else {
                loadProcessContent(topic);
            }
        });
    });
});

function loadContent(contentType) {
    if (contentType === 'help-desk') {
        showHelpDesk();
    } else if (contentType === 'documentos') {
        showDocumentos();
    } else if (contentType === 'processos') {
        showProcessos();
    } else if (contentType === 'sistemas') {
        showSystem();
    } else if (contentType === 'chamados') {
        showChamados();
    } else {
        const contentContainer = document.getElementById('content-container');
        let content = '';

        switch (contentType) {
            case 'desempenho':
                content = `
                    <h1>Desempenho</h1>
                    <p>Acompanhe aqui a performance da sua franquia.</p>
                    <!-- Adicione aqui o conteúdo relevante sobre desempenho -->
                `;
                break;
            case 'eventos':
                content = `
                    <h1>Eventos</h1>
                    <p>Veja aqui os próximos eventos e treinamentos.</p>
                    <!-- Adicione aqui o conteúdo relevante sobre eventos -->
                `;
                break;
            default:
                content = `<p>Conteúdo não encontrado.</p>`;
        }

        contentContainer.innerHTML = content;
    }
}

function showHelpDesk() {
    document.getElementById('help-desk-container').style.display = 'flex';
    document.querySelector('.franqueado-container').style.display = 'none';
    document.getElementById('documentos-container').style.display = 'none';
    document.getElementById('processos-container').style.display = 'none';
    document.getElementById('sistemas-container').style.display = 'none';
    document.getElementById('chamados-container').style.display = 'none';
}

function showDocumentos() {
    document.getElementById('documentos-container').style.display = 'flex';
    document.querySelector('.franqueado-container').style.display = 'none';
    document.getElementById('help-desk-container').style.display = 'none';
    document.getElementById('processos-container').style.display = 'none';
    document.getElementById('sistemas-container').style.display = 'none';
    document.getElementById('chamados-container').style.display = 'none';
}

function showProcessos() {
    document.getElementById('processos-container').style.display = 'flex';
    document.querySelector('.franqueado-container').style.display = 'none';
    document.getElementById('help-desk-container').style.display = 'none';
    document.getElementById('documentos-container').style.display = 'none';
    document.getElementById('sistemas-container').style.display = 'none';
    document.getElementById('chamados-container').style.display = 'none';
    document.getElementById('process-content').innerHTML = ''; // Limpa o conteúdo inicial
}

function showSystem() {
    document.getElementById('sistemas-container').style.display = 'flex';
    document.querySelector('.franqueado-container').style.display = 'none';
    document.getElementById('processos-container').style.display = 'none';
    document.getElementById('help-desk-container').style.display = 'none';
    document.getElementById('documentos-container').style.display = 'none';
    document.getElementById('chamados-container').style.display = 'none';
}

function showChamados() {
    document.getElementById('chamados-container').style.display = 'flex';
    document.querySelector('.franqueado-container').style.display = 'none';
    document.getElementById('processos-container').style.display = 'none';
    document.getElementById('help-desk-container').style.display = 'none';
    document.getElementById('documentos-container').style.display = 'none';
    document.getElementById('sistemas-container').style.display = 'none';
}

function toggleHelpDeskSteps(step) {
    const content = document.getElementById(`${step}-content`);
    const arrow = document.getElementById(`${step}-arrow`);
    if (content.style.display === "none" || content.style.display === "") {
        content.style.display = "block";
        arrow.classList.add('expanded');
        content.animate([
            { height: '0px', opacity: 0 },
            { height: `${content.scrollHeight}px`, opacity: 1 }
        ], {
            duration: 200,
            fill: 'forwards'
        });
    } else {
        content.animate([
            { height: `${content.scrollHeight}px`, opacity: 1 },
            { height: '0px', opacity: 0 }
        ], {
            duration: 200,
            fill: 'forwards'
        }).onfinish = function () {
            content.style.display = "none";
            arrow.classList.remove('expanded');
        };
    }
}

function loadHelpDeskContent(topic) {
    let content = '';
    switch (topic) {
        case 'redefinir-senha':
            content = `
                <div class="system-container">
                    <div class="system-header" onclick="toggleHelpDeskSteps('painel')">
                        <h4>Painel do Corretor</h4>
                        <span class="fas fa-chevron-right toggle-arrow" id="painel-arrow"></span>
                    </div>
                    <div class="system-content" id="painel-content">
                        <p>Clique no link “Esqueci minha senha”, digite seu e-mail cadastrado.</p>
                        <img src=".png" alt="Passo 1" style="width: 80%;">
                    </div>
                </div>
                <div class="system-container">
                    <div class="system-header" onclick="toggleHelpDeskSteps('intranet')">
                        <h4>Intranet</h4>
                        <span class="fas fa-chevron-right toggle-arrow" id="intranet-arrow"></span>
                    </div>
                    <div class="system-content" id="intranet-content">
                        <p>Digite seu e-mail registrado e clique em "Enviar".</p>
                        <img src="{{ url_for('static', filename='images/step2_image.png') }}" alt="Passo 2">
                    </div>
                </div>
                <div class="system-container">
                    <div class="system-header" onclick="toggleHelpDeskSteps('comunica')">
                        <h4>Comunica Brazil</h4>
                        <span class="fas fa-chevron-right toggle-arrow" id="comunica-arrow"></span>
                    </div>
                    <div class="system-content" id="comunica-content">
                        <p>Comunica Brazil.</p>
                        <img src="{{ url_for('static', filename='images/step3_image.png') }}" alt="Passo 3">
                    </div>
                </div>
                <div class="system-container">
                    <div class="system-header" onclick="toggleHelpDeskSteps('universidade')">
                        <h4>Universidade BRH</h4>
                        <span class="fas fa-chevron-right toggle-arrow" id="universidade-arrow"></span>
                    </div>
                    <div class="system-content" id="universidade-content">
                        <p>Universidade BRH.</p>
                        <img src="{{ url_for('static', filename='images/step4_image.png') }}" alt="Passo 4">
                    </div>
                </div>
                <div class="system-container">
                    <div class="system-header" onclick="toggleHelpDeskSteps('duvidas')">
                        <h4>Ainda tem dúvidas? Abra um chamado</h4>
                        <span class="fas fa-chevron-right toggle-arrow" id="duvidas-arrow"></span>
                    </div>
                    <div class="system-content" id="duvidas-content">
                        <p>Aqui mesmo em "Help Desk" em sua sidebar à esquerda.</p>
                    </div>
                </div>
            `;
            break;
        case 'problemas-login':
            content = `
                <h2>Acesso ao Painel do Corretor Clássico</h2>
                <p>Siga os passos abaixo:</p>
                <div class="step">
                    <h3>Passo 1: Acesse o Painel do Corretor, clicando em "Sistemas" > "Painel do Corretor PDC" na sua Intranet.</h3>
                    <p>Como franqueado, nosso time de apoio que efetuará seu cadastro de acesso ao Painel do Corretor Clássico através do e-mail que foi criado para sua corretora. 
                    Você receberá um e-mail da Trindade Tecnologia informando seu nome, o nome de sua corretora e um link através do qual cadastrará sua senha para acesso ao 
                    Painel do Corretor Clássico.</p>
                    <img src="https://raw.githubusercontent.com/BrazilHealth/images-brh/main/paginaloginintranetpdc4.png" alt="Passo 1" style="width: 80%;">
                </div>
                <div class="step">
                    <h3>Passo 2: Clique no link recebido.</h3>
                    <p>Clique no link “Clique aqui para cadastrar sua senha” para criar uma senha de acesso ao Painel do Corretor.</p>
                    <img src="https://raw.githubusercontent.com/BrazilHealth/images-brh/main/acesso_painel_classico_pdc.png" alt="Passo 2" style="width: 80%;">
                </div>
                <div class="step">
                    <h3>Passo 3: Acesse a página de login</h3>
                    <p>Após clicar em [REGISTRAR] você estará habilitado. Acesse o PDC através da Intranet. Verifique sua caixa de entrada (ou spam) e clique no link de redefinição de senha.</p>
                    <img src="https://raw.githubusercontent.com/BrazilHealth/images-brh/main/registro 3 pdc.png" alt="Passo 3" style="width: 80%;">
                </div>
                <div class="step">
                    <h3>Passo 4: Confirmação</h3>
                    <p>Você verá uma mensagem de confirmação de que seu cadastro foi efetivado com sucesso. Agora você pode fazer login!</p>
                </div>
            `;
            break;
        case 'abrir-chamado':
            content = `<h2>Abra seu chamado</h2><p>Para abrir um novo chamado, preencha o formulário abaixo...</p>
                            <div id="chamado-container">
                                <h2>Novidades em Breve</h2>
                                <div id="chamado-content" class="d-flex justify-content-center align-items-center">
                                    <div class="card custom-card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title">Novidades em Breve</h5>
                                            <p class="card-text">Estamos preparando novas atualizações para essa página. Fique atento!</p>
                                            <div class="loading-text" role="status">
                                                <span>C</span><span>a</span><span>r</span><span>r</span><span>e</span><span>g</span><span>a</span><span>n</span><span>d</span><span>o</span><span>.</span><span>.</span><span>.</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                `;
            break;
        case 'status':
            content = '<h2>Status do Sistemas</h2><p>Confira o status atual do Painel do Corretor - PDC & Trindade <a href="https://statusgeral.trindadetecnologia.com.br/" target="_blank">https://statusgeral.trindadetecnologia.com.br/</a></p>';
            break;
        case 'ramais':
            content = '<h2>Ramais Internos BRH</h2><p>Precisa falar com alguma área interna? Veja os ramais da nossa corretora</a></p>';
            break;
        case 'voltar':
            window.location.href = 'franqueado'; // Redirecionar para franqueado.html
            return;
        default:
            content = '<p>Conteúdo não encontrado.</p>';
    }
    document.getElementById('help-desk-content').innerHTML = content;
}

function loadDocumentContent(topic) {
    let content = '';
    switch (topic) {
        case 'circular-oferta':
            content = `<h2>Circular de Oferta de Franquia (COF)</h2><p>A Circular de Oferta de Franquia (COF) contém informações importantes sobre as condições e obrigações do franqueado e da franqueadora.</p>
                        <div id="manual-container">
                            <h2>Novidades em Breve</h2>
                            <div id="manual-content" class="d-flex justify-content-center align-items-center">
                                <div class="card custom-card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Novidades em Breve</h5>
                                        <p class="card-text">Estamos preparando novas atualizações para essa página. Fique atento!</p>
                                        <div class="loading-text" role="status">
                                            <span>C</span><span>a</span><span>r</span><span>r</span><span>e</span><span>g</span><span>a</span><span>n</span><span>d</span><span>o</span><span>.</span><span>.</span><span>.</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
            `;
            break;
        case 'contrato-franquia':
            content = `<h2>Contrato de Franquia</h2><p>O Contrato de Franquia define os termos e condições do relacionamento entre o franqueado e a franqueadora.</p>
                        <div id="manual-container">
                            <h2>Novidades em Breve</h2>
                            <div id="manual-content" class="d-flex justify-content-center align-items-center">
                                <div class="card custom-card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Novidades em Breve</h5>
                                        <p class="card-text">Estamos preparando novas atualizações para essa página. Fique atento!</p>
                                        <div class="loading-text" role="status">
                                            <span>C</span><span>a</span><span>r</span><span>r</span><span>e</span><span>g</span><span>a</span><span>n</span><span>d</span><span>o</span><span>.</span><span>.</span><span>.</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
            `;
            break;
        case 'plano-negocios':
            content = `
                <h2>Plano de Negócios</h2>
                <p>O Plano de Negócios fornece uma visão detalhada das estratégias e planos da franquia.</p>
                <!-- Formulário de Plano de Ação -->
                <div class="container mt-5">
                    <h2>Plano de Ação Unidade Franqueada Brazil Health</h2>
                    <form id="plano-acao-form">
                        <div class="form-group">
                            <label for="unidade">Unidade:</label>
                            <input type="text" class="form-control" id="unidade" name="unidade" required>
                        </div>
                        <div class="form-group">
                            <label for="franqueado">Franqueado:</label>
                            <input type="text" class="form-control" id="franqueado" name="franqueado" required>
                        </div>
                        <div class="form-group">
                            <label for="preenchido_por">Preenchido por:</label>
                            <input type="text" class="form-control" id="preenchido_por" name="preenchido_por" required>
                        </div>
                        <div class="form-group">
                            <label for="data">Data:</label>
                            <input type="date" class="form-control" id="data" name="data" required>
                        </div>
                        <h4>Tabela de Ações</h4>
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Prioridade</th>
                                    <th>Número</th>
                                    <th>Ação (O que)</th>
                                    <th>Execução (Como)</th>
                                    <th>Prazo (Quando)</th>
                                    <th>Responsável</th>
                                    <th>Observações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Exemplo de uma linha -->
                                <tr>
                                    <td>
                                        <select class="form-control" name="prioridade[]">
                                            <option value="Alta">Alta</option>
                                            <option value="Média">Média</option>
                                            <option value="Baixa">Baixa</option>
                                        </select>
                                    </td>
                                    <td><input type="number" class="form-control" name="no[]" required></td>
                                    <td><input type="text" class="form-control" name="acao[]" required></td>
                                    <td><input type="text" class="form-control" name="execucao[]" required></td>
                                    <td><input type="date" class="form-control" name="prazo[]" required></td>
                                    <td><input type="text" class="form-control" name="responsavel[]" required></td>
                                    <td><input type="text" class="form-control" name="observacoes[]"></td>
                                </tr>
                                <!-- Repetir as linhas conforme necessário -->
                            </tbody>
                        </table>
                        <div class="form-group">
                            <label for="anotacoes">Anotações:</label>
                            <textarea class="form-control" id="anotacoes" name="anotacoes" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="data_assinatura">Data:</label>
                            <input type="date" class="form-control" id="data_assinatura" name="data_assinatura" required>
                        </div>
                        <div class="form-group">
                            <label for="assinatura_franqueado">Franqueado:</label>
                            <input type="text" class="form-control" id="assinatura_franqueado" name="assinatura_franqueado" required>
                        </div>
                        <div class="form-group">
                            <label for="assinatura_supervisor">Supervisor Franquias:</label>
                            <input type="text" class="form-control" id="assinatura_supervisor" name="assinatura_supervisor" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Enviar</button>
                    </form>
                </div>
            `;
            break;
        case 'manual-marca':
            content = `<h2>Manual da Marca</h2><p>O Manual da Marca contém diretrizes sobre o uso correto da marca e identidade visual da franquia.</p>
                        <div id="manual-container">
                            <h2>Novidades em Breve</h2>
                            <div id="manual-content" class="d-flex justify-content-center align-items-center">
                                <div class="card custom-card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Novidades em Breve</h5>
                                        <p class="card-text">Estamos preparando novas atualizações para essa página. Fique atento!</p>
                                        <div class="loading-text" role="status">
                                            <span>C</span><span>a</span><span>r</span><span>r</span><span>e</span><span>g</span><span>a</span><span>n</span><span>d</span><span>o</span><span>.</span><span>.</span><span>.</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
            `;
            break;
        case 'certificados':
            content = `<h2>Certificados</h2><p>Os Certificados reconhecem a conformidade com os padrões de qualidade e outras conquistas importantes da franquia.</p>
                        <div id="manual-container">
                            <h2>Novidades em Breve</h2>
                            <div id="manual-content" class="d-flex justify-content-center align-items-center">
                                <div class="card custom-card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Novidades em Breve</h5>
                                        <p class="card-text">Estamos preparando novas atualizações para essa página. Fique atento!</p>
                                        <div class="loading-text" role="status">
                                            <span>C</span><span>a</span><span>r</span><span>r</span><span>e</span><span>g</span><span>a</span><span>n</span><span>d</span><span>o</span><span>.</span><span>.</span><span>.</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
            `;
            break;
        case 'tabela-comissao':
            content = `<h2>Tabela de Comissionamento</h2><p>Confira potenciais ganhos com sua Franquia.</p>
                        <div id="manual-container">
                            <h2>Novidades em Breve</h2>
                            <div id="manual-content" class="d-flex justify-content-center align-items-center">
                                <div class="card custom-card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Novidades em Breve</h5>
                                        <p class="card-text">Estamos preparando novas atualizações para essa página. Fique atento!</p>
                                        <div class="loading-text" role="status">
                                            <span>C</span><span>a</span><span>r</span><span>r</span><span>e</span><span>g</span><span>a</span><span>n</span><span>d</span><span>o</span><span>.</span><span>.</span><span>.</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
            `;
            break;
        case 'comunicados':
            content = `<h2>Comunicados Oficiais</h2><p>Confira os comunicados oficiais da sua Franquia e não esqueça de dar o aceite/lido.</p>
                        <div id="manual-container">
                            <h2>Novidades em Breve</h2>
                            <div id="manual-content" class="d-flex justify-content-center align-items-center">
                                <div class="card custom-card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Novidades em Breve</h5>
                                        <p class="card-text">Estamos preparando novas atualizações para essa página. Fique atento!</p>
                                        <div class="loading-text" role="status">
                                            <span>C</span><span>a</span><span>r</span><span>r</span><span>e</span><span>g</span><span>a</span><span>n</span><span>d</span><span>o</span><span>.</span><span>.</span><span>.</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
            `;
            break;
        case 'voltar':
            window.location.href = 'franqueado'; // Redirecionar para franqueado.html
            return;
        default:
            content = '<p>Conteúdo não encontrado.</p>';
    }

    document.getElementById('document-content').innerHTML = content;
}

function loadProcessContent(topic) {
    let content = '';
    switch (topic) {
        case 'proposta':
            content = `
                        <div class="propostas-container">
                            <h2>Propostas em Andamento</h2>
                            <div class="processos">
                                <!-- Saúde -->
                                <div class="processo-item">
                                    <div class="processo-header" onclick="togglePropostas('saude')">
                                        <h4>Saúde</h4>
                                        <span class="fas fa-chevron-right toggle-arrow" id="saude-arrow"></span>
                                    </div>
                                    <div class="processo-content" id="saude-content">
                                        <ul>
                                            <li>Individual</li>
                                            <li>Adesão</li>
                                            <li>PME (02 a 29 vidas)</li>
                                            <li>PME (30 a 99 vidas)</li>
                                            <li>Empresarial (+100 vidas)</li>
                                        </ul>
                                    </div>
                                </div>
                                <!-- Odonto -->
                                <div class="processo-item">
                                    <div class="processo-header" onclick="togglePropostas('odonto')">
                                        <h4>Odonto</h4>
                                        <span class="fas fa-chevron-right toggle-arrow" id="odonto-arrow"></span>
                                    </div>
                                    <div class="processo-content" id="odonto-content">
                                        <ul>
                                            <li>Individual</li>
                                            <li>Adesão</li>
                                            <li>PME (02 a 29 vidas)</li>
                                            <li>PME (30 a 99 vidas)</li>
                                            <li>Empresarial (+100 vidas)</li>
                                        </ul>
                                    </div>
                                </div>
                                <!-- Pet -->
                                <div class="processo-item">
                                    <div class="processo-header" onclick="togglePropostas('pet')">
                                        <h4>Pet</h4>
                                        <span class="fas fa-chevron-right toggle-arrow" id="pet-arrow"></span>
                                    </div>
                                    <div class="processo-content" id="pet-content">
                                        <!-- Subitens de Pet, se existirem -->
                                    </div>
                                </div>
                                <!-- Auto -->
                                <div class="processo-item">
                                    <div class="processo-header" onclick="togglePropostas('auto')">
                                        <h4>Auto</h4>
                                        <span class="fas fa-chevron-right toggle-arrow" id="auto-arrow"></span>
                                    </div>
                                    <div class="processo-content" id="auto-content">
                                        <!-- Subitens de Auto, se existirem -->
                                    </div>
                                </div>
                                <!-- Vida -->
                                <div class="processo-item">
                                    <div class="processo-header" onclick="togglePropostas('vida')">
                                        <h4>Vida</h4>
                                        <span class="fas fa-chevron-right toggle-arrow" id="vida-arrow"></span>
                                    </div>
                                    <div class="processo-content" id="vida-content">
                                        <ul>
                                            <li>Individual</li>
                                            <li>Adesão</li>
                                            <li>PME (02 a 29 vidas)</li>
                                            <li>PME (30 a 99 vidas)</li>
                                            <li>Empresarial (+100 vidas)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
            break;
        case 'solicitacoes':
            content = `<h2>Solicitações nas operadoras</h2><p>Veja o status das suas solicitações aqui...</p>
                        <div id="manual-container">
                            <h2>Novidades em Breve</h2>
                            <div id="manual-content" class="d-flex justify-content-center align-items-center">
                                <div class="card custom-card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Novidades em Breve</h5>
                                        <p class="card-text">Estamos preparando novas atualizações para essa página. Fique atento!</p>
                                        <div class="loading-text" role="status">
                                            <span>C</span><span>a</span><span>r</span><span>r</span><span>e</span><span>g</span><span>a</span><span>n</span><span>d</span><span>o</span><span>.</span><span>.</span><span>.</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
            `;
            break;
        case 'alteracao-conta':
            content = `<h2>Alteração de conta bancária</h2><p>Informações sobre como alterar sua conta bancária...</p>
                        <div id="manual-container">
                            <h2>Novidades em Breve</h2>
                            <div id="manual-content" class="d-flex justify-content-center align-items-center">
                                <div class="card custom-card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Novidades em Breve</h5>
                                        <p class="card-text">Estamos preparando novas atualizações para essa página. Fique atento!</p>
                                        <div class="loading-text" role="status">
                                            <span>C</span><span>a</span><span>r</span><span>r</span><span>e</span><span>g</span><span>a</span><span>n</span><span>d</span><span>o</span><span>.</span><span>.</span><span>.</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
            `;
            break;
        case 'codigo-operadora':
            content = '<h2>Abertura de código na operadora</h2><p>Detalhes sobre a abertura de código na operadora...</p>';
            break;
        case 'formularios':
            content = `<h2>Formulários</h2><p>Para solicitações internas da corretora.</p>
                        <div id="manual-container">
                            <h2>Novidades em Breve</h2>
                            <div id="manual-content" class="d-flex justify-content-center align-items-center">
                                <div class="card custom-card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Novidades em Breve</h5>
                                        <p class="card-text">Estamos preparando novas atualizações para essa página. Fique atento!</p>
                                        <div class="loading-text" role="status">
                                            <span>C</span><span>a</span><span>r</span><span>r</span><span>e</span><span>g</span><span>a</span><span>n</span><span>d</span><span>o</span><span>.</span><span>.</span><span>.</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
            `;
            break
        case 'voltar':
            window.location.href = 'franqueado'; // Redirecionar para franqueado.html
            return;
        default:
            content = '<p>Conteúdo não encontrado.</p>';
    }

    document.getElementById('process-content').innerHTML = content;
}

function togglePropostas(type) {
    const content = document.getElementById(`${type}-content`);
    const arrow = document.getElementById(`${type}-arrow`);
    if (content.style.display === "none" || content.style.display === "") {
        content.style.display = "block";
        arrow.classList.add('expanded');
        content.animate([
            { height: '0px', opacity: 0 },
            { height: `${content.scrollHeight}px`, opacity: 1 }
        ], {
            duration: 200,
            fill: 'forwards'
        });
    } else {
        content.animate([
            { height: `${content.scrollHeight}px`, opacity: 1 },
            { height: '0px', opacity: 0 }
        ], {
            duration: 200,
            fill: 'forwards'
        }).onfinish = function () {
            content.style.display = "none";
            arrow.classList.remove('expanded');
        };
    }
}

function loadFormContent() {
    const contentContainer = document.getElementById('process-content');
    const formContent = `
        <div class="container">
            <h2>Abertura de Código na Operadora</h2>
            <form action="/submit_form" method="post" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="operadoras">Selecione as Operadoras:</label>
                    <div id="operadoras" class="operadora-list">
                        <div class="operadora-item">
                            <input type="checkbox" id="amil" name="operadora" value="Amil">
                            <label for="amil">Amil</label>
                        </div>
                        <div class="operadora-item">
                            <input type="checkbox" id="hapvida" name="operadora" value="Hapvida (GNDI)">
                            <label for="hapvida">Hapvida (GNDI)</label>
                        </div>
                        <div class="operadora-item">
                            <input type="checkbox" id="cnu" name="operadora" value="CNU">
                            <label for="cnu">CNU</label>
                        </div>
                        <div class="operadora-item">
                            <input type="checkbox" id="omint" name="operadora" value="Omint">
                            <label for="omint">Omint</label>
                        </div>
                        <div class="operadora-item">
                            <input type="checkbox" id="kipp" name="operadora" value="Kipp">
                            <label for="kipp">Kipp</label>
                        </div>
                        <div class="operadora-item">
                            <input type="checkbox" id="corpore" name="operadora" value="Corpore">
                            <label for="corpore">Corpore</label>
                        </div>
                        <div class="operadora-item">
                            <input type="checkbox" id="sulamerica" name="operadora" value="SulAmerica">
                            <label for="sulamerica">SulAmerica</label>
                        </div>
                        <div class="operadora-item">
                            <input type="checkbox" id="segurosunimed" name="operadora" value="Seguros Unimed">
                            <label for="segurosunimed">Seguros Unimed</label>
                        </div>
                        <div class="operadora-item">
                            <input type="checkbox" id="goldencross" name="operadora" value="Golden Cross">
                            <label for="goldencross">Golden Cross</label>
                        </div>
                    </div>
                </div>
                ${createUploadField('contrato_social', 'Contrato Social e/ou última alteração contratual consolidada')}
                ${createUploadField('nota_fiscal', 'Nota Fiscal')}
                ${createUploadField('cartao_cnpj', 'Cartão de CNPJ')}
                ${createUploadField('folha_cheque', 'Folha de Cheque/Cópia Cartão Banco/Proposta Abertura de Conta')}
                ${createUploadField('susep_pj', 'SUSEP Pessoa Jurídica (CASO TENHA)')}
                ${createUploadField('comprovante_endereco', 'Comprovante de endereço em nome da Empresa/Corretora')}
                ${createUploadField('cpf_rg_socios', 'CPF e RG do(s) sócio(s)')}
                ${createUploadField('comprovante_residencia_socios', 'Comprovante de residência do(s) sócio(s)')}
                ${createUploadField('declaracao_simples', 'Declaração Opção pelo Simples Digitalizado')}
                ${createUploadField('comprovante_cpf', 'Comprovante de situação cadastral no CPF')}
                ${createUploadField('ccm', 'CCM (Cadastro de Contribuinte Mobiliário)')}
                ${createUploadField('pis', 'PIS (Programa de Integração Social)')}
                <button type="submit" class="btn btn-primary">Enviar</button>
            </form>
        </div>
    `;
    contentContainer.innerHTML = formContent;
    addEventListenersToUploadButtons();
}

function createUploadField(id, label) {
    return `
        <div class="form-group">
            <label for="${id}">${label}:</label>
            <input type="file" id="${id}" name="${id}" class="form-control-file" style="display: none;" required>
            <div class="upload-container">
                <button type="button" class="upload-btn" onclick="triggerFileUpload('${id}')">Escolher</button>
                <span id="${id}-status" class="upload-status">Nenhum ficheiro selecionado</span>
            </div>
        </div>
    `;
}

function triggerFileUpload(id) {
    document.getElementById(id).click();
}

function addEventListenersToUploadButtons() {
    const fileInputs = document.querySelectorAll('.form-control-file');
    fileInputs.forEach(input => {
        input.addEventListener('change', function () {
            const statusElement = document.getElementById(`${this.id}-status`);
            if (this.files.length > 0) {
                statusElement.textContent = this.files[0].name;
                statusElement.classList.remove('upload-status-error');
                statusElement.classList.add('upload-status-success');
            } else {
                statusElement.textContent = 'Nenhum ficheiro selecionado';
                statusElement.classList.remove('upload-status-success');
                statusElement.classList.add('upload-status-error');
            }
        });
    });
}

function loadSystemContent(topic) {
    let content = '';
    switch (topic) {
        case 'sistema1':
            content = `
                <section>
			        <iframe id="frame" src="https://app.paineldocorretor.com.br/?hash=EPlRZB"&somente-conteudo=false" frameborder="0" scrolling="yes" style="width: 100%; height: 700px;"></iframe>
		        </section>
            `;
            break;
        case 'sistema2':
            content = `
                <section>
			        <iframe id="frame" src="https://app.paineldocorretor.com.br/produto/?hash=EPlRZB&somente-conteudo=true" frameborder="0" scrolling="yes" style="width: 100%; height: 700px;">1</iframe>
		        </section>
            `;
            break;
        case 'sistema3':
            content = `
                <section>
			        <iframe id="frame" src="https://app.comunicabrazil.com.br/app" frameborder="0" scrolling="no" style="width: 100%; height: 700px;"></iframe>
		        </section>
            `;
            break;
        case 'sistema4':
            content = `
                    <section>
                        <iframe id="frame" src="https://www.universidadedevendasbrh.com.br/plataforma/account/" frameborder="0" scrolling="no" style="width: 100%; height: 700px;"></iframe>
                    </section>
                `;
            break;
        default:
            content = '<h2>Sistemas</h2><p>Selecione um sistema para visualizar o conteúdo.</p>';
    }
    document.getElementById('sistemas-content').innerHTML = content;
}

function loadChamadosContent(topic) {
    let content = '';
    switch (topic) {
        case 'sistema1':
            content = `
                <section>
			        <iframe id="frame" src="/abertura_ticket" style="width: 100%; height: 80vh; border: none"></iframe>
		        </section>
            `;
            break;
        case 'sistema2':
            content = `
                <section>
			        <iframe id="frame" src="/colaborador/chamados" style="width: 100%; height: 80vh; border: none">1</iframe>
		        </section>
            `;
            break;
        default:
            content = '<h2>Chamados</h2><p>Selecione um sistema para visualizar o conteúdo.</p>';
    }
    document.getElementById('chamados-content').innerHTML = content;
}