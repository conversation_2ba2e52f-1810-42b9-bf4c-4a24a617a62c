document.addEventListener('DOMContentLoaded', () => {
    let uploadedFiles = [];
    const loadingScreen = document.getElementById('loadingScreen');
    const descricaoField = document.querySelector('#descricao').parentElement;
    const mainButton = document.getElementById('main-button');
    const mainForm = document.getElementById('main-form');
    const nextForm = document.getElementById('next-form');
    const pfForms = document.getElementById('pf-forms');
    const pmeForms = document.getElementById('pme-forms');
    const pfNextButton = document.getElementById('pf-next-button');
    const pmeNextButton = document.getElementById('pme-next-button');
    const beneficiariosForm = document.getElementById('beneficiarios-form');
    const addBeneficiaryBtn = document.getElementById('add-beneficiary-btn');
    const beneficiariesTable = document.getElementById('beneficiaries-table').querySelector('tbody');
    const dtNasciBenef = document.getElementById('dtNasci_benef');
    const idadeBenef = document.getElementById('idade_benef');
    const telefoneBenef = document.getElementById('telefone_benef');
    const telefonePf = document.getElementById('telefone_pf');
    const telefonePme = document.getElementById('telefone_pme');
    const solicitarEmissaoBtn = document.getElementById('solicitar-emissao-btn');
    const valorCotacaoPf = document.getElementById('valor_cotacao_pf');

    // Função para mostrar ou esconder a tela de carregamento
    function toggleLoadingScreen(show) {
        loadingScreen.style.display = show ? 'flex' : 'none';
    }

    // Atualizar a lista de arquivos anexados
    function updateFileList() {
        const fileListContainer = document.getElementById('file-list');
        fileListContainer.innerHTML = '';

        uploadedFiles.forEach((file, index) => {
            const fileName = file.url.split('/').pop();
            const fileItem = document.createElement('div');
            fileItem.classList.add('file-item', 'mb-2');
            fileItem.innerHTML = `
            <span>${file.title}: ${fileName}</span>
            <button type="button" class="btn btn-danger btn-sm remove-file-btn" data-index="${index}">Remover</button>
        `;
            fileListContainer.appendChild(fileItem);
        });

        document.getElementById('uploaded_files').value = JSON.stringify(uploadedFiles);
    }

    // Verificar condições e ocultar/mostrar o campo descrição
    function checkConditions() {
        const setorSelect = document.getElementById('setor_id');
        const motivoSelect = document.getElementById('tipo-chamado');
        const descriptionField = document.querySelector('#descricao').parentElement; // Container do campo Descrição

        if (setorSelect && motivoSelect) {
            if (setorSelect.value === '1' && motivoSelect.value === 'Emissão') {
                // Oculta o campo descrição
                if (descriptionField) {
                    descriptionField.style.setProperty('display', 'none', 'important');
                    descriptionField.querySelector('textarea').removeAttribute('required');
                }
            } else {
                // Mostra o campo descrição
                if (descriptionField) {
                    descriptionField.style.setProperty('display', 'block', 'important');
                    descriptionField.querySelector('textarea').setAttribute('required', 'true');
                }
            }
        }
    }

    // Evento para verificar condições no setor
    document.getElementById('setor_id').addEventListener('change', checkConditions);

    // Evento para verificar condições no tipo de chamado
    document.getElementById('tipo-chamado').addEventListener('change', checkConditions);

    // Adicionar arquivos
    document.getElementById('add-file-btn').addEventListener('click', () => {
        const fileEntry = document.createElement('div');
        fileEntry.classList.add('file-entry', 'mb-3');

        const fileTitleInput = document.createElement('input');
        fileTitleInput.type = 'text';
        fileTitleInput.placeholder = 'Nome do Arquivo';
        fileTitleInput.classList.add('form-control', 'mb-2');

        const fileInput = document.createElement('div');
        fileInput.classList.add('custom-file');
        fileInput.innerHTML = `
            <input type="file" class="custom-file-input">
            <label class="custom-file-label" for="customFile">Adicione o arquivo</label>
        `;

        const uploadButton = document.createElement('button');
        uploadButton.textContent = 'Enviar Arquivo';
        uploadButton.classList.add('btn', 'btn-success', 'btn-upload', 'mt-2');

        fileEntry.appendChild(fileTitleInput);
        fileEntry.appendChild(fileInput);
        fileEntry.appendChild(uploadButton);

        document.getElementById('file-list').appendChild(fileEntry);

        const fileInputField = fileInput.querySelector('input[type="file"]');
        fileInputField.addEventListener('change', function (e) {
            const fileName = e.target.files[0]?.name || 'Adicione o arquivo';
            fileInput.querySelector('.custom-file-label').textContent = fileName;
        });

        uploadButton.addEventListener('click', async (event) => {
            event.preventDefault();
            const file = fileInputField.files[0];
            const title = fileTitleInput.value;

            if (file && title) {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('title', title);

                try {
                    const response = await fetch('/upload_document', {
                        method: 'POST',
                        body: formData,
                    });
                    const data = await response.json();

                    if (response.ok && data.url) {
                        uploadedFiles.push({ title, url: data.url });
                        updateFileList();
                    } else {
                        alert("Erro ao fazer upload do arquivo.");
                    }
                } catch (error) {
                    console.error("Erro no upload:", error);
                    alert("Erro ao fazer upload do arquivo.");
                }
            } else {
                alert("Preencha o título e selecione um arquivo.");
            }
        });
    });

    // Remover arquivo da lista
    document.getElementById('file-list').addEventListener('click', async (event) => {
        if (event.target.classList.contains('remove-file-btn')) {
            const index = event.target.getAttribute('data-index');
            const fileToRemove = uploadedFiles[index];

            try {
                const response = await fetch('/delete_file', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ url: fileToRemove.url }),
                });

                if (response.ok) {
                    uploadedFiles.splice(index, 1);
                    updateFileList();
                } else {
                    alert("Erro ao remover o arquivo.");
                }
            } catch (error) {
                console.error("Erro ao remover o arquivo:", error);
                alert("Erro ao remover o arquivo.");
            }
        }
    });

    // Mostrar campos condicionais com base no setor selecionado
    document.getElementById('setor_id').addEventListener('change', function () {
        const conditionalFields = document.getElementById('conditional-fields');
        conditionalFields.style.display = this.value === '1' ? 'block' : 'none';
        checkConditions();
    });

    // Alterar texto do botão com base no tipo de chamado e verificar condições
    document.getElementById('tipo-chamado').addEventListener('change', function () {
        mainButton.textContent = this.value === 'Emissão' ? 'Próximo' : 'Abrir Chamado';
        checkConditions();
    });

    // Enviar formulário ou navegar para o próximo
    mainForm.addEventListener('submit', async (event) => {
        event.preventDefault();

        const tipoChamado = document.getElementById('tipo-chamado').value;
        const setorId = document.getElementById('setor_id').value;

        if (setorId !== '1') {
            // Enviar formulário para criar o chamado diretamente
            toggleLoadingScreen(true);

            const formData = new FormData(event.target);
            const data = {
                setor_id: formData.get('setor_id'),
                assunto: formData.get('assunto'),
                description: formData.get('descricao'),
                modalidade: formData.get('modalidade') || null,
                operadora: formData.get('operadora') || null,
                motivo: formData.get('tipo-chamado'),
                uploaded_files: JSON.parse(formData.get('uploaded_files') || '[]'),
            };

            try {
                const response = await fetch('/abertura_ticket', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(`Chamado criado com sucesso! Protocolo: ${result.protocolo}`);
                } else {
                    const error = await response.json();
                    alert(`Erro: ${error.message || 'Falha ao abrir o chamado.'}`);
                }
            } catch (error) {
                console.error("Erro na requisição:", error);
                alert("Erro ao abrir o chamado.");
            } finally {
                toggleLoadingScreen(false);
            }
        } else if (tipoChamado === 'Emissão' && mainButton.textContent === 'Próximo') {
            // Navegar para o próximo formulário
            window.location.href = '/proximo_formulario'; // Alterar para a rota desejada
        } else {
            // Enviar formulário para criar o chamado
            toggleLoadingScreen(true);

            const formData = new FormData(event.target);
            const data = {
                setor_id: formData.get('setor_id'),
                assunto: formData.get('assunto'),
                description: formData.get('descricao'),
                modalidade: formData.get('modalidade') || null,
                operadora: formData.get('operadora') || null,
                motivo: formData.get('tipo-chamado'),
                uploaded_files: JSON.parse(formData.get('uploaded_files') || '[]'),
            };

            try {
                const response = await fetch('/abertura_ticket', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(`Chamado criado com sucesso! Protocolo: ${result.protocolo}`);
                } else {
                    const error = await response.json();
                    alert(`Erro: ${error.message || 'Falha ao abrir o chamado.'}`);
                }
            } catch (error) {
                console.error("Erro na requisição:", error);
                alert("Erro ao abrir o chamado.");
            } finally {
                toggleLoadingScreen(false);
            }
        }
    });

    function validateMainForm() {
        const inputs = mainForm.querySelectorAll('input, select, textarea');
        for (let input of inputs) {
            if (input.hasAttribute('required') && !input.value) {
                return false;
            }
        }
        return true;
    }

    function validateForm(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        for (let input of inputs) {
            if (input.hasAttribute('required') && !input.value) {
                return false;
            }
        }
        return true;
    }

    function calculateAge(dateString) {
        const today = new Date();
        const birthDate = new Date(dateString);
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDifference = today.getMonth() - birthDate.getMonth();
        if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        return age;
    }

    dtNasciBenef.addEventListener('change', () => {
        idadeBenef.value = calculateAge(dtNasciBenef.value);
    });

    // Função para formatar valores monetários
    function formatCurrency(value) {
        value = value.replace(/\D/g, ''); // Remove todos os caracteres não numéricos
        value = (value / 100).toFixed(2) + ""; // Converte para formato monetário
        value = value.replace(".", ","); // Substitui ponto por vírgula
        value = value.replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1."); // Adiciona pontos como separadores de milhar
        return "R$ " + value;
    }

    // Formatar valor da cotação conforme o usuário digita
    valorCotacaoPf.addEventListener('input', (event) => {
        event.target.value = formatCurrency(event.target.value);
    });

    // Função para formatar telefone no padrão BR
    function formatPhoneNumber(value) {
        value = value.replace(/\D/g, ''); // Remove todos os caracteres não numéricos
        if (value.length > 11) value = value.slice(0, 11); // Limita a 11 dígitos

        if (value.length > 10) {
            return value.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
        } else if (value.length > 5) {
            return value.replace(/(\d{2})(\d{4})(\d{0,4})/, '($1) $2-$3');
        } else if (value.length > 2) {
            return value.replace(/(\d{2})(\d{0,5})/, '($1) $2');
        } else {
            return value.replace(/(\d*)/, '($1');
        }
    }

    // Bloquear a opção de colar nos campos de telefone
    telefoneBenef.addEventListener('paste', (event) => {
        event.preventDefault();
    });
    telefonePf.addEventListener('paste', (event) => {
        event.preventDefault();
    });
    telefonePme.addEventListener('paste', (event) => {
        event.preventDefault();
    });

    // Formatar telefone conforme o usuário digita
    telefoneBenef.addEventListener('input', (event) => {
        event.target.value = formatPhoneNumber(event.target.value);
    });
    telefonePf.addEventListener('input', (event) => {
        event.target.value = formatPhoneNumber(event.target.value);
    });
    telefonePme.addEventListener('input', (event) => {
        event.target.value = formatPhoneNumber(event.target.value);
    });

    function highlightEmptyFields(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (input.hasAttribute('required') && !input.value) {
                input.classList.add('highlight');
            } else {
                input.classList.remove('highlight');
            }
        });
    }

    mainButton.addEventListener('click', async (event) => {
        event.preventDefault(); // Prevenir o comportamento padrão do botão

        if (!validateMainForm()) {
            alert('Por favor, preencha todos os campos obrigatórios.');
            highlightEmptyFields(mainForm);
            return;
        }

        const setorId = document.getElementById('setor_id').value;

        if (setorId !== '1') {
            // Enviar formulário para criar o chamado diretamente
            toggleLoadingScreen(true);

            const formData = new FormData(mainForm);
            const data = {
                setor_id: formData.get('setor_id'),
                assunto: formData.get('assunto'),
                description: formData.get('descricao'),
                modalidade: formData.get('modalidade') || null,
                operadora: formData.get('operadora') || null,
                motivo: formData.get('tipo-chamado'),
                uploaded_files: JSON.parse(formData.get('uploaded_files') || '[]'),
            };

            try {
                const response = await fetch('/abertura_ticket', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(`Chamado criado com sucesso! Protocolo: ${result.protocolo}`);
                    window.location.reload(); // Atualiza a página ao apertar "OK"
                } else {
                    const error = await response.json();
                    alert(`Erro: ${error.message || 'Falha ao abrir o chamado.'}`);
                }
            } catch (error) {
                console.error("Erro na requisição:", error);
                alert("Erro ao abrir o chamado.");
            } finally {
                toggleLoadingScreen(false);
            }
        } else {
            // Lógica para exibir a próxima div
            nextForm.style.display = 'block';
            mainForm.style.display = 'none';

            const modalidade = document.getElementById('modalidade').value;
            if (modalidade === 'Adesão' || modalidade === 'PF' || modalidade === 'PF - Odontológico') {
                pfForms.style.display = 'block';
                pmeForms.style.display = 'none';
            } else if (modalidade === 'PME' || modalidade === 'PME - Odontológico') {
                pfForms.style.display = 'none';
                pmeForms.style.display = 'block';
            }
        }
    });

    pfNextButton.addEventListener('click', (event) => {
        event.preventDefault(); // Prevenir o comportamento padrão do botão

        if (!validateForm(pfForms)) {
            alert('Por favor, preencha todos os campos obrigatórios.');
            highlightEmptyFields(pfForms);
            return;
        }

        // Lógica para exibir o formulário de beneficiários
        beneficiariosForm.style.display = 'block';
        pfForms.style.display = 'none';
    });

    pmeNextButton.addEventListener('click', (event) => {
        event.preventDefault(); // Prevenir o comportamento padrão do botão

        if (!validateForm(pmeForms)) {
            alert('Por favor, preencha todos os campos obrigatórios.');
            highlightEmptyFields(pmeForms);
            return;
        }

        // Lógica para exibir o formulário de beneficiários
        beneficiariosForm.style.display = 'block';
        pmeForms.style.display = 'none';
    });

    function editBeneficiary(row) {
        const cells = row.querySelectorAll('td');
        document.getElementById('tipo_benef_pme').value = cells[0].textContent;
        document.getElementById('nome_benef').value = cells[1].textContent;
        document.getElementById('plano_benef').value = cells[2].textContent;
        document.getElementById('acomod_benef').value = cells[3].textContent;
        document.getElementById('faixa_benef').value = cells[4].textContent;
        document.getElementById('grau_benef').value = cells[5].textContent;
        document.getElementById('dtNasci_benef').value = cells[6].textContent;
        document.getElementById('idade_benef').value = cells[7].textContent;
        document.getElementById('telefone_benef').value = cells[8].textContent;
        document.getElementById('email_benef').value = cells[9].textContent;
        row.remove();
    }

    addBeneficiaryBtn.addEventListener('click', () => {
        const benefForm = document.getElementById('benef-form');
        if (!validateForm(benefForm)) {
            alert('Por favor, preencha todos os campos obrigatórios.');
            highlightEmptyFields(benefForm);
            return;
        }

        const tipoBenef = document.getElementById('tipo_benef_pme').value;
        const nomeBenef = document.getElementById('nome_benef').value;
        const planoBenef = document.getElementById('plano_benef').value;
        const acomodBenef = document.getElementById('acomod_benef').value;
        const faixaBenef = document.getElementById('faixa_benef').value;
        const grauBenef = document.getElementById('grau_benef').value;
        const civilBenef = document.getElementById('civil_benef').value;
        const dtNasciBenef = document.getElementById('dtNasci_benef').value;
        const idadeBenef = calculateAge(dtNasciBenef);
        const telefoneBenef = document.getElementById('telefone_benef').value;
        const emailBenef = document.getElementById('email_benef').value;

        const newRow = document.createElement('tr');
        newRow.innerHTML = `
            <td>${tipoBenef}</td>
            <td>${nomeBenef}</td>
            <td>${planoBenef}</td>
            <td>${acomodBenef}</td>
            <td>${faixaBenef}</td>
            <td>${grauBenef}</td>
            <td>${dtNasciBenef}</td>
            <td>${idadeBenef}</td>
            <td>${telefoneBenef}</td>
            <td>${emailBenef}</td>
            <td>${civilBenef}</td>
            <td><i class="fa-regular fa-pen-to-square edit-beneficiary" style="cursor: pointer;"></i></td>
        `;
        beneficiariesTable.appendChild(newRow);

        // Limpar os campos do formulário de beneficiários
        benefForm.querySelectorAll('input, select, textarea').forEach(input => input.value = '');
        document.getElementById('idade_benef').value = '';
    });

    beneficiariesTable.addEventListener('click', (event) => {
        if (event.target.classList.contains('edit-beneficiary')) {
            const row = event.target.closest('tr');
            editBeneficiary(row);
        }
    });

    solicitarEmissaoBtn.addEventListener('click', async () => {
        const setorId = document.getElementById('setor_id').value;
        const assunto = document.getElementById('assunto').value;
        const modalidade = document.getElementById('modalidade').value;
        const operadora = document.getElementById('operadora').value;
        const tipoChamado = document.getElementById('tipo-chamado').value;

        // Campos adicionais
        const multinotasPf = document.getElementById('multinotas-pf').value;
        const multinotasPme = document.getElementById('multinotas-pme').value;
        const login_multinotas = document.getElementById('login_multinotas_pme').value || document.getElementById('login_multinotas_pf').value;
        const senha_multinotas = document.getElementById('senha_multinotas_pme').value || document.getElementById('senha_multinotas_pf').value;
        const cod_corretor = document.getElementById('codigo_corretor_pme').value || document.getElementById('codigo_corretor_pf').value;
        const nomeEmpresa = document.getElementById('razao_social_pme').value;
        const cnpj = document.getElementById('cnpj_pme').value;
        const vigencia = document.getElementById('vigencia_pme').value || document.getElementById('vigencia_pf').value;
        const vencimento = document.getElementById('vencimento_pme').value;
        const coparticipacao = document.getElementById('coparticipacao_pme').value || document.getElementById('coparticipacao_pf').value;
        const tipo_copart = document.getElementById('tipo_copart_pme').value || document.getElementById('tipo_copart_pf').value;
        const compulsorio = document.getElementById('plano_compulsorio_pme').value;
        const aprov_carencia = document.getElementById('aproveitamento_pme').value || document.getElementById('aproveitamento_pf').value;
        const odonto = document.getElementById('odontologico_pme').value || document.getElementById('odontologico_pf').value;
        const tabelaRegiao = document.getElementById('tabela_regiao_pme').value;
        const administradora = document.getElementById('admin_pf').value;
        const entidade = document.getElementById('entidade_pf').value;
        const contatoResponsavel = document.getElementById('contato_pme').value || document.getElementById('contato_pf').value;
        const email = document.getElementById('email_pme').value || document.getElementById('email_pf').value;
        const telefone = document.getElementById('telefone_pme').value || document.getElementById('telefone_pf').value;
        const valorCotacao = document.getElementById('valor_cotacao_pme').value || document.getElementById('valor_cotacao_pf').value;
        const infoComplementares = document.getElementById('info_complementares_pme').value || document.getElementById('info_complementares_pf').value;

        const beneficiaries = [];
        const rows = beneficiariesTable.querySelectorAll('tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            beneficiaries.push({
                tipo_beneficiario: cells[0].textContent,
                nome: cells[1].textContent,
                plano: cells[2].textContent,
                acomodacao: cells[3].textContent,
                faixa_etaria: cells[4].textContent,
                grau_parentesco: cells[5].textContent,
                dt_nasci: cells[6].textContent,
                idade: cells[7].textContent,
                telefone: cells[8].textContent,
                email: cells[9].textContent,
                civil: cells[10].textContent
            });
        });

        console.log('Payload enviado para o backend:', JSON.stringify({
            setor_id: setorId,
            assunto: assunto,
            modalidade: modalidade,
            operadora: operadora,
            motivo: tipoChamado,
            multinotas_pf: multinotasPf,
            multinotas_pme: multinotasPme,
            login_multinotas: login_multinotas,
            senha_multinotas: senha_multinotas,
            codigo_corretor: cod_corretor,
            nome_empresa: nomeEmpresa,
            cnpj: cnpj,
            vigencia: vigencia,
            vencimento: vencimento,
            copart: coparticipacao,
            tipo_copart: tipo_copart,
            compulsorio: compulsorio,
            odonto: odonto,
            aprov_carencia: aprov_carencia,
            tabela_regiao: tabelaRegiao,
            contato_responsavel: contatoResponsavel,
            email: email,
            telefone: telefone,
            valor_cotacao: valorCotacao,
            info_complementares: infoComplementares,
            beneficiarios: beneficiaries,
        }));

        try {
            toggleLoadingScreen(true); // Mostrar a tela de carregamento

            const response = await fetch('/emissao_ticket', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    setor_id: setorId,
                    assunto: assunto,
                    modalidade: modalidade,
                    operadora: operadora,
                    motivo: tipoChamado,
                    multinotas_pf: multinotasPf,
                    multinotas_pme: multinotasPme,
                    login_multinotas: login_multinotas,
                    senha_multinotas: senha_multinotas,
                    cod_corretor: cod_corretor,
                    nome_empresa: nomeEmpresa,
                    cnpj: cnpj,
                    vigencia: vigencia,
                    vencimento: vencimento,
                    copart: coparticipacao,
                    tipo_copart: tipo_copart,
                    compulsorio: compulsorio,
                    odonto: odonto,
                    aprov_carencia: aprov_carencia,
                    tabela_regiao: tabelaRegiao,
                    administradora: administradora,
                    entidade: entidade,
                    contato_responsavel: contatoResponsavel,
                    email: email,
                    telefone: telefone,
                    valor_cotacao: valorCotacao,
                    info_complementares: infoComplementares,
                    beneficiarios: beneficiaries,
                    uploaded_files: uploadedFiles
                })
            });

            if (response.ok) {
                const result = await response.json();
                alert(`Emissão solicitada com sucesso! Protocolo: ${result.protocolo}`);
                window.location.reload(); // Atualiza a página ao apertar "OK"
            } else {
                const error = await response.json();
                alert(`Erro: ${error.message || 'Falha ao solicitar emissão.'}`);
            }
        } catch (error) {
            console.error("Erro na requisição:", error);
            alert("Erro ao solicitar emissão.");
        } finally {
            toggleLoadingScreen(false); // Esconder a tela de carregamento
        }
    });
});
