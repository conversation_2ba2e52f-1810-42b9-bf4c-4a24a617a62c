$(document).ready(function () {
    console.log("Initializing DataTable...");

    // Função para atualizar a legenda de última atualização
    function updateLastExecutionTime() {
        $.ajax({
            url: "/api/last-execution",
            method: "GET",
            success: function (response) {
                if (response.status === 'success' && response.data) {
                    $('#last-update').html(`Última atualização: ${response.data.data}`);
                }
            },
            error: function (xhr, status, error) {
                console.log("Error fetching last execution time: ", error);
            }
        });
    }

    // Atualiza a legenda quando a página carrega
    updateLastExecutionTime();

    // Função para carregar as opções de mês/ano do servidor
    function loadMonthYearOptions() {
        $.ajax({
            url: "/api/meses_anos_operadora",
            method: "GET",
            success: function (data) {
                console.log("Month/Year options loaded: ", data);

                // Ordenar as datas em ordem decrescente (mais recente primeiro)
                data.sort((a, b) => {
                    const [monthA, yearA] = a.split('/');
                    const [monthB, yearB] = b.split('/');

                    if (yearA !== yearB) {
                        return yearB - yearA;
                    }
                    return monthB - monthA;
                });

                $('#monthYearPicker').empty();
                data.forEach(function (monthYear) {
                    $('#monthYearPicker').append($('<option>', {
                        value: monthYear,
                        text: monthYear
                    }));
                });
                $('#monthYearPicker').multiselect('rebuild');

                // Selecionar o mês mais atual
                var latestMonthYear = data[0]; // Agora temos certeza que este é o mais recente
                $('#monthYearPicker').multiselect('select', latestMonthYear);

                $('#rankingTable').DataTable().ajax.reload(); // Recarregar a tabela com o mês/ano selecionado
            },
            error: function (xhr, status, error) {
                console.log("Error fetching month/year options: ", xhr.responseText);
            }
        });
    }

    // Função para obter o mês e ano atual
    function getCurrentMonthYear() {
        var date = new Date();
        var month = (date.getMonth() + 1).toString().padStart(2, '0');
        var year = date.getFullYear();
        return month + '/' + year;
    }

    // Inicializar o multiselect para escolher mês/ano
    $('#monthYearPicker').multiselect({
        enableFiltering: true,
        includeSelectAllOption: true,
        buttonWidth: '250px',
        nonSelectedText: 'Selecione Mês/Ano',
        allSelectedText: 'Todos os Meses/Anos',
        onChange: function (option, checked) {
            console.log("Month/Year changed: ", $('#monthYearPicker').val());
            $('#rankingTable').DataTable().ajax.reload(); // Recarregar a tabela quando a seleção mudar
        }
    });

    // Carregar as opções de mês/ano na inicialização
    loadMonthYearOptions();

    // Função para converter valores monetários de string para float e dividir por 100
    function parseCurrency(value) {
        return parseFloat(value.replace(/[^0-9,-]+/g, "").replace(",", ".")) / 100;
    }

    // Inicializar o DataTable para exibir os dados de ranking
    $('#rankingTable').DataTable({
        "ajax": {
            "url": "/api/ranking_operadora",
            "dataSrc": function (json) {
                let data = {};
                json.forEach(function (item) {
                    if (!data[item.operadora]) {
                        data[item.operadora] = {
                            operadora: item.operadora,
                            vltotal: 0,
                            vlcontrato: 0,
                            vlvida: 0,
                            vidacount: 0,
                            corretorcount: 0,
                            contratocount: 0
                        };
                    }
                    data[item.operadora].vltotal += parseCurrency(item.vltotal) || 0;
                    data[item.operadora].vlcontrato += parseCurrency(item.vlcontrato) || 0;
                    data[item.operadora].vlvida += parseCurrency(item.vlvida) || 0;
                    data[item.operadora].vidacount += parseInt(item.vidacount) || 0;
                    data[item.operadora].corretorcount += parseInt(item.corretorcount) || 0;
                    data[item.operadora].contratocount += parseInt(item.contratocount) || 0;
                });
                return Object.values(data); // Retorna os dados agrupados por operadora
            },
            "data": function (d) {
                let mesAno = $('#monthYearPicker').val();
                console.log("Fetching data for: ", mesAno);
                if (mesAno) {
                    d.mesAno = mesAno.join(',');
                } else {
                    d.mesAno = getCurrentMonthYear();
                }
            },
            "error": function (xhr, error, thrown) {
                console.log("Error fetching data: ", xhr.responseText);
            }
        },
        "columns": [
            { "data": "operadora", "width": "700px" },
            { "data": "vltotal", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ '), "width": "350px" },
            { "data": "vidacount", render: $.fn.dataTable.render.number('.', ',', 0), "width": "100px" },
            { "data": "corretorcount", render: $.fn.dataTable.render.number('.', ',', 0), "width": "100px" },
            { "data": "contratocount", render: $.fn.dataTable.render.number('.', ',', 0), "width": "100px" },
            { "data": "vlcontrato", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ '), "width": "250px" },
            { "data": "vlvida", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ '), "width": "150px" }
        ],
        "order": [[1, 'desc']],  // Ordena pelo vltotal em ordem decrescente
        "dom": 'Bfrtip',
        "buttons": ['copy', 'csv', 'excel', 'pdf', 'print'],
        "paging": false,
        "searching": false,
        "info": false,
        "footerCallback": function (row, data, start, end, display) {
            var api = this.api(), data;

            var intVal = function (i) {
                return typeof i === 'string' ?
                    parseFloat(i.replace(/[\$,R$]/g, '').replace(',', '.')) / 100 :
                    typeof i === 'number' ?
                        i : 0;
            };

            // Somar as colunas do total, vida, corretores, contratos, média de contratos e média de vidas
            var totalTotal = api
                .column(1, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalVidas = api
                .column(2, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalCorretores = api
                .column(3, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalContratos = api
                .column(4, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalMediaContrato = api
                .column(5, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalMediaVida = api
                .column(6, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            // Atualizar os rodapés das colunas com os totais calculados
            $(api.column(1).footer()).html('R$ ' + totalTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            $(api.column(2).footer()).html(totalVidas.toLocaleString('pt-BR'));
            $(api.column(3).footer()).html(totalCorretores.toLocaleString('pt-BR'));
            $(api.column(4).footer()).html(totalContratos.toLocaleString('pt-BR'));
            $(api.column(5).footer()).html('R$ ' + totalMediaContrato.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            $(api.column(6).footer()).html('R$ ' + totalMediaVida.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
        }
    }).buttons().container().appendTo('#buttons-container');

    console.log("DataTable initialized.");
});
