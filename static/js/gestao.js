document.addEventListener('DOMContentLoaded', function () {
    function openModal(content) {
        var modal = document.getElementById("modal");
        var modalBody = document.getElementById("modal-body");

        if (content === 'acessos') {
            modalBody.innerHTML = `
            <div class="cardboard" onclick="loadForm('portal')">
                <div class="cardboard-header">
                    <i class="fa-solid fa-desktop"></i>
                    <h3>Cadastrar Portal</h3>
                </div>
                <div class="cardboard-content">
                    <p>Cadastre um novo portal para acesso.</p>
                </div>
                <div class="cardboard-footer">
                    <button class="gestao-btn-acessar">Cadastrar</button>
                </div>
            </div>
            <div class="cardboard" onclick="loadForm('acesso')">
                <div class="cardboard-header">
                    <i class="fas fa-key"></i>
                    <h3>Cadastrar Acesso</h3>
                </div>
                <div class="cardboard-content">
                    <p>Cadastre novos acessos para os portais.</p>
                </div>
                <div class="cardboard-footer">
                    <button class="gestao-btn-acessar">Cadastrar</button>
                </div>
            </div>
            <div class="cardboard">
                <div class="cardboard-header">
                    <i class="fas fa-key"></i>
                    <h3>Login e Senhas</h3>
                </div>
                <div class="cardboard-content">
                    <p>Acesse seus logins e senhas.</p>
                </div>
                <div class="cardboard-footer">
                <button class="gestao-btn-acessar" onclick="window.location.href='/acessos'">Acessar</button>
                </div>
            </div>
            `;
        } else if (content === 'tabela-comissoes') {
            // Verificar se o usuário tem userType 1 ou 7, ou se tem permissão para comissões
            if (permissions.userType === 1 || permissions.userType === 7 || permissions.hasPermissionComissao) {
                modalBody.innerHTML = `
                    <div class="cardboard" id="tabelas">
                        <div class="cardboard-header">
                            <i class="fa-solid fa-file-arrow-up"></i>
                            <h3>Gerencie as Tabelas de Comissão</h3>
                        </div>
                        <div class="cardboard-content">
                            <p>Faça o upload ou update da tabela de comissão.</p>
                        </div>
                        <div class="cardboard-footer">
                            <button class="gestao-btn-acessar" onclick="window.location.href='/upload-tabela-comissoes'">Acessar</button>
                        </div>
                    </div>

                    <div class="cardboard" id="tabelas">
                        <div class="cardboard-header">
                            <i class="fa-solid fa-file-invoice-dollar"></i>
                            <h3>Tabela de Comissão - Gerencial</h3>
                        </div>
                        <div class="cardboard-content">
                            <p>Acesse a tabela de comissões com detalhes completos dos totais e grades.</p>
                        </div>
                        <div class="cardboard-footer">
                            <button class="gestao-btn-acessar" onclick="window.location.href='/tabela-de-comissoes'">Acessar</button>
                        </div>
                    </div>

                    <div class="cardboard" id="tabelas">
                        <div class="cardboard-header">
                            <i class="fa-solid fa-file-invoice-dollar"></i>
                            <h3>Tabela de Comissão</h3>
                        </div>
                        <div class="cardboard-content">
                            <p>Acesse a tabela de comissão.</p>
                        </div>
                        <div class="cardboard-footer">
                            <button class="gestao-btn-acessar" onclick="window.location.href='/tabelas-comissoes/grade'">Acessar</button>
                        </div>
                    </div>
                `;
            } else {
                modalBody.innerHTML = `
                    <div class="cardboard" id="tabelas">
                        <div class="cardboard-header">
                            <i class="fa-solid fa-file-invoice-dollar"></i>
                            <h3>Tabela de Comissão</h3>
                        </div>
                        <div class="cardboard-content">
                            <p>Acesse a tabela de comissão.</p>
                        </div>
                        <div class="cardboard-footer">
                            <button class="gestao-btn-acessar" onclick="window.location.href='/tabelas-comissoes/grade'">Acessar</button>
                        </div>
                    </div>
                `;
            }
        }

        modal.style.display = "block";
    }

    function closeModal() {
        var modal = document.getElementById("modal");
        modal.style.display = "none";
    }

    function loadForm(formType) {
        var modalBody = document.getElementById("modal-body");

        if (formType === 'acesso') {
            fetch('/add_acessos', {
                method: 'GET'
            })
                .then(response => response.json())
                .then(data => {
                    // Ordena os portais pelo nome (índice 1 do array) em ordem alfabética
                    var sortedPortals = data.portais.sort((a, b) => {
                        return a[1].localeCompare(b[1]);
                    });

                    // Cria as opções do select com os portais ordenados
                    var portalOptions = sortedPortals.map((portal, index) =>
                        `<option value="${portal[0]}" data-index="${index}">${portal[1]}</option>`
                    ).join('');

                    modalBody.innerHTML = `
                    <div class="modal-portal">
                        <h3>Cadastrar Acesso</h3>
                        <form id="form-acesso">
                            <div class="input-portal">
                                <label for="portalId">Portal:</label>
                                <select id="portalId" name="portal_id" required>${portalOptions}</select>
                            </div>
                            <!-- Input para exibir o link do portal selecionado (readonly) -->
                            <div class="input-portal">
                                <label for="portalLink">Link do Portal:</label>
                                <div id="portalLinkDisplay" class="portal-link"></div>
                            </div>
                            <div class="input-portal">
                                <label for="login">Login:</label>
                                <input type="text" id="login" name="login" required>
                            </div>
                            <div class="input-portal">
                                <label for="senha">Senha:</label>
                                <input type="text" id="senha" name="senha" required>
                            </div>
                            <div class="input-portal">
                                <label for="unidade">Unidade ou Empresa:</label>
                                <input type="text" id="unidade" name="unidade">
                            </div>
                            <div class="input-portal">
                                <label for="modalidade">Modalidade:</label>
                                <select id="modalidade" name="modalidade" required>
                                    <option value=""></option>
                                    <option value="auto">Auto</option>
                                    <option value="odonto">Odonto</option>
                                    <option value="re">R.E.</option>
                                    <option value="saude">Saúde</option>
                                    <option value="vida">Vida</option>
                                </select>
                            </div>
                            <div class="input-portal">
                                <label for="codigoAcesso">Código de Acesso:</label>
                                <input type="text" id="codigoAcesso" name="codigo_acesso">
                            </div>
                            <div class="input-portal">
                                <label for="outros">Outros:</label>
                                <input type="text" id="outros" name="outros">
                            </div>
                            <button class="gestao-btn-acessar" type="button" onclick="submitForm('acesso')">Cadastrar</button>
                        </form>
                    </div>
                `;

                    // Adiciona o evento change no select para exibir o link do portal selecionado
                    var portalSelect = document.getElementById("portalId");
                    var portalLinkDisplay = document.getElementById("portalLinkDisplay");

                    portalSelect.addEventListener('change', function () {
                        var selectedIndex = portalSelect.selectedIndex;
                        var selectedPortal = sortedPortals[selectedIndex];
                        var portalLink = selectedPortal[2]; // Índice 2 contém o link do portal
                        portalLinkDisplay.innerHTML = `<a href="${portalLink}" target="_blank">${portalLink}</a>`; // Exibe o link como hiperlink
                    });

                    // Exibe o link do primeiro portal na inicialização (se houver)
                    if (sortedPortals.length > 0) {
                        var firstPortalLink = sortedPortals[0][2];
                        portalLinkDisplay.innerHTML = `<a href="${firstPortalLink}" target="_blank">${firstPortalLink}</a>`; // Define o valor inicial
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Erro ao carregar portais.');
                });
        } else if (formType === 'portal') {
            modalBody.innerHTML = `
                <div class="modal-portal">
                    <h3>Cadastrar Portal</h3>
                    <form id="form-portal">
                        <div class="input-portal">
                            <label for="portalNome">Nome do Portal:</label>
                            <input type="text" id="portalNome" name="nome" required>
                        </div>
                        <div class="input-portal">
                            <label for="portalLink">Link do Portal:</label>
                            <input type="text" id="portalLink" name="link" required>
                        </div>
                        <button class="gestao-btn-acessar" type="button" onclick="submitForm('portal')">Cadastrar</button>
                    </form>
                </div>
            `;
        }
    }

    function submitForm(formType) {
        var form;
        if (formType === 'portal') {
            form = document.getElementById("form-portal");
        } else if (formType === 'acesso') {
            form = document.getElementById("form-acesso");
        }

        showLoadingScreen(); // Mostra a tela de carregamento

        var formData = new FormData(form);
        var jsonData = {};
        formData.forEach((value, key) => {
            jsonData[key] = value || null;  // Atribui null se o valor não estiver preenchido
        });

        fetch(formType === 'portal' ? '/add_portal' : '/add_acessos', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(jsonData)
        })
            .then(response => response.json())
            .then(data => {
                hideLoadingScreen(); // Esconde a tela de carregamento
                alert(data.message || data.error);
                if (data.message) {
                    closeModal();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                hideLoadingScreen(); // Esconde a tela de carregamento
                alert('Erro ao cadastrar.');
            });
    }

    function showLoadingScreen() {
        var loadingScreen = document.getElementById("loadingScreen");
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
    }

    function hideLoadingScreen() {
        var loadingScreen = document.getElementById("loadingScreen");
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }

    window.openModal = openModal;
    window.closeModal = closeModal;
    window.loadForm = loadForm;
    window.submitForm = submitForm;
});
