$(document).ready(function () {
    console.log("Initializing DataTable...");

    // Função para atualizar a legenda de última atualização
    function updateLastExecutionTime() {
        $.ajax({
            url: "/api/last-execution",
            method: "GET",
            success: function (response) {
                if (response.status === 'success' && response.data) {
                    $('#last-update').html(`Última atualização: ${response.data.data}`);
                }
            },
            error: function (xhr, status, error) {
                console.log("Error fetching last execution time: ", error);
            }
        });
    }

    // Atualiza a legenda quando a página carrega
    updateLastExecutionTime();

    function loadMonthYearOptions() {
        $.ajax({
            url: "/api/meses_anos_unidades",
            method: "GET",
            success: function (data) {
                console.log("Month/Year options loaded: ", data);

                // Ordena o array de meses/anos em ordem decrescente
                data.sort(function (a, b) {
                    var aParts = a.split('/');
                    var bParts = b.split('/');
                    var aDate = new Date(aParts[1], aParts[0] - 1);
                    var bDate = new Date(bParts[1], bParts[0] - 1);
                    return bDate - aDate;
                });

                $('#monthYearPicker').empty();
                data.forEach(function (monthYear) {
                    $('#monthYearPicker').append($('<option>', {
                        value: monthYear,
                        text: monthYear
                    }));
                });
                $('#monthYearPicker').multiselect('rebuild');

                // Selecionar o mês mais atual
                var latestMonthYear = data[0]; // Assume que a lista está ordenada do mais recente para o mais antigo
                $('#monthYearPicker').multiselect('select', latestMonthYear);

                $('#rankingTable').DataTable().ajax.reload(); // Recarregar a tabela com o mês/ano selecionado
            },
            error: function (xhr, status, error) {
                console.log("Error fetching month/year options: ", xhr.responseText);
            }
        });
    }

    function getCurrentMonthYear() {
        var date = new Date();
        var month = (date.getMonth() + 1).toString().padStart(2, '0');
        var year = date.getFullYear();
        return month + '/' + year;
    }

    $('#monthYearPicker').multiselect({
        enableFiltering: true,
        includeSelectAllOption: true,
        buttonWidth: '250px',
        nonSelectedText: 'Selecione Mês/Ano',
        allSelectedText: 'Todos os Meses/Anos',
        onChange: function (option, checked) {
            console.log("Month/Year changed: ", $('#monthYearPicker').val());
            $('#rankingTable').DataTable().ajax.reload();
        }
    });

    loadMonthYearOptions();

    function parseCurrency(value) {
        if (typeof value !== 'string') {
            return 0;
        }
        return parseFloat(value.replace(/[^0-9,-]+/g, "").replace(",", ".")) || 0;
    }

    function parsePercent(value) {
        return parseFloat(value) * 100 || 0;
    }

    $('#rankingTable').DataTable({
        "ajax": {
            "url": "/api/ranking_unidades",
            "dataSrc": function (json) {
                let data = {};
                json.forEach(function (item) {
                    if (!data[item.assistente_grupo]) {
                        data[item.assistente_grupo] = {
                            assistente_grupo: item.assistente_grupo,
                            vltotal: 0,
                            vlcontrato: 0,
                            vlvida: 0,
                            vidacount: 0,
                            corretorcount: 0,
                            contratocount: 0
                        };
                    }
                    data[item.assistente_grupo].vltotal += parseFloat(item.total_vltotal) || 0;
                    data[item.assistente_grupo].vlcontrato += parseFloat(item.total_vlcontrato) || 0;
                    data[item.assistente_grupo].vlvida += parseFloat(item.total_vlvida) || 0;
                    data[item.assistente_grupo].vidacount += parseInt(item.total_vidacount) || 0;
                    data[item.assistente_grupo].corretorcount += parseInt(item.total_corretorcount) || 0;
                    data[item.assistente_grupo].contratocount += parseInt(item.total_contratocount) || 0;
                });
                return Object.values(data);
            },
            "data": function (d) {
                let mesAno = $('#monthYearPicker').val();
                console.log("Fetching data for: ", mesAno);
                if (mesAno) {
                    d.mesAno = mesAno.map(val => `'${val}'`).join(',');
                } else {
                    d.mesAno = `'${getCurrentMonthYear()}'`;
                }
            },
            "error": function (xhr, error, thrown) {
                console.log("Error fetching data: ", xhr.responseText);
            }
        },
        "columns": [
            { "data": "assistente_grupo", "width": "700px" },
            { "data": "vltotal", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ '), "width": "350px" },
            { "data": "vidacount", "width": "100px" },
            { "data": "corretorcount", "width": "100px" },
            { "data": "contratocount", "width": "100px" },
            { "data": "vlcontrato", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ '), "width": "200px" },
            { "data": "vlvida", render: $.fn.dataTable.render.number('.', ',', 2, 'R$ '), "width": "150px" }
        ],
        "order": [[1, 'desc']],
        "dom": 'Bfrtip',
        "buttons": ['copy', 'csv', 'excel', 'pdf', 'print'],
        "paging": false,
        "searching": false,
        "info": false,
        "footerCallback": function (row, data, start, end, display) {
            var api = this.api(), data;

            var intVal = function (i) {
                return typeof i === 'string' ?
                    parseFloat(i.replace(/[\$,R$]/g, '').replace(',', '.')) || 0 :
                    typeof i === 'number' ?
                        i : 0;
            };

            var totalTotal = api
                .column(1, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalVidas = api
                .column(2, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalCorretores = api
                .column(3, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalContratos = api
                .column(4, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalMediaContrato = api
                .column(5, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            var totalMediaVida = api
                .column(6, { page: 'current' })
                .data()
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            $(api.column(1).footer()).html('R$ ' + totalTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            $(api.column(2).footer()).html(totalVidas.toLocaleString('pt-BR'));
            $(api.column(3).footer()).html(totalCorretores.toLocaleString('pt-BR'));
            $(api.column(4).footer()).html(totalContratos.toLocaleString('pt-BR'));
            $(api.column(5).footer()).html('R$ ' + totalMediaContrato.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            $(api.column(6).footer()).html('R$ ' + totalMediaVida.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
        }
    }).buttons().container().appendTo('#buttons-container');

    console.log("DataTable initialized.");
});
