document.addEventListener('DOMContentLoaded', function () {
    // Seleciona o formulário de registro e os campos relevantes
    const formRegister = document.getElementById('formRegister');
    const cargoSelect = document.getElementById('cargo');
    const equipeContainer = document.getElementById('equipe-container');
    const senhaInput = document.getElementById('senha');
    const confirmSenhaInput = document.getElementById('confirm_senha');
    const loadingScreen = document.getElementById('loadingScreen');

    // Função para mostrar ou ocultar o campo "Equipe" com base na seleção do campo "Cargo"
    if (cargoSelect) {
        cargoSelect.addEventListener('change', function () {
            const selectedValue = cargoSelect.value;
            // Exibe o campo "Equipe" se o cargo for Superintendente ou Supervisor
            if (selectedValue === '3' || selectedValue === '4') {
                equipeContainer.style.display = 'block';
            } else {
                // Oculta o campo "Equipe" para outros cargos
                equipeContainer.style.display = 'none';
            }
        });

        // Inicializa o estado do campo "Equipe" com base no valor inicial do campo "Cargo"
        const selectedValue = cargoSelect.value;
        if (selectedValue === '3' || selectedValue === '4') {
            equipeContainer.style.display = 'block';
        } else {
            equipeContainer.style.display = 'none';
        }
    }

    // Adiciona um event listener para o formulário de registro
    if (formRegister) {
        formRegister.addEventListener('submit', handleRegisterFormSubmit);
    }

    // Função para lidar com o envio do formulário de registro
    function handleRegisterFormSubmit(event) {
        event.preventDefault();  // Previne o comportamento padrão de submissão do formulário

        showLoadingScreen();  // Mostra a tela de carregamento

        const formData = new FormData(formRegister);  // Coleta os dados do formulário

        // Envia uma requisição POST para o endpoint de registro
        fetch('/register', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())  // Converte a resposta para JSON
        .then(data => {
            hideLoadingScreen();  // Esconde a tela de carregamento
            if (data.error) {
                alert(data.error);  // Exibe um alerta com a mensagem de erro
            } else {
                alert("Registro efetuado com sucesso! Por favor, confira seu e-mail para ativar sua conta.");
                window.location.href = '/confirm_email';  // Redireciona para a página de confirmação de e-mail
            }
        })
        .catch(error => {
            console.error('Erro no registro:', error);
            hideLoadingScreen();  // Esconde a tela de carregamento
            alert('Erro no registro.');
        });
    }

    // Função para mostrar a tela de carregamento
    function showLoadingScreen() {
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
    }

    // Função para esconder a tela de carregamento
    function hideLoadingScreen() {
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }

    // Seleciona os elementos de toggle para mostrar/ocultar as senhas
    const togglePassword = document.getElementById('togglePassword');
    const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');

    // Adiciona event listener para mostrar/ocultar a senha
    if (togglePassword) {
        togglePassword.addEventListener('click', function () {
            const passwordInput = document.getElementById('senha');
            togglePasswordVisibility(passwordInput, this);
        });
    }

    // Adiciona event listener para mostrar/ocultar a confirmação da senha
    if (toggleConfirmPassword) {
        toggleConfirmPassword.addEventListener('click', function () {
            const confirmPasswordInput = document.getElementById('confirm_senha');
            togglePasswordVisibility(confirmPasswordInput, this);
        });
    }

    // Função para alternar a visibilidade da senha
    function togglePasswordVisibility(input, icon) {
        if (input.type === 'password') {
            input.type = 'text';  // Mostra a senha
            icon.classList.add('fa-eye-slash');
            icon.classList.remove('fa-eye');
        } else {
            input.type = 'password';  // Oculta a senha
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Função para validar se as senhas são iguais
    function validatePasswords() {
        const senha = senhaInput.value;
        const confirmSenha = confirmSenhaInput.value;
        const senhaMatch = document.getElementById('req-match');

        if (senha === confirmSenha) {
            senhaMatch.style.color = '#009900';  // Verde para senhas iguais
            senhaMatch.textContent = 'Senhas são iguais';
        } else {
            senhaMatch.style.color = 'red';  // Vermelho para senhas diferentes
            senhaMatch.textContent = 'Certifique-se de que as senhas são iguais';
        }
    }

    // Adiciona event listeners para validar os requisitos da senha e se as senhas são iguais
    if (senhaInput && confirmSenhaInput) {
        senhaInput.addEventListener('input', function () {
            const senha = this.value;
            // Verifica os requisitos da senha
            document.getElementById('req-length').style.color = senha.length >= 6 && senha.length <= 64 ? '#009900' : 'red';
            document.getElementById('req-letter').style.color = /[a-zA-Z]/.test(senha) ? '#009900' : 'red';
            document.getElementById('req-number').style.color = /[0-9]/.test(senha) ? '#009900' : 'red';
            document.getElementById('req-special').style.color = /[!@#$%&*]/.test(senha) ? '#009900' : 'red';
            validatePasswords();  // Valida se as senhas são iguais
        });

        confirmSenhaInput.addEventListener('input', function () {
            validatePasswords();  // Valida se as senhas são iguais quando a confirmação da senha é digitada
        });

        // Adiciona event listener para bloquear a ação de colar no campo de confirmação de senha
        confirmSenhaInput.addEventListener('paste', function (e) {
            e.preventDefault();  // Previne a ação de colar
        });
    }
});

// Mascara de telefone.
function aplicarMascaraTelefone(element) {
    // Remove caracteres não numéricos
    let numero = element.value.replace(/\D/g, '');

    // Limita a entrada a 11 dígitos
    numero = numero.substring(0, 11);

    // Aplica a máscara
    numero = numero.replace(/^(\d{2})(\d)/g, "($1) $2")
                   .replace(/(\d)(\d{4})$/, "$1-$2");

    element.value = numero;
}

// Limitar a quantidade de dígitos na data de nascimento.
function limitarData(element) {
    const data = element.value;
    if (data.length > 10) {
        element.value = data.slice(0, 10);
    }
}
