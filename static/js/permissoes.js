// Exibe a mensagem inicial na div centralizada
document.getElementById('painel-permissoes').innerHTML = `<h3 class="message-center">Selecione um acesso do painel ao lado.</h3>`;

// Seleciona todos os links com a classe 'load-info'
document.querySelectorAll('.load-info').forEach(link => {
    link.addEventListener('click', function (event) {
        event.preventDefault(); // Previne o comportamento padrão do link
        const id = this.getAttribute('data-id'); // Captura o valor do data-id
        console.log('ID clicado:', id); // Apenas para debug, remova depois

        // Chama a função para carregar os dados no painel-permissoes
        loadContent(id);
    });
});

function loadUserList() {
    fetch('/listar-usuarios')
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro ao listar usuários.');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                console.error('Erro ao listar usuários:', data.error);
                alert('Erro ao listar usuários.');
                return;
            }

            const userList = data.usuarios;
            const selectElement = document.getElementById('userSelect');

            if (!selectElement) {
                console.error('Elemento userSelect não encontrado.');
                return;
            }

            selectElement.innerHTML = '<option value="">Selecione um usuário</option>';

            // Ordena os usuários pelo nome (A a Z)
            userList.sort((a, b) => a.user_name.localeCompare(b.user_name));

            // Verifica se a lista de usuários está vazia
            if (userList.length === 0) {
                console.log('Nenhum usuário encontrado.');
                alert('Nenhum usuário disponível.');
                return;
            }

            // Preenche o select com os nomes dos usuários
            userList.forEach(user => {
                const option = document.createElement('option');
                option.value = user.user_id;
                option.textContent = user.user_name;
                selectElement.appendChild(option);
            });

            console.log('Usuários carregados com sucesso:', userList);
        })
        .catch(error => {
            console.error('Erro ao carregar lista de usuários:', error);
        });
}


function loadContent(permissao_id) {
    fetch(`/gestao-permissoes/${permissao_id}`)
        .then(response => response.json())
        .then(data => {
            // Verifica se houve algum erro
            if (data.error) {
                document.getElementById('painel-permissoes').innerHTML = `<p class="text-danger">${data.error}</p>`;
                return;
            }

            // Carrega a lista de usuários ao abrir o formulário
            loadUserList();

            // Cria o formulário para adicionar novo usuário (antes da tabela)
            let formHTML = `
                <div class="mt-4 permissoes-form">
                    <h4>Inserir Novo Usuário</h4>
                    <form id="addUserForm" data-permissao-id="${permissao_id}">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="newUserId">ID do Usuário:</label>
                                <input type="text" id="newUserId" class="form-control" placeholder="Digite o ID ou selecione o nome" required>
                            </div>
                            <div class="col-md-4">
                                <label for="userSelect">Nome do Usuário:</label>
                                <select id="userSelect" class="form-control">
                                    <option value="">Selecione um usuário</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-success">Adicionar Usuário</button>
                            </div>
                        </div>
                    </form>
                </div>`;

            // Cria a tabela dinamicamente, mesmo se não houver permissões
            let tableHTML = `
                <table class="table table-striped permissoes-table">
                    <thead class="permissoes-thead">
                        <tr class="permissoes-tr">
                            <th class="permissoes-th">Permissão ID</th>
                            <th class="permissoes-th">Nome Permissão</th>
                            <th class="permissoes-th">User ID</th>
                            <th class="permissoes-th">Nome</th>
                            <th class="permissoes-th">Ações</th>
                        </tr>
                    </thead>
                    <tbody class="permissoes-tbody">`;

            const permissoes = data.info;

            if (permissoes.length === 0) {
                tableHTML += `
                    <tr class="permissoes-tr">
                        <td colspan="5" class="text-center permissoes-td">Nenhuma permissão cadastrada ainda. Adicione um novo usuário acima.</td>
                    </tr>`;
            } else {
                permissoes.forEach(permissao => {
                    tableHTML += `
                        <tr class="permissoes-tr">
                            <td class="permissoes-td">${permissao.permissao_id}</td>
                            <td class="permissoes-td">${permissao.nome_permissao}</td>
                            <td class="permissoes-td">${permissao.user_id}</td>
                            <td class="permissoes-td">${permissao.user_name}</td>
                            <td class="permissoes-td">
                                <i class="bi bi-trash delete-icon" data-user-id="${permissao.user_id}" data-permissao-id="${permissao.permissao_id}" style="cursor:pointer"></i>
                            </td>
                        </tr>`;
                });
            }

            tableHTML += `
                    </tbody>
                </table>`;

            // Renderiza o formHTML antes da tabela
            document.getElementById('painel-permissoes').innerHTML = formHTML + tableHTML;

            // Adiciona o evento de remoção aos ícones de lixeira
            document.querySelectorAll('.delete-icon').forEach(icon => {
                icon.addEventListener('click', function () {
                    const userId = this.getAttribute('data-user-id');
                    const permissaoId = this.getAttribute('data-permissao-id');

                    if (confirm('Tem certeza que deseja remover este usuário?')) {
                        removeUserFromPermissao(userId, permissaoId);
                    }
                });
            });

            // Evento de submissão do formulário para adicionar novo usuário
            document.getElementById('addUserForm').addEventListener('submit', function (e) {
                e.preventDefault();
                let newUserId = document.getElementById('newUserId').value;
                let permissaoId = this.getAttribute('data-permissao-id');

                addUserToPermissao(newUserId, permissaoId);
            });

            // Preenche o ID do usuário no campo de ID quando um nome é selecionado no select
            document.getElementById('userSelect').addEventListener('change', function () {
                document.getElementById('newUserId').value = this.value;
            });

            // Quando o campo de ID perder o foco, busca o nome do usuário se o nome não tiver sido selecionado
            document.getElementById('newUserId').addEventListener('blur', function () {
                const userId = this.value;
                const userSelect = document.getElementById('userSelect');
                if (userId && userSelect.value === "") {
                    buscarUsuarioPorId(userId);
                }
            });
        })
        .catch(error => console.error('Erro ao carregar dados:', error));
}

// Função para buscar o nome do usuário ao digitar o ID e preencher o select
function buscarUsuarioPorId(userId) {
    fetch(`/buscar-usuario/${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Usuário não encontrado:', data.error);
                alert('Usuário não encontrado.');
            } else {
                document.getElementById('userSelect').innerHTML = `<option value="${userId}">${data.user_name}</option>`;
                document.getElementById('userSelect').value = userId;
            }
        })
        .catch(error => console.error('Erro ao buscar usuário:', error));
}

function removeUserFromPermissao(userId, permissaoId) {
    fetch(`/usuarios/${userId}/permissoes/${permissaoId}`, {
        method: 'DELETE'
    })
        .then(response => response.json())
        .then(result => {
            console.log(result.message);
            loadContent(permissaoId); // Recarrega a tabela após remover o usuário
        })
        .catch(error => console.error('Erro ao remover usuário:', error));
}

function addUserToPermissao(userId, permissaoId) {
    fetch('/adicionar-permissao', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
            user_id: userId,
            permissao_id: permissaoId
        })
    })
        .then(response => response.json())
        .then(result => {
            console.log(result.message);
            loadContent(permissaoId); // Recarrega a tabela após adicionar o usuário
        })
        .catch(error => console.error('Erro ao adicionar usuário:', error));
}
