/***************************************************************************/
/** RESPONSIVIDADE **/
/***************************************************************************/
@media screen and (max-width: 768px) {

    .admin,
    .homepage,
    .confirm_email,
    .inactive,
    .login,
    .pswd,
    .register,
    .usuarios,
    .change-user {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media screen and (max-width: 426px) {

    .admin,
    .homepage,
    .confirm_email,
    .inactive,
    .login,
    .pswd,
    .register,
    .usuarios,
    .change-user {
        grid-template-columns: repeat(1, 1fr);
        padding: 3rem 2rem;
    }
}

@media screen and (max-width: 768px) {

    .header-botton,
    .header-botton-rh,
    .bloco-container {
        flex-direction: column;
        /* Empilha os itens verticalmente */
        gap: 1rem;
    }

    .header-botton,
    .header-botton-rh {
        gap: 1rem;
        /* Reduz o espaçamento entre os elementos */
        height: auto;
        /* Ajusta a altura automaticamente baseado no conteúdo */
        padding: 1rem;
        /* Adiciona um pouco de padding para não ficar muito apertado */
    }

    .bloco-container {
        gap: 1rem;
        /* Ajusta o gap entre os itens do container para telas menores */
    }

    #brazil-float-box,
    .session-container {
        padding: 10px;
        /* Reduz o padding para telas menores */
    }
}

@media screen and (max-width: 426px) {

    .header-submenu,
    .bloco-container {
        flex-direction: column;
        gap: 0.5rem;
    }

    .header-submenu {
        margin-top: 1rem;
        /* Ajusta a margem superior */
    }

    .number-container {
        width: auto;
        /* Faz o container se ajustar automaticamente à tela */
        margin: 0 auto;
        /* Centraliza o container */
        padding: 10px;
        /* Ajusta o padding */
    }
}

/***************************************************************************/
/** HEADER PADRÃO **/
/***************************************************************************/

.pn-header {
    height: 100%;
    font-size: 10px;
    background-color: #FFF;
}

.pn-header .pn-header-top {
    border-bottom: none;
    height: 64px;
    /* Ajuste para caber a altura do seu fundo azul */
}

.pn-header .pn-header-top .pn-container {
    display: flex;
}

.pn-panel-title a {
    text-decoration: none;
    color: #47484c;
}

.pn-container {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
    position: absolute;
    top: 0;
    z-index: 1000;
    background: #235397;
    margin: 0 auto;
    height: 5rem;
    right: 0.5px;
}

.pn-container img.home-header-image {
    width: auto;
    height: 70px;
    margin-top: 20px;
    margin-right: 15px;
}

.home-user-dropdown .home-user-dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: #f9f9f9;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1001;
}

.home-user-dropdown .home-user-dropdown-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    font-size: 0.9rem;
}

.home-user-dropdown .home-user-dropdown-content a:hover {
    background-color: #f1f1f1;
}

.show {
    display: block !important;
}

.pn-header .pn-header-top .pn-top-menu {
    list-style-type: none;
    width: 628px;
    height: 100%;
    margin: 0;
    padding: 0;
    float: left;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: baseline;
}

.pn-header .pn-header-top .pn-top-menu .item.item-selected .link {
    height: 78.5px;
    background-color: #fff;
    color: #D66833;
    font-weight: 700;
    border-color: #FFF;
    border-left-width: 1px;
    border-right-width: 1px;
    border-style: solid;
}

/*Borda laranja acima*/
.pn-header .pn-header-top .pn-top-menu .item.item-selected .pn-menu-ruler {
    height: 6px;
    background: #D66833;
}

.pn-header .pn-header-top .pn-top-menu .item .link {
    text-decoration: none;
    /* Remove o sublinhado dos links */
    padding: 0px 16px;
    height: 100%;
    /* Estique o link para ocupar a altura total do contêiner */
    display: flex;
    /* Use flexbox para alinhamento */
    align-items: center;
    /* Centralize verticalmente */
    justify-content: center;
    /* Centralize horizontalmente */
    color: #fff;
    position: relative;
    font-family: 'Montserrat', sans-serif;
    font-size: 1rem;
    /* Adjust the font size as needed */
    color: #fff;
    /* Adjust to match text color, this is white */
    padding: 20px 15px;
    margin: 0 10px;
    /* Opcional: Adicione espaço entre os itens do menu */
    font-weight: 600;
    text-transform: uppercase;
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    -ms-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    transition: all 0.2s linear;
    box-sizing: border-box;
    text-align: center;
}

.pn-header .pn-header-top .pn-top-menu .item .link:hover,
.pn-header .pn-header-top .pn-top-menu .item.item-selected .link {
    font-weight: bold;
    /* Torne o item selecionado em negrito */
    color: #D66833;
    /* Use uma cor distinta para o item selecionado */
}

/* Remova as réguas se elas não forem mais necessárias */
.pn-header .pn-header-top .pn-top-menu .item .pn-menu-ruler {
    margin: 0 10px;
}

.pn-header .pn-header-top .pn-top-menu .item:hover .pn-menu-ruler,
.pn-header .pn-header-top .pn-top-menu .item.item-selected .pn-menu-ruler {
    background: #D66833;
    /* This sets the ruler color on hover/active */
}

.pn-header .pn-header-top .pn-top-menu .item .link:hover {
    color: #D66833;
    font-weight: 700;
}

.menu-icon {
    cursor: pointer;
    display: flex;
    align-items: center;
    padding-right: 40px;
}

.menu-icon .bi {
    font-size: 2rem;
    color: #fff;
}

.header_left {
    display: flex;
    align-items: center;
    justify-content: start;
    width: 100%;
    min-height: 59px;
}

.header_right {
    display: flex;
    align-items: center;
    justify-content: end;
    width: 100%;
    min-height: 59px;
}

/* Estilo para o overlay preto semi-transparente */
.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    transition: opacity 0.3s ease;
    opacity: 0;
}

.overlay.show {
    opacity: 1;
    display: block;
}

/* Estilo para o menu dropdown */
.home-menu-dropdown .header-dropdown-content {
    position: fixed;
    left: 0;
    top: 0;
    width: 25%;
    height: 100%;
    background-color: #235397f7;
    z-index: 1001;
    overflow-y: auto;
    padding-top: 64px;
    transition: transform 0.5s ease, opacity 0.5s ease;
    transform: translateX(-100%);
    opacity: 0;
}

.home-menu-dropdown .header-dropdown-content.show {
    transform: translateX(0);
    opacity: 1;
    display: block;
}

.home-menu-dropdown .header-dropdown-content a {
    color: white;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    font-size: 1.1rem;
}

/* Estilo para o submenu */
.submenu {
    display: none;
    background-color: #235397f7;
    padding-left: 20px;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    /* Adiciona transição */
    max-height: 0;
    overflow: hidden;
    opacity: 0;
}

.submenu.show {
    display: block;
    /* Certifique-se de que o submenu é exibido */
    max-height: 500px;
    /* Ajusta conforme necessário */
    opacity: 1;
}

.dropdown-link.active+.submenu {
    max-height: 500px;
    /* Ajusta conforme necessário */
    opacity: 1;
}

/* Classe para exibir os elementos */
.show {
    display: block !important;
}

i.bi.bi-list {
    margin-left: 20px;
}

/* Estilos para o item do menu com dropdown */
.pn-header .pn-header-top .pn-top-menu .item.dropdown {
    position: relative;
}

.pn-header .pn-header-top .pn-top-menu .item.dropdown:hover .submenu-dropdown {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* Estilos para o submenu-dropdown */
.submenu-dropdown {
    display: none;
    position: absolute;
    background-color: #235397f7;
    /* Cor com transparência */
    width: auto;
    /* Ajuste conforme necessário */
    padding: 20px 0;
    z-index: 1000;
    left: 0;
    /* Alinhado à esquerda do contêiner pai */
    top: 83%;
    /* Alinhado ao fundo do item pai */
    box-sizing: border-box;
    transition: opacity 0.3s ease, transform 0.3s ease;
    /* Transição suave */
    opacity: 0;
    transform: translateY(-20px);
    /* Efeito de deslocamento */
}

/* Adicione essa classe para ajustar a visibilidade quando o submenu for exibido */
.submenu-dropdown.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* Estilos para os links dentro do submenu-dropdown */
.submenu-dropdown a {
    color: white;
    text-decoration: none;
    display: block;
    padding: 10px 20px;
    font-size: 1rem;
}

/* Estilo específico para .submenu-letter */
.submenu-letter {
    color: #ffffff !important;
    cursor: pointer !important;
    text-decoration: none !important;
}

.submenu-letter:hover {
    text-decoration: underline;
}

/* Adicionando transições suaves */
.home-menu-dropdown .header-dropdown-content.show {
    transform: translateX(0);
    opacity: 1;
    display: block;
}

.submenu.show {
    max-height: 500px;
    /* Ajusta conforme necessário */
    opacity: 1;
    transition: max-height 0.3s ease, opacity 0.3s ease;
}


/***************************************************************************/
/** FOOTER PADRÃO **/
/***************************************************************************/
.footer-container {
    width: 100%;
    background-color: #235397;
    padding: 0;
    margin: 0;
    position: relative;
    clear: both;
    /* Garante que o footer fique abaixo de todos os elementos flutuantes */
}

#footer-box {
    font-family: 'Montserrat', sans-serif;
    background: #235397;
    width: 100%;
    padding: 1rem;
    box-sizing: border-box;
}

#footer-images {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    position: relative;
}

img.footer-logo-image {
    width: auto;
    padding: 0.8rem;
    height: 70px;
}

img.footer-gptw-image {
    width: auto;
    padding: 0.8rem;
    height: 130px
}

#footer-social {
    display: flex;
    gap: 2rem;
    padding-left: 2.5rem;
}

#footer-social .bi {
    font-size: 1.3rem;
    color: #fff;
    transition: color 0.3s ease;
}

#footer-social .bi:hover {
    color: #D66833;
}

.footer-gptw-container {
    display: flex;
    align-items: center;
    margin-left: auto;
    /* Move o contêiner para a direita */
}

.footer-separator {
    width: 100%;
    height: 1px;
    background-color: #ffffff;
}

p.copyright {
    color: #fff;
    text-align: center;
}

.content-container {
    flex: 1 0 auto;
    width: 100%;
    padding-bottom: 100px;
}

/* Ajustes para garantir que o footer fique no fundo */
html,
body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main {
    flex: 1;
    width: 100%;
}


/***************************************************************************/
/** BREVE.HTML **/
/***************************************************************************/
.breve .pn-header {
    height: 7%;
}

.breve main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}


/***************************************************************************/
/** LOGIN.HTML **/
/***************************************************************************/
.login {
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    height: 500px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #235397;
    position: absolute;
    color: rgb(0, 0, 0);
    inset: 0px;
    overflow: hidden;
}

.flash-message.message {
    background: white;
    font-size: 40px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.header-image {
    text-align: center;
    margin-bottom: 20px;
    margin-top: 50px;
}

.header-image img {
    width: 250px;
    height: auto;
}

.login-container {
    width: 700px;
    max-width: 90%;
    position: relative;
    background: #fff;
    padding: 2rem;
    border-radius: 8px;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.login-form h2 {
    text-align: center;
}

.input-group {
    width: 100%;
    position: relative;
}

.password-wrapper {
    position: relative;
}

#btn-senha {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    padding: 10px;
    z-index: 2;
    cursor: pointer;
}

.login .input-group input[type='email'],
.login input[type='password'],
.login input[type='text'] {
    width: calc(100% - 20px);
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    outline: 0;
}

button[type='submit'] {
    padding: 10px 20px;
    color: white;
    background-color: #D66833;
    border-color: black;
    font-weight: bold;
    border-radius: 5px;
    cursor: pointer;
    display: block;
    margin-left: auto;
}

button[type='submit']:hover {
    color: white;
    background-color: #874120;
    border-color: #874120;
    width: auto;
}

.register-link {
    text-align: right;
    margin-top: 10px;
    color: white;
}

.register-link a {
    color: #D66833;
    text-decoration: none;
}

.register-link a:hover {
    text-decoration: underline;
}

.password-wrapper i {
    font-size: 30px;
    width: 20px;
    color: #c1c4ca;
    cursor: pointer;
    right: 5%;
}

a#fgt-pswd {
    text-decoration: none;
    color: #D66833;
    font-weight: bold;
}

p.rotulo-login {
    margin: 0;
    padding: 0;
    color: #fff;
    line-height: 1.5;
}


/***************************************************************************/
/** REGISTER.HTML **/
/***************************************************************************/
.register {
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    height: 500px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background: #235397;
    position: absolute;
    color: rgb(0, 0, 0);
    inset: 0px;
    overflow: auto;
    flex-direction: column;
}

.register-form {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    width: 600px;
    position: static;
    margin: 0 auto;
    box-sizing: border-box;
}

@media (max-width: 768px) {
    .register-container {
        width: 90%;
        /* Mais largura em telas menores */
    }
}

.header-image-register {
    text-align: center;
    margin-bottom: 20px;
    margin-top: 20px;
}

.header-image-register img {
    width: 300px;
    height: auto;
}

.register-form label {
    display: block;
    margin-top: 10px;
}

.register-form h2 {
    text-align: center;
}

.register-form input[type="text"],
.register-form input[type="date"],
.register-form input[type="password"],
.register-form input[type="email"],
.register-form input[type="text"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    outline: 0;
    margin-top: 5px;
    margin-bottom: 15px;
    box-sizing: border-box;
    outline: 0;
}

.select-content {
    display: flex;
    gap: 3rem;
}

.editSelect-register {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    outline: 0;
    margin-top: 5px;
    margin-bottom: 15px;
    box-sizing: border-box;
    outline: 0;
}

.submit-register button[type='submit'] {
    padding: 10px 20px;
    color: white;
    background-color: #D66833;
    border-color: black;
    font-weight: bold;
    border-radius: 5px;
    cursor: pointer;
    display: block;
    margin-left: auto;
}

.submit-register button[type='submit']:hover {
    background-color: #874120;
    border-color: #874120;
}

.submit-register button[type='submit'] {
    padding: 10px 20px;
    color: white;
    background-color: #D66833;
    border-color: black;
    font-weight: bold;
    height: auto;
    border-radius: 5px;
    cursor: pointer;
    align-self: flex-end;
    margin-left: auto;
}

.submit-register button[type='submit']:hover {
    color: white;
    background-color: #874120;
    border-color: #874120;
    width: auto;
}

.register-form .error {
    color: red;
    margin-top: 10px;
}

.login-link {
    text-align: right;
    margin-top: 20px;
    font-size: 1.2rem;
    color: white;
    padding-bottom: 100px;
}

.login-link a {
    color: #D66833;
    text-decoration: none;
}

.login-link a:hover {
    text-decoration: underline;
}

.input-group-register {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group-register-addon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-60%);
    padding: 10px;
    z-index: 2;
    cursor: pointer;
}

.input-group-register .fa-eye {
    font-size: 22px;
    width: 20px;
    color: #c1c4ca;
    cursor: pointer;
    position: absolute;
}

.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border-left-color: #09f;
    animation: spin 1s ease infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}



/***************************************************************************/
/** HOMEPAGE.HTML **/
/***************************************************************************/
.homepage {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #fff;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.homepage main.content-container {
    padding: 0px;
    margin-left: -5px;
}

#home-float-box {
    background-color: #fff;
    font-family: 'Montserrat', sans-serif;
    height: 700px;
    padding: 20px;
    margin-top: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 5;
    flex-grow: 1;
}

/* Ajuste de fontes para inputs de tipo data - Erick */
input[type="date"] {
    font-family: 'Montserrat', sans-serif;
    font-size: 1rem;
    color: #235397;
    padding: 10px;
    border: 1px solid #235397;
    border-radius: 5px;
    box-sizing: border-box;
}

#multinotas-form {
    background-color: #fff;
    border-radius: 10px;
    max-width: 1100px;
    /* Largura máxima para o formulário */
    width: 100%;
    /* Faz o formulário expandir até a largura da float-box */
    margin-top: 20px;
    /* Espaçamento superior */
    font-family: 'Montserrat', sans-serif;
    color: #235397;

}

.form-group {
    display: flex;
    /* Usa flexbox para alinhar label e input */
    justify-content: space-between;
    /* Separa label e input */
    align-items: center;
    /* Alinha verticalmente */
    margin-bottom: 10px;
    /* Espaçamento entre os grupos */
    font-weight: bold;
}

.form-group label {
    flex-basis: 70%;
    /* Ajusta o tamanho base do label */
}

.form-group input[type="file"] {
    flex-basis: 28%;
    /* Ajusta o tamanho base do input */
    cursor: pointer;
    /* Muda o cursor para indicar que é clicável */
}

#multinotas-form .submit-btn {
    padding: 10px 20px;
    background-color: #235397;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    width: auto;
    /* Permite que o botão tenha largura baseada no conteúdo */
    margin-top: 20px;
    align-self: flex-end;
    /* Alinha o botão à direita */
}

#multinotas-form .submit-btn:hover {
    background-color: #1e4d80;
}

.submit-group {
    text-align: left;
    /* Alinha o botão de envio à esquerda */
}

#multinotas-form .form-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

#multinotas-form .form-group label {
    margin-right: 10px;
}

#multinotas-form .form-group input[type="file"] {
    flex-grow: 1;
}

#multinotas-form .form-group {
    display: flex;
    flex-wrap: nowrap;
    /* Garante que o input possa ir para a linha de baixo se não couber */
    justify-content: space-between;
    /* Alinha os elementos com espaço entre eles */
    align-items: start;
    /* Alinha os elementos verticalmente */
    margin-bottom: 1px;
}

#multinotas-form .form-group label {
    flex-basis: 30%;
    /* Define a base do tamanho do label para não ultrapassar 30% */
    margin-right: 10px;
    /* Espaço entre o label e o input */
}

#multinotas-form .form-group input[type="file"] {
    flex-basis: 65%;
    /* Define a base do tamanho do input para não ultrapassar 65% */
    /* Adiciona uma margem direita para alinhamento */
}

#multinotas-form .submit-group {
    display: flex;
    justify-content: flex-start;
    /* Alinha o botão à esquerda */
}

.homepage .home-head-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
    position: absolute;
    top: 0;
    z-index: 1000;
}

body.homepage {
    padding-top: 100px;
}

.home-head-container img.home-header-image {
    width: auto;
    height: 80px;
}

.home-user-dropdown {
    position: relative;
    display: inline-block;
}

.show {
    display: block;
}

.home-user-icon-container {
    cursor: pointer;
    display: flex;
    align-items: center;
    padding-right: 40px;
}

.home-user-icon-container .fas {
    font-size: 2rem;
    color: #fff;
}

#home-sidebar {
    padding-top: 20px
}

.homepage #home-sidebar {
    background-color: #235397;
    color: #fff;
    width: 233px;
    height: 0.1%;
    font-size: 15px;
    height: auto;
    position: static;
    z-index: 10;
    flex: 0 0 233px;
}

#home-sidebar ul {
    list-style-type: none;
    padding: 5px;
}

#home-sidebar ul li {
    position: relative;
    margin-bottom: 10px;
}

#home-sidebar ul li a {
    color: #fff;
    padding: 5px 10px;
    display: block;
    text-decoration: none;
    transition: background-color 0.3s;
}

#home-sidebar ul li a:hover {
    background-color: #0d63a5;
}

#home-sidebar ul li a .fas {
    margin-right: 10px;
}

.home-menu-item .home-submenu {
    display: none;
    /* Esconde o submenu */
    position: absolute;
    /* Posiciona o submenu */
    top: 100%;
    /* Posiciona o submenu abaixo do item do menu */
    left: 0;
    /* Alinha o submenu à esquerda do item do menu */
    background-color: #1e4d80;
    /* Cor azul mais acinzentada para o submenu */
    min-width: 190px;
    /* Largura mínima para o submenu */
    z-index: -1;
}

.home-menu-item:hover .home-submenu {
    display: grid;
    /* Mostra o submenu quando passa o mouse */
    z-index: 100;
}

.home-menu-item .home-submenu li a {
    padding: 10px;
    background-color: #2a6fb0;
    color: #fff;
    border-left: 4px solid #0d63a5;
}

.home-menu-item .home-submenu li a:hover {
    background-color: #235397;
}

.home-modal {
    display: none;
    /* Escondido por padrão */
    position: fixed;
    /* Posicionamento fixo na página */
    z-index: 2000;
    /* Sobre os outros elementos */
    left: 0;
    top: 0;
    width: 100%;
    /* Largura total */
    height: 100%;
    /* Altura total */
    overflow: auto;
    /* Permitir rolagem se necessário */
    background-color: rgba(0, 0, 0, 0.4);
    /* Cor de fundo semi-transparente */
}

.home-modal-content {
    background-color: #fefefe;
    margin: 10% auto;
    height: 500px;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
}

.home-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.home-close:hover,
.home-close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

#home-main-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* Isso alinha os filhos ao centro */
    width: auto;
    /* Isso permite que o contêiner se ajuste ao conteúdo */
    max-width: 100%;
    /* Isso limita o contêiner a não exceder a largura da tela */
    padding: 20px;
    /* Espaço interno */
}

#home-main-content>div {
    padding: 10px;
}

#home-main-content img {
    max-width: 100%;
    /* faz a imagem ser responsiva e não exceder o tamanho do bloco */
    height: auto;
    /* mantém a proporção da imagem */
}

.insta-content {
    margin-top: 3%;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* Centraliza os filhos horizontalmente */
    justify-content: center;
    /* Centraliza os filhos verticalmente, se necessário */
    width: 100%;
    /* Ocupa 100% da largura da tela */
    background-color: #E8E9EC;
    padding: 1rem 0;
    /* Espaçamento superior e inferior para .insta-content */
    gap: 1rem;
    /* Espaçamento entre itens internos */
}

.carousel-inner {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    /* Ajusta os itens do carrossel para o início, removendo o centralizado horizontal */
    align-items: center;
    flex-wrap: nowrap;
    overflow-x: auto;
    width: 100%;
    height: auto;
    /* Altura automática para manter a proporção */
    margin-bottom: 0.6rem;
    /* Espaçamento entre os itens quando estão em coluna */
    margin-left: 0.6rem;
    /* Espaçamento entre os itens quando estão em coluna */
}

.carousel-item {
    width: calc(100% / 1);
    flex: 0 0 auto;
    /* Os itens não vão crescer ou encolher e terão largura automática baseada no conteúdo */
    width: 289px;
    /* Largura fixa para os itens */
    height: 289px;
    /* Altura fixa para os itens */
    margin-right: 0.6rem;
    /* Espaçamento entre os itens */
    display: flex;
    /* Para centralizar o conteúdo do vídeo/imagem verticalmente */
    align-items: center;
    /* Alinhamento vertical central */
    justify-content: center;
    /* Alinhamento horizontal central */
    overflow: hidden;
    /* Esconde o conteúdo que passar do tamanho máximo */
}

.carousel-item img,
.carousel-item video {
    width: 100%;
    /* Define a largura da imagem ou vídeo para preencher o item do carrossel */
    height: auto;
    /* Ajusta a altura automaticamente para manter a proporção */
    max-height: 100%;
    /* Evita que o vídeo/imagem ultrapasse a altura do item do carrossel */
}

.rotulo_insta {
    font-weight: bold;
    margin-right: 75%;
}

.video-container {
    position: relative;
}

.video-icon {
    position: absolute;
    right: 5px;
    /* Ajusta para uma pequena distância da direita */
    z-index: 10;
    /* Garante que o ícone fique acima de qualquer outro conteúdo */
}

/*------------- informativo -----------------*/
.informativos-container {
    margin-top: 1%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 1rem 0;
    gap: 1rem;
}

.rotulo_inform {
    font-weight: bold;
    margin-right: auto;
    /* Ajuste para alinhar à esquerda */
    font-size: 1.1rem;
    padding-left: 1rem;
    /* Adicione um pouco de preenchimento à esquerda */
}

.informativo-card {
    position: relative;
    width: 300px;
    height: 500px;
    border: 1px solid #ddd;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease-in-out;
    background-color: #fff;
    /* Adicione um fundo branco para consistência */
    display: flex;
    flex-direction: column;
    /* Para que o conteúdo se expanda corretamente */
}

.informativo-card:hover {
    transform: translateY(-2px);
}

.informativo-card img {
    width: 100%;
    height: 200px;
    /* Define a altura da imagem para que todas tenham o mesmo tamanho */
    display: block;
    object-fit: cover;
    /* Ajusta a imagem para cobrir a área sem distorcer */
}

.informativo-content {
    padding: 15px;
    flex-grow: 1;
    /* Permite que o conteúdo preencha o espaço restante */
}

.informativo-title {
    font-size: 1.1em;
    margin-bottom: 5px;
    text-align: center;
    font-weight: bold;
}

.informativo-excerpt {
    font-size: 0.9em;
    color: #555;
    overflow: hidden;
    text-align: justify;
    /* Ajuste o texto para justificar */
    flex-grow: 1;
    /* Faz o excerpt ocupar o espaço disponível */
}

.informativo-footer {
    padding: 10px 15px;
    background-color: #E8E9EC;
    text-align: center;
    /* Ajuste para centralizar o botão */
    margin-top: auto;
    /* Empurra o footer para o fim do card */
}

.saiba-mais-btn {
    background-color: #0056b3;
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.saiba-mais-btn:hover {
    background-color: #003580;
}

.inform_add {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: -1%;
}

.btn-inform {
    background-color: #D66833;
    border: #603018;
    color: white;
    text-decoration: none;
}

.btn-inform:hover {
    background-color: #df8457;
    text-decoration: none;
}

a.btn.btn-inform {
    margin-left: auto;
    margin-right: 1rem;
    /* Ajuste a margem para alinhar corretamente */
}

.inform-container {
    display: flex;
    flex-direction: row;
    gap: 1.7rem;
    justify-content: center;
    margin-left: 2rem;
}

.delete-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    display: none;
    cursor: pointer;
    color: red;
    font-size: 20px;
}

.informativo-card:hover .delete-btn {
    display: block;
}

.informativos-carousel {
    position: relative;
    overflow: hidden;
    width: 95%;
}

.informativos-carousel-inner {
    display: flex;
    transition: transform 0.5s ease;
    width: 100%;
}

.informativos-carousel-item {
    min-width: 300px;
    /* Ajuste a largura conforme necessário */
    flex: 0 0 auto;
    margin-right: 1rem;
}

.informativos-carousel-control-prev,
.informativos-carousel-control-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 10px;
    cursor: pointer;
}

.informativos-carousel-control-prev {
    left: 10px;
}

.informativos-carousel-control-next {
    right: 10px;
}

.container.detalhes-informativo {
    background: #ebebeb;
    border-radius: 3px;
    text-align: center;
    max-width: 1000px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: 3rem;
    font-size: 1.1rem;
    margin-top: 70px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 100px;
    padding: 1rem;
}

.imagem-informativo img {
    width: 100%;
    height: auto;
    border-radius: 10px;
}

@media (max-width: 1024px) {
    .container.detalhes-informativo {
        margin-left: 1rem;
        margin-right: 1rem;
    }
}

/*------------------------------*/

@media (min-width: 2134px) and (max-width: 2400px) {
    .carousel-item {
        width: 320px;
        height: 320px;
        justify-content: center;
    }

    .inform_add {
        margin-left: 10%;
    }

    .rotulo_inform {
        font-size: 1.3rem;
    }

    .informativos-carousel-inner {
        width: 100%;
        justify-content: center;
        margin-left: 0.7rem;
    }

    .informativos-carousel-item {
        width: 320px;
    }
}

/* Responsividade para telas até 1920px */
@media (max-width: 1920px) {
    .carousel-inner {
        width: 100%;
        height: auto;
        margin-bottom: 0.6rem;
    }

    .carousel-item {
        width: 249px;
        height: 249px;
        justify-content: center;
    }

    .carousel-item:last-child {
        margin-right: 0;
    }

    .carousel-item img,
    .carousel-item video {
        height: auto;
    }

    .rotulo_insta {
        margin-right: 72%;
    }

    a.btn.btn-inform {
        margin-left: 1380px;
    }

    .container.detalhes-informativo {
        margin-left: 20%;
    }

    .inform-container {
        width: 100%;
        height: auto;
        margin-bottom: 0.6rem;
    }

    .inform_add {
        margin-left: 25%;
    }
}

/* Responsividade para telas entre 1707px e 1920px */
@media (min-width: 1707px) and (max-width: 1919px) {
    .carousel-inner {
        width: 100%;
        height: auto;
        margin-bottom: 0.6rem;
    }

    .carousel-item {
        width: 229px;
        height: 229px;
        justify-content: center;
    }

    .carousel-item:last-child {
        margin-right: 0;
    }

    .carousel-item img,
    .carousel-item video {
        height: auto;
    }

    .rotulo_insta {
        margin-right: 72%;
    }

    .rotulo_inform {
        margin-right: 80%;
    }

    .inform_add {
        margin-left: 15%;
    }

    a.btn.btn-inform {
        margin-left: 1380px;
    }

    .container.detalhes-informativo {
        margin-left: 20%;
    }
}

/* Responsividade para telas até 1190px */
@media (max-width: 1190px) {
    .carousel-inner {
        width: 100%;
        height: auto;
        margin-bottom: 0.6rem;
    }

    .carousel-item {
        width: 157px;
        height: 157px;
        justify-content: center;
    }

    .carousel-item:last-child {
        margin-right: 0;
    }

    .carousel-item img,
    .carousel-item video {
        height: auto;
    }

    .rotulo_insta {
        margin-right: 60%;
    }

    .rotulo_inform {
        margin-right: 60%;
    }

    a.btn.btn-inform {
        margin-left: 870px;
    }

    .container.detalhes-informativo {
        margin-left: 7%;
    }
}

.aniversariantes-container {
    background-color: #FFFFFF;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    margin-bottom: 30px;
    width: 50%;
}

#calendar-aniversariantes {
    max-width: 100%;
    margin: 0 auto;
    height: 500px;
}

.modal-atualizacao-homepage {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content-atualizacao-homepage {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 30px;
    border: 1px solid #888;
    border-radius: 5px;
    width: 40%;
    height: 25%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
}

.btn-atualizar {
    background-color: #4CAF50;
    color: white;
    padding: 15px 20px;
    border: none;
    cursor: pointer;
    width: 100%;
    opacity: 0.9;
}

select#setor {
    padding: 6px 10px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: none;
    outline: none;
    margin-left: 1rem;
}

select#setor:focus {
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

button.btn-atualizar-modal {
    width: 100%;
}

button.btn-atualizar-modal:hover {
    width: 100%;
    opacity: 1;
}


/***************************************************************************/
/** BRAZIL.HTML **/
/***************************************************************************/
.brazil {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #E8E9EC;
    display: flex;
    flex-direction: column;
}

.brazil .pn-header {
    height: 7%;
}

.brazil main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.header-botton {
    display: flex;
    flex-direction: row;
    align-items: center;
    /* Certifique-se de que os itens são centralizados horizontalmente */
    justify-content: center;
    background: #fff;
    height: 150px;
    line-height: 94px;
    font-size: 1.2rem;
    gap: 8rem;
}

.pn-panel-title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    /* Centraliza horizontalmente */
    margin: 0;
    /* Remove margens adicionais */
    line-height: 1;
    /* Reduz o espaçamento entre linhas */
    font-size: 1.2rem;
    /* Ajuste o tamanho da fonte conforme necessário */
}

.pn-header .pn-header-bottom .pn-panel-title {
    float: left;
    width: 55px;
    height: 94px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-right: 16px;
}

.pn-header .pn-header-bottom .pn-panel-title h2 a {
    color: #47484c;
}

.pn-panel-title h2 {
    display: grid;
    margin: 0;
    /* Remove margens padrão do título */
    font-weight: lighter;
    font-size: 1.2rem;
    /* Pode ajustar para tamanho desejado */
    line-height: 0.6;
    /* Reduz o espaçamento entre linhas */
    margin-top: 30%;
    text-align: left;
}

a:hover,
a:focus,
a:active {
    text-decoration: none !important;
}

.header-submenu {
    display: flex;
    flex-direction: row;
    justify-content: center;
    list-style-type: none;
    background: #fff;
    height: 3rem;
    margin-top: 3rem;
    gap: 1.9rem;
    padding-left: 0px;
    font-size: 1.0rem;
    color: #47484c;
    font-weight: bolder;
    align-items: center;
}

.item a {
    color: #47484c;
    cursor: pointer;
    text-decoration: none;
}

.title-part {
    display: block;
    /* Faz com que cada parte ocupe a sua própria linha */
    text-align: center;
    /* Centraliza o texto */
}

#brazil-main-content {
    padding: 20px;
    margin-left: 300px;
    margin-right: 300px;
}

#brazil-float-box {
    background-color: #fff;
    font-family: 'Montserrat', sans-serif;
    height: auto;
    margin-top: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 5;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.bloco-conteiner-um {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
}

#sobre-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

#missao-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.bloco-conteiner-dois {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    gap: 70px;
}

#visao-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

#valores-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.text-container {
    text-align: left;
}

.text-container::after {
    content: '';
    display: inline-block;
    width: 100%;
}

.session-container {
    background: #E8E9EC;
    font-family: 'Montserrat', sans-serif;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.bloco-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 5rem;
}

.estrutura-container h2 {
    margin-top: 50px;
    margin-bottom: 40px;
    font-size: 2rem;
}

.number-container {
    background: #ffffff;
    border: 2px solid #c2c2c4;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px 0 30px 0;
    margin-bottom: 50px;
    width: 15rem;
    height: 10rem;
}

.numbers {
    color: #f60;
    font-family: Poppins, Sans-serif;
    font-size: 55px;
    font-weight: 600;
}

.rotulos {
    color: #000;
    font-family: 'Montserrat', sans-serif;
    font-size: 17px;
    font-weight: 400;
    line-height: 2.4em;
}

.carrossel-organograma {
    width: 65%;
    height: 65%;
    position: relative;
    overflow: hidden;
    margin-left: auto;
    /* Centraliza o carrossel horizontalmente */
    margin-right: auto;
    /* Centraliza o carrossel horizontalmente */
}

.carrossel-slides img {
    width: 100%;
    height: 100%;
    display: none;
    /* Oculta todas as imagens inicialmente */
}

.carrossel-slides img.active {
    display: block;
    /* Mostra apenas a imagem ativa */
}

.carrossel-organograma .prev,
.carrossel-organograma .next {
    cursor: pointer;
    position: absolute;
    top: 50%;
    transform: translateY(-40%);
    /* Centraliza verticalmente os botões */
    width: auto;
    padding: 16px;
    margin-top: -50px;
    color: white;
    font-weight: bold;
    font-size: 18px;
    transition: 0.6s ease;
    border-radius: 0 3px 3px 0;
    user-select: none;
}

.carrossel-organograma .prev {
    left: 0;
    /* Posiciona o botão 'Anterior' à esquerda */
}

.carrossel-organograma .next {
    right: 0;
    border-radius: 3px 0 0 3px;
}

.carrossel-organograma .prev:hover,
.carrossel-organograma .next:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

.indicadores-slide {
    text-align: center;
    position: absolute;
    bottom: 10px;
    width: 100%;
}

.indicadores-slide span {
    cursor: pointer;
    height: 15px;
    width: 15px;
    margin: 0 2px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    transition: background-color 0.6s ease;
}

.indicadores-slide span.active,
.indicadores-slide span:hover {
    background-color: #717171;
}


/***************************************************************************/
/** CALENDAR.HTML **/
/***************************************************************************/
/* Estilos para a página do calendário */
.calendar {
    margin: 40px 10px;
    padding: 0;
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    background-color: #E8E9EC;
    display: flex;
    flex-direction: column;
}

.calendar .pn-header {
    height: 7%;
}

.calendar main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.agendamento-container {
    background-color: #FFFFFF;
    /* Fundo branco para o container do calendário */
    padding: 20px;
    /* Espaçamento interno para separar o conteúdo das bordas */
    border-radius: 8px;
    /* Bordas arredondadas para o container */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    /* Sombra suave para dar profundidade */
    margin-top: 20px;
    /* Margem superior para distanciar do topo ou de outros elementos */
    margin-bottom: 30px;
}

#calendario {
    max-width: 70%;
    margin: 0 auto;
    height: 700px;
}

.fc .fc-toolbar-title {
    font-size: 1.75em;
    /* Tamanho do título do calendário */
    color: #333;
    /* Cor do título */
}

.fc .fc-button-primary {
    background-color: #FFFFFF;
    /* Fundo dos botões */
    color: #333;
    /* Cor do texto dos botões */
    border: 1px solid #CCC;
    /* Borda dos botões */
}

.fc .fc-button-primary:not(:disabled):hover {
    background-color: #F3F3F3;
    /* Fundo dos botões ao passar o mouse */
}

.fc .fc-button-active {
    background-color: #E2E2E2;
    /* Fundo dos botões ativos */
}

.fc .fc-event {
    background-color: #3174ad;
    /* Fundo dos eventos */
    border: 1px solid #295c87;
    /* Borda dos eventos */
    color: #FFF;
    /* Cor do texto dos eventos */
    font-size: 0.85em;
    /* Tamanho do texto dos eventos */
}

.fc .fc-event:hover {
    background-color: #5592c6;
    /* Cor de fundo ao passar o mouse sobre eventos */
}

.fc .fc-day-today {
    background-color: #FFF0F0;
    /* Cor de fundo para o dia atual */
}

.fc .fc-day-past {
    background-color: #F9F9F9;
    /* Cor de fundo para dias que já passaram */
}

.fc .fc-nonbusiness {
    background-color: #FFF0F0;
    /* Cor de fundo para dias não úteis */
}

.event-container {
    width: 35%;
    height: 27%;
}

.modal {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    display: flex;
    flex-direction: column;
    max-width: 600px;
    width: 90%;
    background-color: #fefefe;
    border-radius: 10px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

.modal-header {
    padding: 16px;
    background-color: #fff;
    /* Ajuste esta cor para corresponder à imagem */
    color: #D66833;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top-left-radius: 8px;
    /* Arredonda os cantos superiores */
    border-top-right-radius: 8px;
    /* Arredonda os cantos superiores */
    height: 1px;
}

.modal-header h2 {
    margin: 0;
}

.close-button {
    color: #D66833;
    font-size: 50px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 0.8rem;
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    justify-content: flex-end;
    margin-left: 97%;
}

.close-button:hover,
.close-button:focus {
    color: #bbb;
    text-decoration: none;
}

.customModal {
    height: 30%;
    width: 50%;
}

.modal-body {
    padding: 20px;
    background-color: #fefefe;
    /* Fundo branco para o corpo do modal */
}

.modal-footer {
    display: flex;
    justify-content: center;
    text-align: left;
    padding: 10px;
    background-color: #fefefe;
    text-align: right;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    gap: 9px;
}

.btn {
    flex: 1;
    margin-right: 10px;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    margin: 5px 0;
    max-width: 200px;
    width: auto;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 1rem;
}

.btn:hover {
    opacity: 0.9;
}

.btn-primary {
    background-color: #235397;
    /* Cor azul para o botão primário */
    border: #12223a;
    color: white;
    /* Texto branco para o botão primário */
}

.btn-primary:hover {
    background-color: #1a3c6d;
    /* Cor azul claro para o hover */
}

.btn-danger {
    background-color: #D66833;
    /* Cor vermelha para o botão de perigo */
    border: #552b16;
    color: white;
    /* Texto branco para o botão de perigo */
}

.btn-danger:hover {
    background-color: #944e2b;
    /* Cor vermelha escura para o hover */
}

.btn:first-child {
    margin-left: 0;
}

.form-control {
    width: 100%;
    /* Ocupa toda a largura */
    padding: 8px 12px;
    /* Espaçamento interno */
    margin-bottom: 10px;
    /* Margem inferior */
    border-radius: 4px;
    /* Borda arredondada */
    border: 1px solid #ced4da;
    /* Borda cinza */
}

.form-check-input {
    margin-right: 5px;
    /* Margem à direita do checkbox */
}

.form-check-label {
    width: 100%;
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 20px;
    /* Espaçamento entre os campos do formulário */
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

.form-control {
    width: auto;
    /* Faz com que o input ocupe a largura disponível */
    flex-grow: 1;
    /* Permite que o input cresça para preencher o espaço */
}

/* Responsividade */
@media (max-width: 600px) {
    .modal-content {
        max-width: 100%;
        margin: 10px;
    }

    .modal-header,
    .modal-footer {
        padding: 10px;
    }
}

/* Fechar o Modal */
.close-button {
    cursor: pointer;
}

@media screen and (max-width: 768px) {
    .modal-content {
        width: 100%;
        /* Ocupa toda a largura em telas menores */
        margin: 10px;
        padding: 10px;
    }

    .modal-footer {
        flex-direction: column;
        /* Coloca os botões em coluna em telas menores */
    }

    .btn {
        max-width: 80px;
        padding: 5px;
        margin: 5px 0;
    }

    .btn:first-child {
        margin-top: 0;
        /* O primeiro botão não deve ter margem no topo */
    }
}

.fc .fc-daygrid-day-top a {
    text-decoration: none !important;
}

.fc-col-header-cell a,
.fc .fc-toolbar-title,
.fc .fc-day-header span {
    text-decoration: none !important;
}

/***************************************************************************/
/** USUARIO.HTML **/
/***************************************************************************/
.usuarios {
    font-family: 'Montserrat', sans-serif;
    background: #E8E9EC;
    color: #000;
    padding: 20px;
}

.usuarios .pn-header {
    height: 7%;
}

.usuarios main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.header-image {
    text-align: center;
}

.usuarios-container {
    background: white;
    padding: 30px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    width: 90%;
    margin-top: 150px;
}

.usuarios-table {
    width: 100%;
    border-collapse: collapse;
}

.usuarios-table th,
.usuarios-table td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: left;
}

.usuarios-table th {
    background-color: #f2f2f2;
}

.usuarios-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.usuarios-table tr:hover {
    background-color: #ddf;
}

.usuarios-table td[contenteditable="true"] {
    padding: 5px;
    border: 1px dashed #235397;
}

button.update-user {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background-color: #4CAF50;
    color: white;
}

button.update-user:hover {
    background-color: #45a049;
}

.usuarios-table th,
.usuarios-table td {
    text-align: left;
}

.usuarios-table th#nome-col {
    width: 25%;
}

.usuarios-table th#data-nasc-col {
    width: 15%;
}

.usuarios-table th#cpf-col {
    width: 15%;
}

.usuarios-table th#email-col {
    width: 25%;
}

.usuarios-table th#acao-col {
    width: 10%;
}

.nome-input,
.email-input {
    width: 95%;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
    box-sizing: border-box;
}

.nome-input:focus,
.email-input:focus {
    border: 1px solid #235397;
    background-color: #e6e6e6;
}

.atualizar-link {
    color: #235397;
    text-decoration: none;
    text-align: center;
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: fit-content;
}

.atualizar-link:hover {
    text-decoration: underline;
}

.btn-voltar {
    display: inline-block;
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    margin: 10px 0;
    text-align: center;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.2s;
    font-size: 1em;
}

.btn-voltar:hover,
.btn-voltar:focus {
    background-color: #0056b3;
    color: white;
    text-decoration: none;
    outline: none;
}

.usuarios-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.picture-profile-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin-bottom: 5px;
}

img#profile-image {
    border: 2px solid black;
    width: 200px;
    height: 250px;
    object-fit: cover;
}


/***************************************************************************/
/** PASSWORD.HTML **/
/***************************************************************************/
.pswd {
    font-family: 'Montserrat', sans-serif;
    background: #E8E9EC;
    color: #000;
    padding: 20px;
}

.pswd .pn-header {
    height: 7%;
}

.pswd main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.header-image {
    text-align: center;
}

.pswd-container {
    background: #fff;
    padding: 30px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    width: 60%;
    margin-top: 6px;
    display: flex;
    margin-top: 100px;
}

.pswd-subcontainer {
    background: white;
    padding: 30px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    width: 80%;
    margin-top: 30px;
    display: flex;
    flex-direction: column;
}

.alerta-troca-senha {
    flex-basis: 40%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-right: 2px solid #ccc;
}

.icone-alerta {
    color: #cc0000;
    font-size: 3rem;
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
}

.mensagem-alerta {
    color: #555555;
    font-size: 1.2rem;
    line-height: 1.4;
    padding-top: 20px;
    text-align: left;
    width: 100%;
}

.form-troca-senha {
    flex-basis: 60%;
    padding: 20px;
    margin-top: 15px;
}

.grupo-form {
    margin-bottom: 20px;
}

.grupo-form label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-size: 0.9rem;
}

.grupo-form input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
}

.grupo-form input[type="submit"] {
    background-color: #004080;
    color: white;
    cursor: pointer;
    padding: 10px 15px;
    font-size: 1.1rem;
    border: none;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.grupo-form input[type="submit"]:hover {
    background-color: #0055cc;
}

.submit-pswd button[type='button'] {
    padding: 10px 20px;
    color: white;
    background-color: #D66833;
    border: 1px solid black;
    font-weight: bold;
    height: auto;
    border-radius: 5px;
    cursor: pointer;
    align-self: flex-end;
    margin-left: auto;
    transition: background-color 0.3s ease;
}

.submit-pswd button[type='button']:hover {
    color: white;
    background-color: #874120;
    border-color: #874120;
}

.pswd-container-button {
    display: flex;
    justify-content: flex-end;
    margin-right: 18%;
    padding-right: 10px;
    margin-top: 60px;
}

.btn-voltar-psdw {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    margin: 10px 0;
    text-align: center;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.2s;
    font-size: 1em;
}

.btn-voltar-psdw:hover,
.btn-voltar-psdw:focus {
    background-color: #0056b3;
    color: white;
    text-decoration: none;
    outline: none;
}


/***************************************************************************/
/** INACTIVE.HTML **/
/***************************************************************************/
.inactive {
    font-family: 'Montserrat', sans-serif;
    background: #235397;
    color: #000;
    padding: 20px;
    font-size: 1.2rem;
}

.header-image-inactive {
    text-align: center;
    margin-bottom: 20px;
}

.header-image-inactive img {
    width: 250px;
    height: auto;
}

.inactive-container {
    background: white;
    padding: 30px;
    border-radius: 6px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    width: 60%;
    margin-top: 60px;
    display: flex;
    flex-direction: column;
}

.inactive-container h1 {
    text-align: center;
}


/***************************************************************************/
/** CONFIRM_EMAIL.HTML **/
/***************************************************************************/
.confirm_email {
    font-family: 'Montserrat', sans-serif;
    background: #235397;
    color: #000;
    padding: 20px;
    font-size: 1.2rem;
}

.header-image-email {
    text-align: center;
    margin-bottom: 20px;
}

.header-image-email img {
    width: 250px;
    height: auto;
}

.email-container {
    background: white;
    padding: 30px;
    border-radius: 6px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    width: 60%;
    margin-top: 60px;
    display: flex;
    flex-direction: column;
}

a.login-confirmEmail {
    text-decoration: none;
    color: #D66833;
    font-weight: bold;
}

a#resendEmailCE {
    text-decoration: none;
    color: #D66833;
    font-weight: bold;
}


/***************************************************************************/
/** ADMIN.HTML **/
/***************************************************************************/
.admin {
    font-family: 'Montserrat', sans-serif;
    background: #E8E9EC;
    color: #000;
    padding: 20px;
}

.admin .pn-header {
    height: 0;
}

.admin main.content-container {
    padding: 0px;
    margin-left: -5px;
}

.header-image {
    text-align: center;
}

.admin-container {
    background: white;
    padding: 30px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    width: 90%;
    margin-top: 100px;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.admin-table th,
.admin-table td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: left;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    /* Ajuste conforme necessário para o padding */
    min-width: 120px;
    /* Ajuste conforme necessário para a largura mínima */
}

.admin-table th {
    background-color: #f2f2f2;
}

.admin-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.admin-table tr:hover {
    background-color: #ddf;
}

.admin-table td[contenteditable="true"] {
    padding: 5px;
    border: 1px dashed #235397;
}

button.update-user {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background-color: #4CAF50;
    color: white;
}

button.update-user:hover {
    background-color: #45a049;
}

.admin-table th#nome-col {
    width: 25%;
}

.admin-table th#data-nasc-col {
    width: 15%;
}

.admin-table th#cpf-col {
    width: 15%;
}

.admin-table th#email-col {
    width: 25%;
}

.admin-table th#acao-col {
    width: 10%;
}

.nome-input,
.adm-email-input,
.data_nascimento-input,
.cpf-input,
.perfil-input {
    width: 95%;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
    box-sizing: border-box;
}

.nome-input:focus,
.adm-email-input:focus .data_nascimento-input:focus,
.cpf-input:focus,
.perfil-input:focus {
    border: 1px solid #235397;
    background-color: #e6e6e6;
}

.atualizar-link {
    color: #235397;
    text-decoration: none;
    text-align: center;
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: fit-content;
}

.atualizar-link:hover {
    text-decoration: underline;
}

.btn-voltar {
    display: inline-block;
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    margin: 10px 0;
    text-align: center;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.2s;
    font-size: 1em;
}

.btn-voltar:hover,
.btn-voltar:focus {
    background-color: #0056b3;
    color: white;
    text-decoration: none;
    outline: none;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.clickable-row {
    cursor: pointer;
}

.search-admin-container {
    margin-bottom: 20px;
    /* Espaço abaixo do campo de busca */
    text-align: left;
    /* Centraliza o campo de busca */
    display: flex;
    justify-content: space-between;
    /* Alinha o campo de busca à esquerda e o botão à direita */
    align-items: center;
    /* Centraliza verticalmente */
    gap: 10px;
    /* Espaçamento entre o campo de busca e o botão */
}

#searchInput {
    padding: 10px;
    /* Espaçamento interno para o campo de busca */
    width: 50%;
    /* Largura do campo de busca */
    font-size: 16px;
    /* Tamanho da fonte */
    border: 1px solid #ccc;
    /* Borda do campo de busca */
    border-radius: 5px;
    /* Bordas arredondadas */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    /* Sombra leve para dar um efeito elevado */
    transition: width 0.4s ease-in-out;
    /* Transição suave ao focar no campo */
}

#searchInput:focus {
    border-color: #666;
    /* Cor da borda mais escura quando focado */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    /* Sombra mais destacada quando focado */
}

/* Estilos para telas menores */
@media (max-width: 600px) {
    #searchInput {
        width: 100%;
        /* Largura total em telas pequenas */
    }
}

.filter-button {
    background: none;
    /* Adiciona transparência ao fundo */
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
    float: right;
    /* Alinha o botão à direita */
    margin-top: -2px;
    /* Move o botão para cima */
    color: blue
}

#clearFilterBtn {
    margin-top: auto;
    /* Empurra o botão para baixo, alinhando-o com o fim da tabela */
    margin-right: 0;
    display: inline-block;
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    margin: 10px 0;
    text-align: center;
    border-radius: 5px;
    border: none;
    text-decoration: none;
    transition: background-color 0.2s;
    font-size: 1em;
    cursor: pointer;
}

#clearFilterBtn:hover {
    background-color: #0056b3;
    /* Cor mais escura no hover */
}

#exportExcelBtn {
    margin-top: auto;
    margin-right: 0;
    display: inline-block;
    background-color: #007bff;
    color: white;
    padding: 8px 10px;
    text-align: center;
    border-radius: 5px;
    border: none;
    text-decoration: none;
    transition: background-color 0.2s;
    font-size: 0.8rem;
    cursor: pointer;
}

#exportExcelBtn:hover {
    background-color: #0056b3;
    /* Cor mais escura no hover */
}

a.pass-pag, span.pass-pag {
    text-decoration: none;
    color: #007bff;
    padding: 8px 15px;
    border: 1px solid #007bff;
    border-radius: 4px;
    transition: all 0.3s ease;
    margin: 0 5px;
    display: inline-block;
}

a.pass-pag:hover {
    color: white;
    background-color: #007bff;
}

span.pass-pag {
    border-color: #ccc;
    color: #ccc;
}

/* Estilos para o menu dropdown */
.dropdown-menu {
    position: absolute;
    z-index: 1000;
    /* Alta prioridade para garantir visibilidade */
    background-color: white;
    border: 1px solid #ccc;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    padding: 10px;
    border-radius: 5px;
    width: 300px;
    display: none;
    /* Escondido por padrão */
}

.dropdown-menu.show {
    display: block;
    /* Mostra quando necessário */
}

.dropdown-item {
    padding: 5px;
    cursor: pointer;
}

.dropdown-item:hover {
    background-color: #f0f0f0;
}

.active-filter {
    background-color: #007bff;
    color: white;
}

.filter-dropdown {
    position: absolute;
    z-index: 100;
    /* Certifique-se de que está acima de outros elementos */
    background: #fff;
    border: 1px solid #ccc;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    padding: 10px;
    border-radius: 4px;
    width: 200px;
    /* ou o tamanho que você desejar */
}

.filter-sort i {
    cursor: pointer;
    margin-right: 5px;
}

.filter-options {
    max-height: 150px;
    overflow-y: auto;
}

.filter-actions button {
    margin-top: 10px;
    margin-right: 5px;
    cursor: pointer;
}

.sort-options {
    display: flex;
    margin-bottom: 5px;

}

.sort-options button {
    background: none;
    /* Remove o background */
    border: none;
    /* Remove a borda */
    padding: 5px;
    /* Ajuste de padding conforme necessário */
    font-size: 22px;
    /* Ajuste do tamanho do texto conforme necessário */
    cursor: pointer;
    /* Cursor de ponteiro para indicar que é clicável */
    color: black;
    /* Cor do texto, ajuste conforme necessário */
}

.sort-options button:hover {
    color: #0056b3;
}

.sort-options p {
    margin-left: 10px;
}

/* Aumentar a caixa de texto de pesquisa dentro do filtro */
.filter-dropdown input[type="text"] {
    width: 100%;
    /* faz a caixa de texto ocupar todo o espaço horizontal disponível */
    padding: 5px;
    margin-bottom: 10px;
    /* adiciona espaço abaixo da caixa de texto */
}

/* Customizar checkboxes para serem maiores e remover o ponto da lista */
.filter-options {
    list-style: none;
    /* remove os marcadores de lista */
    padding-left: 0;
    /* remove o padding à esquerda */
}

.filter-options li {
    margin: 5px;
}

.filter-options input[type="checkbox"] {
    transform: scale(1.2);
    /* aumenta o tamanho do checkbox */
    margin-right: 5px;
    /* adiciona espaço à direita do checkbox */
}

/* Ajustes no botão 'Aplicar' */
.filter-actions button {
    background-color: #007bff;
    /* cor de fundo */
    border: none;
    padding: 5px 10px;
    color: white;
    /* cor do texto */
    border-radius: 5px;
    /* bordas arredondadas */
    cursor: pointer;
}

.filter-actions button:hover {
    background-color: #0056b3;
    /* cor de fundo ao passar o mouse */
}

/* Para telas menores, você pode fazer as células da tabela se comportarem como blocos */
@media screen and (max-width: 768px) {

    .admin-table,
    .admin-table thead,
    .admin-table tbody,
    .admin-table th,
    .admin-table td,
    .admin-table tr {
        display: block;
    }

    /* Esconde os cabeçalhos da tabela na visão responsiva */
    .admin-table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    .admin-table tr {
        margin-bottom: 10px;
    }

    .admin-table td {
        /* Cada célula é agora um bloco no estilo "card" */
        position: relative;
        padding-left: 50%;
        /* Ajuste conforme necessário para o padding */
        text-align: left;
    }

    .admin-table td:before {
        /* Adiciona o título da coluna antes de cada célula */
        position: absolute;
        top: 0;
        left: 10px;
        /* Ajuste conforme necessário para o padding */
        width: 45%;
        /* Ajuste conforme necessário para a largura */
        padding-right: 10px;
        /* Ajuste conforme necessário para o padding */
        white-space: nowrap;
        content: attr(data-label);
        /* Usa o valor do atributo data-label como o conteúdo */
        text-overflow: ellipsis;
        overflow: hidden;
    }
}

/* Ajusta as larguras das colunas individuais conforme necessário */
.admin-table .column-id {
    width: 5%;
}

.admin-table .column-perfil {
    width: 10%;
}

.admin-table .column-nome {
    width: 15%;
}

.admin-table .column-data-nasc {
    width: 10%;
}

.admin-table .column-cpf {
    width: 15%;
}

.admin-table .column-email {
    width: 20%;
}

.admin-table .column-email-confirmado {
    width: 10%;
}

#table-container-admin {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    position: relative;
    /* Adiciona o posicionamento relativo para o contêiner da tabela */
}

.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

#paginas {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-numbers {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: #f8f9fa;
    padding: 5px 10px;
    border-radius: 5px;
}

.pagination-numbers a, .pagination-numbers span {
    display: inline-block;
    padding: 5px 10px;
    text-decoration: none;
    border-radius: 3px;
    color: #007bff;
    min-width: 30px;
    text-align: center;
    margin: 0 2px;
}

.pagination-numbers a:hover {
    background-color: #f0f0f0;
}

.pagination-numbers span.current-page {
    background-color: #007bff;
    color: white;
    font-weight: bold;
}

.pagination-ellipsis {
    color: #6c757d;
}


/***************************************************************************/
/** CHANGE_USER.HTML **/
/***************************************************************************/
@media (max-width: 1707.50px) {
    .container-users .btn-voltar {
        margin-left: 93%
    }
}

.change-user {
    font-family: 'Montserrat', sans-serif;
    background: #E8E9EC;
    color: #000;
    padding: 20px;
    height: 100px;
}

.change-user .pn-header {
    height: 7%;
}

.change-user main.content-container {
    flex: 1;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    gap: 0.5rem;
}

.change-user .user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: bold;
}

.change-user .editUser-container {
    background: white;
    border-radius: 10px;
    padding: 50px 0px;
    margin-bottom: 100px
}

.change-user .container-users {
    padding: 2rem;
    margin: 0px 100px;
}

.user-info #user-id {
    font-weight: bold;
}

.user-info #idUser {
    font-weight: normal;
}

.change-user .editUser-container .form-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: bold;
}

.change-user .editUser-container .form-group label {
    flex-basis: 70%;
}

.change-user input[type="text"],
.change-user input[type='password'] {
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
}

.change-user input#editEmail {
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
}

.change-user select#editTipoUsuario {
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
}

.change-user select#editUnidade {
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
}

.change-user select#editEquipe {
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
}

.change-user input#editDataNascimento {
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
}

.change-user .botton-conteiner-users {
    display: flex;
}

.change-user button.btn-excluir {
    padding: 10px 20px;
    color: white;
    background-color: #cd1e1e;
    border-color: black;
    font-weight: bold;
    border-radius: 5px;
    cursor: pointer;
    display: block;
    margin-left: 79%;
    margin-right: 2%;
}

.change-user button.btn-excluir:hover {
    background-color: #9b1d1d;
}

.change-user .input-group-register {
    position: relative;
    width: 100%;
}

.change-user .input-group-register input {
    width: 100%;
    padding-right: 40px;
    /* espaço suficiente para o ícone */
}

.change-user .input-group-register-addon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 22px;
    color: #c1c4ca;
    cursor: pointer;
}

.change-user .form-group .fa-eye,
.change-user .form-group .fa-eye-slash {
    font-size: 22px;
    width: 20px;
    color: #c1c4ca;
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
}


/***************************************************************************/
/** COMISSOES.HTML **/
/***************************************************************************/
.comissoes {
    font-family: 'Montserrat', sans-serif;
    background: #ffffff;
    color: #000;
}

body.comissoes .pn-header {
    height: 10%;
}

body.comissoes .content-container {
    flex: 1;
    padding: 0;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-left: 0;
}

.comissoes-container {
    background: white;
    padding: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    width: 95%;
    margin-top: 100px;
    overflow-x: auto;
    /* Habilita a rolagem horizontal */
}

.comissoes-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: auto;
    /* Permite que as colunas tenham tamanho fixo */
}

.comissoes-table th,
.comissoes-table td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: center;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    word-wrap: break-word;
}

/* Definir tamanhos fixos para colunas específicas */
.comissoes-table th:nth-child(1),
/* Comissionáveis */
.comissoes-table th:nth-child(2),
/* Totais */
.comissoes-table th:nth-child(n+3) {
    /* Parcelas */
    min-width: 200px;
    max-width: 200px;
}

.comissoes-table td:nth-child(1),
/* Comissionáveis */
.comissoes-table td:nth-child(2),
/* Totais */
.comissoes-table td:nth-child(n+3) {
    /* Parcelas */
    min-width: 200px;
    max-width: 200px;
}

.comissoes-table th {
    background-color: #f2f2f2;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 2;
}

.comissoes-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.comissoes-table tr:hover {
    background-color: #ddf;
}

.comissoes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.clickable-row {
    cursor: pointer;
}

.search-comissoes-container {
    margin-bottom: 20px;
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.comissoes .modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
    padding-top: 60px;
}

.comissoes .modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 900px;
    position: relative;
}

.comissoes .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.comissoes .close:hover,
.comissoes .close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.comissoes .comissoes-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.comissoes .comissoes-modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 95%;
    box-shadow: 0px 0px 10px #000;
    border-radius: 8px;
}

.comissoes .comissoes-modal-header {
    background-color: #235397;
    color: white;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px;
}

.comissoes .comissoes-modal-header h2 {
    margin: 0;
}

#closeModalBtn {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    padding: 0;
}

#closeModalBtn:hover {
    color: #ff4d4d;
}

#closeModalBtn i {
    display: inline-block;
    /* Garante que o ícone seja exibido como inline-block */
}


.comissoes .comissoes-modal-body {
    margin-top: 20px;
}

.comissoes .loading-indicator {
    text-align: center;
    font-size: 16px;
    color: #235397;
}

.comissoes .comissoes-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.comissoes .comissoes-table th,
.comissoes .comissoes-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}

.comissoes .comissoes-modal-body {
    max-height: 400px;
    /* Mantém a altura máxima do modal */
    overflow-y: auto;
    /* Habilita a rolagem vertical */
    overflow-x: auto;
    /* Habilita a rolagem horizontal */
    white-space: nowrap;
    /* Previne quebra de linha nas células */
}

#gradeSelect {
    width: 10%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #ffffff;
    color: #333;
    font-size: 16px;
    font-family: 'Montserrat', sans-serif;
    appearance: none;
    cursor: pointer;
}

#gradeSelect:focus {
    outline: none;
    border-color: #235397;
    /* Altera a cor da borda ao focar */
    background-color: #e6e6e6;
    /* Altera a cor do fundo ao focar */
}


.select-container:after {
    font-size: 12px;
    color: #333;
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    pointer-events: none;
}

.select-container select {
    width: 100%;
    padding-right: 30px;
    /* Espaço para a seta personalizada */
}


/***************************************************************************/
/** UPLOAD-TABELA.HTML **/
/***************************************************************************/
.uploadTabela {
    margin: 0.8rem;
    background-color: #fff;
    font-family: 'Montserrat', sans-serif;
}

body.uploadTabela .pn-header {
    height: 10%;
}

.uploadTabela .content-container {
    margin-left: -0.55%;
    border-radius: 0;
    display: flex;
    justify-content: center;
    align-items: stretch;
}

.uploadTabela #footer-box {
    margin-left: -0.6%;
}

.upload-container {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    padding: 20px;
    margin-top: 10%;
}

.upload-h1 {
    text-align: center;
}

.upload-h3 {
    margin-bottom: 50px;
    font-weight: 500;
}

.uploadTabela #upload-form {
    margin: 2rem;
}

.uploadTabela .form-group {
    gap: 2rem;
}


/***************************************************************************/
/** CADASTRO_CORRETOR.HTML **/
/***************************************************************************/
.cadastro-corretor {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #E8E9EC;
    display: flex;
    flex-direction: column;
}

/* Estilos para o formulário de gerenciamento de estudos de saúde */
.form-estudos {
    max-width: 1400px;
    margin: auto;
    background-color: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.form-estudos header h1 {
    color: #0056b3;
    text-align: center;
}

.form-estudos section {
    margin-top: 20px;
}

.form-estudos input[type="text"],
.form-estudos input[type="number"],
.form-estudos input[type="date"],
.form-estudos textarea {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-estudos input[type="submit"] {
    width: 100%;
    padding: 10px;
    background-color: #0056b3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.form-estudos footer p {
    text-align: center;
    color: #333;
}

.scrollable {
    overflow-y: auto;
    /* Permite rolagem vertical se o conteúdo for maior que a altura */
    max-height: 90vh;
    /* Define uma altura máxima para a float box */
}


/***************************************************************************/
/** CRIAR_INFORMATIVO.HTML **/
/***************************************************************************/
.criar-informativo {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0.8rem;
    background-color: #fff;
}


/***************************************************************************/
/** DETALHES_INFORMATIVO.HTML **/
/***************************************************************************/
.detalhes-inform {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0.8rem;
    background-color: #fff;
}

.btn-voltar {
    background-color: #D66833;
    border: #603018;
    color: white;
    text-decoration: none;
}

.btn-voltar:hover {
    background-color: #df8457;
    text-decoration: none;
}


/***************************************************************************/
/** FRANQUEADO.HTML **/
/***************************************************************************/
.area-do-franqueado {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #E8E9EC;
    display: flex;
    flex-direction: column;
}

.area-do-franqueado main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.area-do-franqueado .pn-header {
    height: 10%;
}

.area-do-franqueado .footer-container {
    margin-left: -8px;
}

/*ESTILOS DOS CARDS*/
.area-do-franqueado .card-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin: 20px 0;
    background-color: transparent;
    border: none;
}

/*FORMATO/POSIÇÃO/BORDAS/ANIMAÇÃO DOS CARDS*/
.area-do-franqueado .card {
    flex: 1 1 23%;
    background: #fff;
    border: none;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
    margin: 10px;
    position: relative;
    padding-bottom: 60px;
}

/*EFEITO AO PASSAR O MOUSE NOS CARDS*/
.area-do-franqueado .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/*FORMATO/POSIÇÃO/BORDAS/ANIMAÇÃO DOS CARDS - ADICIONAIS*/
.area-do-franqueado .card-container .card {
    background: linear-gradient(to bottom, #fff, #f9f9f9);
    border: 1px solid #ddd;
}

/*COR DOS ICONES DENTRO DOS CARDS*/
.area-do-franqueado .card-container .card i {
    font-size: 24px;
    color: #007bff;
    margin-bottom: 10px;
}

.area-do-franqueado .card-container .card h3 {
    margin: 10px 0;
    font-size: 24px;
    color: #333;
}

.area-do-franqueado .card-container .card .btn-acessar {
    display: inline-block;
    padding: 10px 20px;
    font-size: 14px;
    color: #fff;
    background-color: #ff6600;
    border: none;
    border-radius: 20px;
    text-decoration: none;
    cursor: pointer;
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
}

.area-do-franqueado .card-container .card .btn-acessar:hover {
    background-color: #cc5200;
}

.area-do-franqueado .processos-container .sidebar,
.area-do-franqueado .sistemas-container .sidebar,
.area-do-franqueado .chamados-container .sidebar,
.area-do-franqueado .documentos-container .sidebar {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/*Cabeçalhos Sidebar */
.area-do-franqueado .help-desk-container .sidebar h3,
.area-do-franqueado .sistemas-container .sidebar h3,
.area-do-franqueado .documentos-container .sidebar h3,
.area-do-franqueado .multinotas-container .sidebar h3,
.area-do-franqueado .processos-container .sidebar h3,
.area-do-franqueado .chamados-container .sidebar h3,
.area-do-franqueado .documentos-container .sidebar h3 {
    color: #007bff;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

/* Adicionando Cores para cabeçalho Sidebar */
.area-do-franqueado .help-desk-container .sidebar h2,
.area-do-franqueado .help-desk-container .main-content h2,
.area-do-franqueado .sistemas-container .sidebar h2,
.area-do-franqueado .sistemas-container .main-content h2,
.area-do-franqueado .propostas-container .sidebar h2,
.area-do-franqueado .propostas-container .main-content h2,
.area-do-franqueado .multinotas-container .sidebar h2,
.area-do-franqueado .multinotas-container .main-content h2,
.area-do-franqueado .processos-container .sidebar h2,
.area-do-franqueado .processos-container .main-content h2,
.area-do-franqueado .chamados-container .sidebar h2,
.area-do-franqueado .chamados-container .main-content h2,
.area-do-franqueado .documentos-container .sidebar h2,
.area-do-franqueado .documentos-container .main-content h2 {
    color: #007bff;
    font-size: 1.5rem;
}

.area-do-franqueado .franqueado-calendar {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.area-do-franqueado .franqueado-calendar h2 {
    margin-bottom: 20px;
}

.area-do-franqueado .franqueado-calendar select {
    margin-bottom: 20px;
}

.area-do-franqueado #franqueado-calendar {
    max-width: 900px;
    margin: 0 auto;
}

.area-do-franqueado .operadora-list {
    max-height: 200px;
    overflow-y: scroll;
    border: 1px solid #ccc;
    padding: 10px;
    margin-bottom: 20px;
    white-space: nowrap;
    width: auto;
}

.area-do-franqueado .operadora-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    width: 100%;
}

.area-do-franqueado .operadora-item input {
    margin-right: 10px;
}

.area-do-franqueado .help-desk-container,
.area-do-franqueado .sistemas-container,
.area-do-franqueado .chamados-container,
.area-do-franqueado .documentos-container,
.area-do-franqueado .multinotas-container,
.area-do-franqueado .processos-container {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    margin-top: 20px;
    width: 100%;
}

.area-do-franqueado .help-desk-container .sidebar,
.area-do-franqueado .sistemas-container .sidebar,
.area-do-franqueado .chamados-container .sidebar,
.area-do-franqueado .documentos-container .sidebar,
.area-do-franqueado .multinotas-container .sidebar,
.area-do-franqueado .processos-container .sidebar {
    width: 250px;
    background: #f4f4f4;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin-right: 20px;
}

.area-do-franqueado .help-desk-container .sidebar ul,
.area-do-franqueado .sistemas-container .sidebar ul,
.area-do-franqueado .chamados-container .sidebar ul,
.area-do-franqueado .documentos-container .sidebar ul,
.area-do-franqueado .multinotas-container .sidebar ul,
.area-do-franqueado .processos-container .sidebar ul {
    list-style-type: none;
    padding: 0;
}

.area-do-franqueado .help-desk-container .sidebar ul li,
.area-do-franqueado .chamados-container .sidebar ul li,
.area-do-franqueado .sistemas-container .sidebar ul li,
.area-do-franqueado .documentos-container .sidebar ul li,
.area-do-franqueado .multinotas-container .sidebar ul li,
.area-do-franqueado .processos-container .sidebar ul li {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #ddd;
}

/* Cor azul ao passar o mouse na sidebar */
.area-do-franqueado .help-desk-container .sidebar ul li:hover,
.area-do-franqueado .sistemas-container .sidebar ul li:hover,
.area-do-franqueado .chamados-container .sidebar ul li:hover,
.area-do-franqueado .documentos-container .sidebar ul li:hover,
.area-do-franqueado .multinotas-container .sidebar ul li:hover,
.area-do-franqueado .processos-container .sidebar ul li:hover {
    background-color: #e0e0e0;
    color: #007bff;
}

/*CONTAINER DE EXIBIÇÃO*/
.area-do-franqueado .help-desk-container .main-content,
.area-do-franqueado .sistemas-container .main-content,
.area-do-franqueado .documentos-container .main-content,
.area-do-franqueado .multinotas-container .main-content,
.area-do-franqueado .chamados-container .main-content,
.area-do-franqueado .processos-container .main-content,
.area-do-franqueado .propostas-container .main-content {
    flex: 1;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background: #fff;
    width: 100%;
}

.content-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
}

.area-do-franqueado .system-container,
.area-do-franqueado .processo-item {
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.area-do-franqueado .system-container:hover,
.area-do-franqueado .processo-item:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.area-do-franqueado .system-header,
.area-do-franqueado .processo-header {
    padding: 15px;
    background-color: #f8f9fa;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.area-do-franqueado .system-header h4,
.area-do-franqueado .processo-header h4 {
    margin: 0;
    font-size: 16px;
    color: #007bff;
    /* Ajustando para azul */
}

.area-do-franqueado .system-header .toggle-arrow,
.area-do-franqueado .processo-header .toggle-arrow {
    font-size: 18px;
    transition: transform 0.2s ease-in-out;
}

.area-do-franqueado .system-header .toggle-arrow.expanded,
.area-do-franqueado .processo-header .toggle-arrow.expanded {
    transform: rotate(90deg);
}

.area-do-franqueado .system-content,
.area-do-franqueado .processo-content {
    padding: 20px;
    background-color: #fff;
    display: none;
    overflow: hidden;
}

.area-do-franqueado .system-content p,
.area-do-franqueado .processo-content ul {
    list-style-type: none;
    padding: 0;
}

.area-do-franqueado .processo-content ul li {
    padding: 5px 0;
}

@media screen and (max-width: 768px) {
    .area-do-franqueado .card-container {
        flex-direction: column;
        align-items: center;
    }

    .area-do-franqueado .help-desk-container,
    .area-do-franqueado .documentos-container,
    .area-do-franqueado .chamados-container,
    .area-do-franqueado .processos-container {
        flex-direction: column;
        align-items: center;
    }

    .area-do-franqueado .help-desk-container .sidebar,
    .area-do-franqueado .documentos-container .sidebar,
    .area-do-franqueado .chamados-container .sidebar,
    .area-do-franqueado .processos-container .sidebar {
        position: fixed;
        width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
        color: #007bff;
    }

    .area-do-franqueado .help-desk-container .main-content,
    .area-do-franqueado .documentos-container .main-content,
    .area-do-franqueado .chamados-container .main-content,
    .area-do-franqueado .processos-container .main-content {
        width: 100%;
    }
}

.step {
    padding: 2rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}


/***************************************************************************/
/** RH.HTML **/
/***************************************************************************/
.rh {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #E8E9EC;
    display: flex;
    flex-direction: column;
}

.rh .pn-header {
    height: 7%;
}

.rh main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.rh .brazil-container {
    margin-top: 5%;
}

#rh-float-box {
    background-color: #fff;
    font-family: 'Montserrat', sans-serif;
    height: auto;
    margin-top: 30px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 5;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 30px;
}

.header-botton-rh {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    background: #fff;
    height: 150px;
    line-height: 94px;
    font-size: 1.2rem;
    gap: 8rem;
    margin-left: -110px;
}

#videoOverlay {
    display: none;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
}

#videoContainer {
    position: relative;
    padding: 20px;
    width: 960px;
    height: 540px;
    max-width: 70%;
    max-height: 70%;
}

#closeButton {
    position: absolute;
    top: 15px;
    right: -15px;
    cursor: pointer;
    font-size: 35px;
    color: #fff;
    border-radius: 50%;
    padding: 5px 10px;
}

/***************************************************************************/
/** FAQ.HTML **/
/***************************************************************************/
.faq {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #E8E9EC;
    display: flex;
    flex-direction: column;
}

.faq .pn-header {
    height: 7%;
}

.faq main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

#faq-float-box {
    background-color: #fff;
    font-family: 'Montserrat', sans-serif;
    height: auto;
    margin-top: 30px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 5;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 30px;
    border-radius: 8px;
    padding: 20px;
    max-height: 110%;
    max-width: 88%;
    overflow-y: auto;
    gap: 20px;
}

/* Estilo para os headers das perguntas */
#faqAccordion .card-header {
    background-color: #f7f7f7;
    border: none;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Adicionando margem entre os cards */
#faqAccordion .card {
    margin-bottom: 5px;
}

/* Estilo para os botões de link */
#faqAccordion .btn-link {
    color: #000;
    text-decoration: none;
    text-align: right;
    padding: 0;
}

#faqAccordion .card-header:hover .btn-link span,
#faqAccordion .card-header:hover .btn-link .fas,
#faqAccordion .btn-link:hover span,
#faqAccordion .btn-link:hover .fas,
#faqAccordion .btn-link:focus span,
#faqAccordion .btn-link:focus .fas {
    color: #D66833;
    text-decoration: none;
}

#faqAccordion .btn-link:focus {
    outline: none;
}

/* Estilo para as setas */
#faqAccordion .fas.fa-chevron-down {
    transition: transform 0.2s;
}

#faqAccordion .collapse.show .fas.fa-chevron-down {
    transform: rotate(180deg);
}


/***************************************************************************/
/** MANUAL_COLAB.HTML **/
/***************************************************************************/
.manual {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #E8E9EC;
    display: flex;
    flex-direction: column;
}

#manual-container {
    background-color: #fff;
    font-family: 'Montserrat', sans-serif;
    height: auto;
    margin-top: 30px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 5;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
    height: 600px;
    max-height: 100%;
    max-width: 100%;
    overflow-y: auto;
    gap: 10px;
}

#manual-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 50px 0;
    /* Ajusta o padding para centralizar melhor */
}

/* Estilo personalizado para o card */
.custom-card {
    width: 50rem;
    /* Define a largura do card */
}

/* Animação do texto "Carregando..." */
@keyframes loading {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

.loading-text {
    color: #D66833;
    /* Define a cor do texto */
}

.loading-text span {
    opacity: 0;
    animation: loading 1s infinite;
}

.loading-text span:nth-child(1) {
    animation-delay: 0s;
}

.loading-text span:nth-child(2) {
    animation-delay: 0.1s;
}

.loading-text span:nth-child(3) {
    animation-delay: 0.2s;
}

.loading-text span:nth-child(4) {
    animation-delay: 0.3s;
}

.loading-text span:nth-child(5) {
    animation-delay: 0.4s;
}

.loading-text span:nth-child(6) {
    animation-delay: 0.5s;
}

.loading-text span:nth-child(7) {
    animation-delay: 0.6s;
}

.loading-text span:nth-child(8) {
    animation-delay: 0.7s;
}

.loading-text span:nth-child(9) {
    animation-delay: 0.8s;
}

.loading-text span:nth-child(10) {
    animation-delay: 0.9s;
}

.loading-text span:nth-child(11) {
    animation-delay: 1s;
}

.loading-text span:nth-child(12) {
    animation-delay: 1.1s;
}

.loading-text span:nth-child(13) {
    animation-delay: 1.2s;
}

/* Media queries para diferentes resoluções de tela */
@media (min-width: 1920px) {
    #manual-container {
        height: 400px;
    }
}

/* Media queries para diferentes resoluções de tela */
@media (min-width: 2400px) {
    #manual-container {
        height: 600px;
    }
}


/***************************************************************************/
/** RANKING.HTML **/
/***************************************************************************/
/* Estilos existentes */
.ranking {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #E8E9EC;
    display: flex;
    flex-direction: column;
}

.ranking .pn-header {
    height: 7%;
}

.ranking main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.ranking .ranking-container {
    width: 90%;
    max-width: 90%;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
}

.ranking #ranking-name {
    display: flex;
    justify-content: center;
    padding: 15px;
}

.ranking #buttons-container {
    text-align: left;
    margin-left: 0px;
}

.ranking .dataTables_wrapper .dt-buttons {
    float: left;
    margin-bottom: 10px;
}

.ranking .ranking-header th {
    background-color: #F2753D;
    color: white;
    text-align: center;
    /* Centraliza o texto nos cabeçalhos */
}

.ranking .ranking-body tr:nth-child(even) {
    background-color: #f9f9f9;
}

.ranking .ranking-body tr:hover {
    background-color: #f1f1f1;
}

.ranking .ranking-footer th {
    background-color: #F2753D;
    color: white;
    font-weight: bold;
    text-align: center;
    /* Centraliza o texto nos rodapés */
}

.ranking tfoot .ranking-column-footer {
    text-align: center;
    /* Centraliza o texto nos rodapés */
}

.ranking td,
.ranking th {
    text-align: center;
    /* Centraliza o texto em todas as colunas */
}

.ranking td:first-child,
.ranking th:first-child {
    text-align: left;
    /* Mantém o texto da coluna "Assistente" alinhado à esquerda */
}

.ranking .ranking-body td {
    background-color: #013977;
    /* Define a cor de fundo das células */
    color: white;
    /* Define a cor do texto como branca */
}

.ranking .ranking-body td {
    background-color: #013977 !important;
}

.ranking .sorted {
    background-color: #013977 !important;
    /* Mantém a cor de fundo ao classificar */
}

.ranking #monthYearPicker {
    z-index: 1050;
    /* Certifique-se de que o z-index está alto o suficiente */
    position: relative;
}

.ranking .btn-group {
    width: 100%;
}


/***************************************************************************/
/** UNIDADES.HTML **/
/***************************************************************************/
/* Estilos existentes */
.unidades {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
}

.unidades .pn-header {
    height: 7%;
}

.unidades main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.card-unidades {
    background: #fff;
    padding: 20px;
    margin: 10px;
    box-shadow: 0 4px 8px rgba(35, 83, 151, 0.807);
    /* box-shadow: 0 4px 8px rgba(0,0,0,0.1); */
    border-radius: 8px;
    text-align: center;
    width: 450px;
}

.card-unidades:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    transition: 0.3s;
}

.card-unidades h2 {
    margin-bottom: 10px;
    color: #235397;
    text-align: center;
    margin-bottom: 2rem;
}

.card-unidades .content-left {
    text-align: left;
}

.card-unidades .content-left strong {
    display: block;
}

.card-unidades .content-left p {
    margin-left: 1rem;
    text-indent: -1rem;
}

.card-unidades p {
    margin: 5px 0;
}

.card-unidades h3 {
    margin: 10px 0;
    color: #666;
}

.card-unidades .telefone-unidade,
.card-unidades .email-unidade,
.card-unidades .endereco-unidade {
    margin: 10px 0;
}

.card-unidades .telefone-unidade p,
.card-unidades .email-unidade p,
.card-unidades .endereco-unidade p {
    margin: 5px 0;
}

.unidades-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    margin-left: 3%;
}

#line-unidades {
    display: flex;
    gap: 2rem;
}

.card-unidades i {
    color: #007bff;
    font-size: 1.25rem;
    margin-right: 8px;
}

div#header-unidades {
    margin-top: 3rem;
}

/* Estilos de responsividade para telas de até 1366px */
@media (max-width: 1366px) {
    .unidades-container {
        padding: 10px;
    }

    .unidades-container .card-unidades {
        padding: 15px;
        margin: 10px 0;
        width: 100%;
    }

    .unidades-container .card-unidades h2 {
        font-size: 1.5rem;
    }

    .unidades-container .card-unidades p {
        font-size: 0.9rem;
    }

    .unidades-container .card-unidades strong {
        font-size: 1rem;
    }

    .unidades-container .content-left {
        text-align: left;
    }

    .unidades-container .socio-unidade,
    .unidades-container .telefone-unidade,
    .unidades-container .email-unidade,
    .unidades-container .endereco-unidade {
        display: flex;
        align-items: center;
        text-align: left;
    }

    .unidades-container .socio-unidade i,
    .unidades-container .telefone-unidade i,
    .unidades-container .email-unidade i,
    .unidades-container .endereco-unidade i {
        margin-right: 5px;
    }

    .unidades-container .socio-unidade strong,
    .unidades-container .telefone-unidade strong,
    .unidades-container .email-unidade strong,
    .unidades-container .endereco-unidade strong {
        margin-right: 5px;
    }
}


/***************************************************************************/
/** GERENCIAIS.HTML **/
/***************************************************************************/
.gerenciais {
    margin: 0.8rem;
    background-color: #fff;
    font-family: 'Montserrat', sans-serif;
}

.gerenciais .pn-header {
    height: 90%;
}

.gerenciais main.content-container {
    padding: 0px;
    margin-left: -12px;
    margin-top: -40%;
}

.gerenciais .footer-container {
    margin-left: -13px;
}

.gerenciais .card-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin: 20px 0;
    background-color: transparent;
    border: none;
}

.gerenciais .card-container .card {
    background: linear-gradient(to bottom, #fff, #f9f9f9);
    border: 1px solid #ddd;
}

.gerenciais .card {
    flex: 1 1 23%;
    background: #fff;
    border: none;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
    margin: 10px;
    position: relative;
    padding-bottom: 60px;
    gap: 1rem
}

.gerenciais .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.gerenciais .card-container .card .btn-acessar {
    display: inline-block;
    padding: 10px 20px;
    font-size: 14px;
    color: #fff;
    background-color: #ff6600;
    border: none;
    border-radius: 20px;
    text-decoration: none;
    cursor: pointer;
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
}

.gerenciais .card-container .card i {
    font-size: 24px;
    color: #007bff;
    margin-bottom: 10px;
}

/***************************************************************************/
/** RAMAIS.HTML **/
/***************************************************************************/
.ramais {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
}

.card-ramais {
    background: #fff;
    padding: 20px;
    margin: 10px;
    box-shadow: 0 4px 8px rgba(35, 83, 151, 0.807);
    border-radius: 8px;
    text-align: center;
    width: 450px;
}

.card-ramais:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    transition: 0.3s;
}

.card-ramais h2 {
    margin-bottom: 10px;
    color: #235397;
    text-align: center;
    margin-bottom: 2rem;
}

.card-ramais .content-left {
    text-align: left;
}

.card-ramais .content-left strong {
    display: block;
}

.card-ramais .content-left p {
    margin-left: 1rem;
    text-indent: -1rem;
}

.card-ramais p {
    margin: 5px 0;
}

.ramais-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

#line-ramais {
    display: flex;
    gap: 2rem;
}

.card-ramais i {
    color: #007bff;
    font-size: 1.25rem;
    margin-right: 8px;
}

div#header-ramais {
    margin-top: 3rem;
}

/* Estilos de responsividade para telas de até 1366px */
@media (max-width: 1366px) {
    .ramais-container {
        padding: 10px;
    }

    .ramais-container .card-ramais {
        padding: 15px;
        margin: 10px 0;
        width: 100%;
    }

    .ramais-container .card-ramais h2 {
        font-size: 1.5rem;
    }

    .ramais-container .card-ramais p {
        font-size: 0.9rem;
    }

    .ramais-container .card-ramais strong {
        font-size: 1rem;
    }

    .ramais-container .content-left {
        text-align: left;
    }

    .ramais-container .ramal-unidade {
        display: flex;
        align-items: center;
        text-align: left;
    }

    .ramais-container .ramal-unidade i {
        margin-right: 5px;
    }

    .ramais-container .ramal-unidade strong {
        margin-right: 5px;
    }
}


/***************************************************************************/
/** SUGESTOES.HTML **/
/***************************************************************************/
.sugestoes {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.sugestoes .pn-header {
    height: 7%;
}

.sugestoes main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.sugestoes #sugestao-float-box {
    background-color: #fff;
    font-family: 'Montserrat', sans-serif;
    margin-top: 30px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 30px 50px;
    max-width: 800px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.sugestoes #sugestao-float-box h2 {
    text-align: center;
    margin-bottom: 20px;
    color: #235397;
}

.sugestoes #sugestao-float-box .form-group {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
}

.sugestoes #sugestao-float-box .form-group-basic {
    margin-bottom: 20px;
    display: flex;
    flex-direction: row;
    gap: 1rem;
    align-items: center;
}

.sugestoes #sugestao-float-box .form-group label {
    font-weight: bold;
    color: #235397;
    margin-bottom: 5px;
}

.sugestoes select#tipo-despesa {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 10px;
    font-size: 16px;
    width: calc(100% - 20px);
}

.sugestoes #sugestao-float-box .form-group input,
.sugestoes #sugestao-float-box .form-group textarea {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 10px;
    font-size: 16px;
    width: calc(100% - 20px);
}

.sugestoes #sugestao-float-box .form-group input::placeholder,
.sugestoes #sugestao-float-box .form-group textarea::placeholder {
    color: #999;
}

.sugestoes #sugestao-float-box .form-group input:focus,
.sugestoes #sugestao-float-box .form-group textarea:focus {
    border-color: #235397;
    outline: none;
}

.sugestoes #sugestao-float-box .btn-primary {
    background-color: #FF6C00;
    border: none;
    color: #fff;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 10px 0;
    border-radius: 4px;
    cursor: pointer;
    align-self: center;
    width: 200px;
}

.sugestoes #sugestao-float-box .btn-primary:hover {
    background-color: #d65a00;
}

.sugestoes .message-box {
    padding: 10px;
    margin-top: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    text-align: center;
}

/***************************************************************************/
/** GESTAO.HTML **/
/***************************************************************************/
.gestao {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 30px;
}

.header-gestao {
    text-align: center;
    margin-bottom: 20px;
}

.gestao .pn-header {
    height: 10%;
}

.header-gestao h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 1rem;
    /* Espaçamento inferior */
}

.gestao-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin: 0 auto;
    max-width: 1200px;
}

body.gestao .content-container {
    box-shadow: none;
}

.gestao .fa-solid.fa-person-circle-exclamation {
    margin-top: 1rem;
}

.cardboard {
    width: calc(33.333% - 20px);
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    padding: 10px 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: transform 0.2s;
    text-decoration: none;
}

.cardboard:hover {
    transform: translateY(-5px);
}

.cardboard-header {
    flex-grow: 10px;
    margin-bottom: 10px;
    /* Reduzir o espaçamento inferior */
}

.cardboard-header i {
    font-size: 2rem;
    color: #007bff;
}

.cardboard-header h3 {
    font-size: 1.5rem;
    margin-top: 5px;
    /* Reduzir o espaçamento superior */
    color: #333;
    margin-bottom: 5px;
    /* Reduzir o espaçamento inferior */
}

.cardboard-content p {
    font-size: 1rem;
    color: #666;
    margin-top: 5px;
    /* Reduzir o espaçamento superior */
    margin-bottom: 5px;
    /* Reduzir o espaçamento inferior */
}

.cardboard-footer {
    margin-top: 10px;
    /* Reduzir o espaçamento superior */
}

.gestao-btn-acessar {
    padding: 10px 20px;
    background-color: #ff6600;
    color: #fff;
    border: none;
    border-radius: 5px;
    text-transform: uppercase;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
    text-decoration: none;
    /* Remove underline */
    display: inline-block;
    /* Ensure proper button styling */
}

.gestao-btn-acessar:hover {
    background-color: #cc5200;
}

gestao.content-container {
    margin-top: 30px;
    text-align: center;
}

.gestao .modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0, 0, 0);
    background-color: rgba(0, 0, 0, 0.5);
    padding-top: 60px;
}

.gestao .modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80% !important;
    max-width: 950px;
    position: relative;
}

.gestao .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.gestao .close:hover,
.gestao .close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.gestao #modal-body {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    gap: 1rem;
}

.input-portal {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.gestao #portalId,
#modalidade {
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
    margin-left: 0;
}

.portal-link {
    white-space: normal;
    /* Permite quebra de linha */
    word-wrap: break-word;
    /* Força a quebra de palavras longas */
    width: 100%;
    /* Define a largura para 100% do contêiner */
    max-width: 500px;
    padding: 8px;
    /* Adiciona um espaçamento interno para parecer com um input */
    border: 1px solid #ccc;
    /* Adiciona uma borda semelhante ao input */
    border-radius: 4px;
    /* Arredonda os cantos como em um input */
    background-color: #f9f9f9;
    /* Fundo mais claro para parecer com um campo de texto */
    box-sizing: border-box;
    /* Garante que o padding e a borda sejam incluídos na largura total */
    overflow-wrap: break-word;
    /* Garante que as palavras sejam quebradas quando necessário */
}

#tabelas {
    width: 40%;
}

.comissoes .ranking-header th {
    position: relative;
}

.comissoes .ranking-body td {
    border-right: 1px solid #fff;
}

.comissoes .ranking-body td:last-child {
    border-right: none;
}

.comissoes input[type="search"] {
    width: 400px;
    padding: 6px 10px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    /* Borda cinza clara */
    border-radius: 4px;
    /* Bordas arredondadas */
    box-shadow: none;
    /* Remove sombra */
    outline: none;
    /* Remove o contorno */
    transition: border-color 0.3s, box-shadow 0.3s;
    /* Transição suave para foco */
    margin-left: 1rem;
}

.comissoes input[type="search"]:focus {
    border-color: #007bff;
    /* Azul claro no foco */
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
    /* Sombra azul clara */
}

.comissoes #filter-operadora,
.comissoes #filter-modalidade {
    padding: 10px;
    width: 50%;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: width 0.4s ease-in-out;
}

.comissoes #filter-operadora:focus {
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

.comissoes #filter-modalidade:focus {
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

.comissoes .filter-container {
    display: flex;
    align-items: center;
}

.comissoes .filter-container {
    display: flex;
    justify-content: space-evenly;
    align-items: flex-end;
}

.comissoes .filter-checkbox {
    margin-left: 10px;
}

.comissoes .filter-dropdown {
    position: absolute;
    background-color: white;
    border: 1px solid #ccc;
    padding: 10px;
    z-index: 1000;
    top: 100%;
    right: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.comissoes .filter-dropdown label {
    display: block;
    margin-bottom: 5px;
}

/* Texto explictivo em Gestao.html */
.gestao div p.paragrafogestao {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

/***************************************************************************/
/** GESTAO-REEMBOLSOS.HTML **/
/***************************************************************************/
.gestaoReembolso {
    margin: 0.8rem;
    background-color: #fff;
    font-family: "Inter Tight", Sans-serif;
}

.reembolso-colaborador main.content-container {
    padding: 0px;
    margin-left: -4px;
}

.colaborador #sugestao-float-box {
    margin-top: 0;
    box-shadow: none;
    border-radius: 0;
    padding: 30px 50px;
    max-width: none;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.gestaoReembolso .pn-header {
    height: 10%;
}


.gestaoReembolso .pn-header {
    height: 10%;
}

.gestaoReembolso main.content-container {
    padding: 0px;
    border-radius: 0;
    width: 95%;
    box-shadow: none;
}

.gestaoReembolso #footer-box {
    margin-left: -0.6%;
}

.gestaoReembolso .board {
    display: flex;
    gap: 20px;
    flex-direction: column;
    align-items: center;
}

.gestaoReembolso .limiter-board {
    display: flex;
    flex-direction: row;
    margin: 2% 0;
    justify-content: space-between;
    gap: 7rem;
}

.gestaoReembolso .column {
    background-color: #1B80F3;
    padding: 10px;
    width: 70%;
    min-width: 35%;
    min-height: 400px;
    max-height: 800px;
    overflow-y: auto;
    transition: box-shadow 0.3s ease-in-out;
    border-radius: 20px 20px 20px 20px;
    margin-left: -4%;
}

.gestaoReembolso .column h2 {
    text-align: center;
    margin-top: 0;
    color: #fff;
    margin-bottom: 1.3rem;
}

.gestaoReembolso .card {
    background-color: #fff;
    border-radius: 20px 20px 20px 20px;
    padding: 15px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: transform 0.2s ease-in-out, box-shadow 0.3s ease-in-out;
    margin: 0 10px 10px 10px;
}

.gestaoReembolso .card h3 {
    margin-top: 0;
    font-size: x-large;
    margin-bottom: 1rem;
}

.gestaoReembolso .view-details {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #FF6600;
    color: white;
    border: none;
    border-radius: 14px 14px 14px 14px;
    cursor: pointer;
    font-family: "Inter Tight", Sans-serif;
    font-size: 20px;
    font-weight: 600;
    text-transform: none;
}

.gestaoReembolso .modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.gestaoReembolso .modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    width: 500px;
    max-width: 90%;
}

.gestaoReembolso .close-button {
    float: right;
    font-size: 20px;
    cursor: pointer;
}

.gestaoReembolso .approve-btn,
.gestaoReembolso .reject-btn {
    margin-top: 10px;
    padding: 10px 15px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    color: white;
}

.gestaoReembolso .approve-btn {
    background-color: #28a745;
}

.gestaoReembolso .reject-btn {
    background-color: #dc3545;
}

/* Estilização aplicada ao arrastar um card */
.gestaoReembolso .card.dragging {
    transform: rotate(5deg);
    box-shadow: 1px 1px 20px #ff6600;
    opacity: 0.8;
    cursor: grabbing;
}

/* Estilização aplicada ao box sobre o qual o card está sendo arrastado */
.gestaoReembolso .column.drag-over {
    box-shadow: 1px 1px 20px #ff6600;
}


.gestaoReembolso .modal-content p {
    color: #000000;
}

.gestaoReembolso p {
    font-weight: 500;
}


/***************************************************************************/
/** Documentos.CSS ** Documentos_empresas.html **/
/***************************************************************************/

.documentos-container {
    padding: 20px;
}

#header-documentos {
    text-align: center;
    margin-bottom: 20px;
}

#header-documentos h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 1rem;
    /* Espaçamento inferior */
}

#select-empresa {
    text-align: center;
    margin-bottom: 20px;
}

#select-empresa h2 {
    font-size: 2rem;
    color: #0056b3;
    margin-bottom: 20px;
}

.empresa-lista {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

.empresa-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    margin: 5px 0;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.2s;
}

.empresa-btn:hover {
    background-color: #0056b3;
}

#line-documentos {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin: 0 auto;
    max-width: 1200px;
}

.card-documentos {
    width: calc(33.333% - 20px);
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: transform 0.2s;
    text-decoration: none;
}

.card-documentos:hover {
    transform: translateY(-5px);
}

.card-documentos h2 {
    font-size: 1.5rem;
    margin-top: 10px;
    color: #007bff;
    margin-bottom: 10px;
}

.content-documentos {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.dropdown-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: block;
    margin: 5px 0;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.2s;
}

.dropdown-btn:hover {
    background-color: #0056b3;
}

/***************************************************************************/
/** Documentos.CSS ** Render_documentos.html **/
/***************************************************************************/
body,
html {
    height: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
}

.brazil {
    font-family: 'Montserrat', sans-serif;
    background-color: #e9ebec;
    display: flex;
    flex-direction: column;
}

.layout-container {
    display: flex;
    height: 100%;
}

/* Estilos da Sidebar Colaborador */
.sidebar {
    width: 250px;
    background: #f9f9f9;
    color: #333;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    height: 100%;
}

.sidebar h3 {
    color: #007bff;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.empresa-lista {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.empresa-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.2s;
}

.empresa-btn:hover {
    background-color: #0056b3;
}

/* Estilos do Container de Conteúdo */
.content-container {
    flex: 1;
    padding: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-left: 20px;
    /* Espaçamento entre sidebar e content container */
}

.documentos-container {
    padding: 20px;
}

.documentos-container-gestao {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    overflow: hidden;
}

#header-documentos {
    text-align: center;
    margin-bottom: 20px;
}

#header-documentos h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 1rem;
    /* Espaçamento inferior */
}

#select-empresa {
    text-align: center;
    margin-bottom: 20px;
}

#select-empresa h2 {
    font-size: 2rem;
    color: #0056b3;
    margin-bottom: 20px;
}

/* Estilos da Lista de Documentos */
.documentos-lista {
    margin-top: 20px;
}

.documentos-lista h2 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 10px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

.documentos-lista ul {
    list-style-type: none;
    padding-left: 0;
    margin: 10px 0 20px 0;
}

.documentos-lista li {
    font-size: 1rem;
    color: #666;
    padding: 8px 0;
    border-bottom: 1px solid #e1e1e1;
}

.documentos-lista li:last-child {
    border-bottom: none;
}

.documentos-lista li a {
    text-decoration: none;
    color: #007bff;
    display: block;
    padding: 8px 15px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.documentos-lista li a:hover {
    text-decoration: none;
    background-color: #f0f0f0;
}

/* Adicionando Estilos de Cabeçalhos e Texto */
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
    padding: 0;
    font-weight: 600;
}

p {
    margin: 0;
    padding: 0;
    color: #666;
    line-height: 1.5;
}

/* Estilos para a tabela de comissões */
.tabela-comissoes-container {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 10px;
}

.tabela-bloco {
    margin-bottom: 40px;
}

.table {
    width: 100%;
    margin-bottom: 20px;
    border-collapse: collapse;
    font-size: 14px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.table th,
.table td {
    padding: 10px;
    text-align: center;
    border: 1px solid #ddd;
    vertical-align: middle;
}

.table th {
    background-color: #007bff;
    color: #ffffff;
    font-weight: bold;
    border-bottom: 2px solid #0056b3;
}

.table tbody tr:nth-of-type(odd) {
    background-color: #f2f2f2;
}

.table tbody tr:hover {
    background-color: #e9ecef;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

.table-bordered thead th,
.table-bordered thead td {
    border-bottom-width: 2px;
}

.highlight {
    background-color: #cce5ff;
}

.highlight-total {
    background-color: #d4edda;
    font-weight: bold;
}

.highlight-corretor {
    background-color: #fff3cd;
}

/* Estilos de Politicas de Beneficios RH */
.beneficios-container {
    max-width: 1200px;
    margin: 40px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.beneficios-header {
    text-align: center;
    color: #333;
    margin-bottom: 40px;
    font-size: 2.5em;
    font-weight: 600;
}

.beneficios-section-title,
.premium-section-title {
    display: inline-block;
    background-color: #0056b3;
    color: #fff;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 1.8em;
    margin-top: 40px;
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.beneficios-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: space-between;
    /* Adicionado para espaçar os cartões */
}

.beneficios-card {
    flex: 1 1 calc(33.33% - 20px);
    /* Ajustado para que três cartões caibam lado a lado */
    background-color: #fdfdfd;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
    box-sizing: border-box;
    /* Para garantir que o padding e o border não aumentem o tamanho do card */
    margin-bottom: 20px;
    /* Espaçamento inferior entre linhas de cartões */
}

.beneficios-card:hover {
    transform: translateY(-10px);
}

.beneficios-card-icon {
    text-align: center;
    padding: 20px;
    background-color: #0056b3;
    color: #fff;
}

.beneficios-card-content {
    padding: 20px;
    text-align: left;
    /* Alinhamento à esquerda */
}

.beneficios-card-title {
    color: #333;
    font-size: 1.4em;
    margin-bottom: 10px;
}

.beneficios-card-description {
    color: #666;
    line-height: 1.5;
}

.beneficios-calculo-title {
    margin-top: 20px;
    font-size: 1.6em;
    color: #0056b3;
}

.beneficios-calculo-form {
    margin-top: 20px;
}

.beneficios-calculo-form-group {
    margin-bottom: 15px;
}

.beneficios-calculo-form-group label {
    font-weight: bold;
}

.beneficios-calculo-input {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.beneficios-calculo-buttons {
    display: flex;
    justify-content: space-between;
}

.beneficios-calculo-btn {
    background-color: #0056b3;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
}

.beneficios-calculo-btn:hover {
    background-color: #004494;
}

.beneficios-calculo-btn-reset {
    background-color: #0056b3;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
}

.beneficios-calculo-btn-reset:hover {
    background-color: #004494;
}

.beneficios-calculo-resultado {
    margin-top: 20px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.beneficios-calculo-resultado h4 {
    margin-bottom: 15px;
}

.beneficios-calculo-resultado p {
    margin-bottom: 10px;
    font-size: 18px;
}

.operadora-icon {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.operadora-icon img {
    width: 30px;
    height: auto;
    margin-right: 10px;
}

.coparticipation-table {
    width: 100%;
    margin-top: 10px;
    border-collapse: collapse;
}

.coparticipation-table th,
.coparticipation-table td {
    border: 1px solid #ccc;
    padding: 10px;
    text-align: left;
}

.coparticipation-table th {
    background-color: #0056b3;
    color: #fff;
}

.beneficios-card-content img {
    max-width: 30px;
    height: auto;
    margin-bottom: 10px;
}

.toggle-button {
    background-color: #0056b3;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    margin-top: 10px;
}

.toggle-button:hover {
    background-color: #004494;
}

/***************************************************************************/
/** GESTAO_RAMAIS.HTML **/
/***************************************************************************/
.gestao-ramais {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
}

.header-gestao-ramais {
    margin-top: 3rem;
    text-align: center;
    color: #235397;
}

.gestao-ramais-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
}

.card-ramais {
    background: #fff;
    padding: 20px;
    margin: 10px;
    box-shadow: 0 4px 8px rgba(35, 83, 151, 0.807);
    border-radius: 8px;
    text-align: center;
    width: 450px;
    cursor: pointer;
}

.card-ramais:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    transition: 0.3s;
}

.card-ramais h2 {
    margin-bottom: 10px;
    color: #235397;
    text-align: center;
    margin-bottom: 2rem;
}

.popup {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0, 0, 0);
    background-color: rgba(0, 0, 0, 0.4);
}

.popup-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    border-radius: 10px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.gestao-ramais-table {
    width: 100%;
    border-collapse: collapse;
}

.gestao-ramais-table th,
.gestao-ramais-table td {
    border: 1px solid #ccc;
    padding: 10px;
    text-align: left;
}

.gestao-ramais-table th {
    background-color: #235397;
    color: #fff;
}

/* Estilos de responsividade para telas de até 1366px */
@media (max-width: 1366px) {
    .gestao-ramais-container {
        padding: 10px;
    }

    .gestao-ramais-container .card-ramais {
        padding: 15px;
        margin: 10px 0;
        width: 100%;
    }

    .gestao-ramais-container .card-ramais h2 {
        font-size: 1.5rem;
    }

    .gestao-ramais-container .card-ramais p {
        font-size: 0.9rem;
    }

    .gestao-ramais-container .card-ramais strong {
        font-size: 1rem;
    }

    .gestao-ramais-container .dropdown-content {
        padding: 10px;
    }

    .gestao-ramais-container .gestao-ramais-table th,
    .gestao-ramais-container .gestao-ramais-table td {
        font-size: 0.9rem;
    }
}

/***************************************************************************/
/** GESTAO_INTEGRACOES.HTML **/
/***************************************************************************/
.gestao-integracoes {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
}

.header-gestao-integracoes {
    margin-top: 3rem;
    text-align: center;
    color: #235397;
}

.gestao-integracoes-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
}

.card-integracoes {
    background: #fff;
    padding: 20px;
    margin: 10px;
    box-shadow: 0 4px 8px rgba(35, 83, 151, 0.807);
    border-radius: 8px;
    text-align: center;
    width: 450px;
    cursor: pointer;
}

.card-integracoes:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    transition: 0.3s;
}

.card-integracoes h2 {
    margin-bottom: 10px;
    color: #235397;
    text-align: center;
    margin-bottom: 2rem;
}

/* Estilos de responsividade para telas de até 1366px */
@media (max-width: 1366px) {
    .gestao-integracoes-container {
        padding: 10px;
    }

    .gestao-integracoes-container .card-integracoes {
        padding: 15px;
        margin: 10px 0;
        width: 100%;
    }

    .gestao-integracoes-container .card-integracoes h2 {
        font-size: 1.5rem;
    }

    .gestao-integracoes-container .card-integracoes p {
        font-size: 0.9rem;
    }
}


/***************************************************************************/
/** ACESSOS.HTML **/
/***************************************************************************/
.acessos {
    font-family: 'Montserrat', sans-serif;
    background: #ffffff;
    color: #000;
}

body.acessos .pn-header {
    height: 10%;
}

body.acessos .content-container {
    flex: 1;
    padding: 0;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-left: 0;
}

.acessos .modal-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
    width: 80%;
    /* Tente definir a largura */
    max-width: 1000px;
    /* Defina um valor máximo */
    margin: auto;
    text-align: left;
    margin-top: 8%;
}

.acessos-container {
    background: white;
    padding: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    width: 100%;
    margin-top: 100px;
}

.acessos-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.acessos-table th,
.acessos-table td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: left;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    min-width: 120px;
}

.acessos i.bi.bi-filter {
    color: black;
}

.acessos-table th {
    background-color: #f2f2f2;
}

.acessos-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.acessos-table tr:hover {
    background-color: #ddf;
}

.acessos-table td[contenteditable="true"] {
    padding: 5px;
    border: 1px dashed #235397;
}

.acessos-table th#nome-col {
    width: 10%;
}

.acessos-table th#link-col {
    width: 24%;
}

.acessos-table th#login-col {
    width: 16.7%;
}

.acessos-table th#senha-col {
    width: 10%;
}

.acessos-table th#unidade-col {
    width: 10.5%;
}

.acessos-table th#codigo-col {
    width: 11%;
}

.acessos-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.clickable-row {
    cursor: pointer;
}

.search-acessos-container {
    margin-bottom: 20px;
    /* Espaço abaixo do campo de busca */
    text-align: left;
    /* Centraliza o campo de busca */
    display: flex;
    justify-content: space-between;
    /* Alinha o campo de busca à esquerda e o botão à direita */
    align-items: center;
    /* Centraliza verticalmente */
    gap: 10px;
    /* Espaçamento entre o campo de busca e o botão */
}

/* Para telas menores, você pode fazer as células da tabela se comportarem como blocos */
@media screen and (max-width: 768px) {

    .acessos-table,
    .acessos-table thead,
    .acessos-table tbody,
    .acessos-table th,
    .acessos-table td,
    .acessos-table tr {
        display: block;
    }

    /* Esconde os cabeçalhos da tabela na visão responsiva */
    .acessos-table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    .acessos-table tr {
        margin-bottom: 10px;
    }

    .acessos-table td {
        /* Cada célula é agora um bloco no estilo "card" */
        position: relative;
        padding-left: 50%;
        /* Ajuste conforme necessário para o padding */
        text-align: left;
    }

    .acessos-table td:before {
        /* Adiciona o título da coluna antes de cada célula */
        position: absolute;
        top: 0;
        left: 10px;
        /* Ajuste conforme necessário para o padding */
        width: 45%;
        /* Ajuste conforme necessário para a largura */
        padding-right: 10px;
        /* Ajuste conforme necessário para o padding */
        white-space: nowrap;
        content: attr(data-label);
        /* Usa o valor do atributo data-label como o conteúdo */
        text-overflow: ellipsis;
        overflow: hidden;
    }
}

/* Ajusta as larguras das colunas individuais conforme necessário */
.acessos-table .column-nome {
    width: 5%;
}

.acessos-table .column-link {
    width: 10%;
}

.acessos-table .column-login {
    width: 15%;
}

.acessos-table .column-senha {
    width: 10%;
}

.acessos-table .column-unidade {
    width: 15%;
}

.acessos-table .column-codigo {
    width: 15%;
}

a.link-acessos {
    text-decoration: none;
}

body.acessos .fa-copy,
body.acessos .fa-pen-to-square,
body.acessos .fa-eye,
body.acessos .fa-eye-slash {
    cursor: pointer;
    margin-left: 5px;
    color: #383b3f;
}


/***************************************************************************/
/** ACESSOS_COLABORADOR.HTML **/
/***************************************************************************/
.acessos-colaborador-container .table th,
.acessos-colaborador-container .table td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: left;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    min-width: 120px;
    vertical-align: middle;
}

body.colaborador #mainContent {
    max-width: 100%;
    overflow-x: auto;
    /* Adiciona barra de rolagem horizontal quando necessário */
    padding: 20px;
    box-sizing: border-box;
}

body.colaborador .acessos-colaborador-table {
    width: 100%;
    table-layout: fixed;
    /* Permite que a tabela expanda conforme o conteúdo */
    white-space: nowrap;
    /* Evita que o conteúdo das células quebre em múltiplas linhas */
}

body.colaborador .container-fluid {
    height: 100%;
}

body.colaborador main {
    overflow-y: clip;
}


.acessos-colaborador-table th#nome-col,
.acessos-colaborador-table .column-nome {
    width: 10%;
}

.acessos-colaborador-table th#link-col,
.acessos-colaborador-table .column-link {
    width: 30%;
}

.acessos-colaborador-table th#login-col,
.acessos-colaborador-table .column-login {
    width: 16.7%;
}

.acessos-colaborador-table th#senha-col,
.acessos-colaborador-table .column-senha {
    width: 10.7%;
}

.acessos-colaborador-table th#unidade-col,
.acessos-colaborador-table .column-unidade {
    width: 12.5%;
}

.acessos-colaborador-table th#codigo-col,
.acessos-colaborador-table .column-codigo {
    width: 11%;
}

.acessos-colaborador-table th#modalidade-col,
.acessos-colaborador-table .column-modalidade {
    width: 11%;
}

.acessos-colaborador-table th#outros-col,
.acessos-colaborador-table .column-outros {
    width: 11%;
}

.table-container-acessos {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.acessos i.bi.bi-filter {
    color: black;
}

.table-container-acessos th {
    background-color: #f2f2f2;
}

.table-container-acessos tr:nth-child(even) {
    background-color: #f9f9f9;
}

.table-container-acessos tr:hover {
    background-color: #ddf;
}

.table-container-acessos td[contenteditable="true"] {
    padding: 5px;
    border: 1px dashed #235397;
}


/***************************************************************************/
/** CHAT.HTML **/
/***************************************************************************/
.chatWrapper {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
    gap: 20px;
    width: 80%;
}

.chatContainer {
    background-color: #ffffff;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    width: 60%;
    max-width: 800px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.messages {
    flex-grow: 1;
    border: 1px solid #ddd;
    border-radius: 10px;
    height: 400px;
    overflow-y: auto;
    padding: 15px;
    background-color: #f9f9f9;
}

.message {
    margin-bottom: 15px;
    padding: 10px 15px;
    border-radius: 10px;
    max-width: 70%;
    font-size: 14px;
    line-height: 1.5;
}

.message.you {
    background-color: #007bff;
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 0;
}

.message.them {
    background-color: #e1e1e1;
    color: #333;
    align-self: flex-start;
    border-bottom-left-radius: 0;
}

.messageInputContainer {
    display: flex;
    align-items: center;
    gap: 10px;
    border-top: 1px solid #ddd;
    padding-top: 10px;
}

.messageInput {
    flex-grow: 1;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 10px;
    font-size: 14px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sendButton {
    padding: 12px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.sendButton:hover {
    background-color: #0056b3;
}

.userListContainer {
    background-color: #ffffff;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    width: 30%;
    max-width: 300px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.userListContainer h3 {
    margin-top: 0;
    font-size: 18px;
    color: #333;
}

.dropdownContainer {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.dropdownContainer select {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 10px;
    font-size: 14px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: border-color 0.3s ease;
}

.dropdownContainer select:focus {
    border-color: #007bff;
    outline: none;
}

.userList {
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 15px;
    height: 150px;
    overflow-y: auto;
    background-color: #f9f9f9;
}

body.colaborador .pn-header {
    height: 10%;
}

.colaborador main.content-container {
    padding: 0px;
    margin-left: -5px;
}


/***************************************************************************/
/** FASTMONEY.HTML **/
/***************************************************************************/
.fastmoney-page .form-group {
    display: block !important;
    width: 100%;
}

.fastmoney-page .pn-header {
    height: 15%;
}

.fastmoney-page .container {
    max-width: 3000px;
}

/* Cabeçalho */
.fastmoney-page .h1 {
    font-size: 24px;
    color: #333;
    text-align: center;
    margin-bottom: 20px;
}

/* Formulário */
.fastmoney-page form {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
}

/* Grupos de Formulário */
.fastmoney-page .form-group-basic {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 20px;
    /* Adiciona espaço entre os itens */
}

.fastmoney-page .form-group {
    display: flex;
    flex-direction: column;
    width: calc(50% - 10px);
    /* Ajusta a largura para dividir em duas colunas */
}

@media (max-width: 600px) {
    .fastmoney-page .form-group {
        width: 100%;
        /* Nos dispositivos menores, o campo ocupa 100% da largura */
    }
}

.fastmoney-page label {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
}

.fastmoney-page input[type="text"],
.fastmoney-page input[type="number"],
.fastmoney-page input[type="date"],
.fastmoney-page input[type="file"] {
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
}

.gestao #portalId,
#modalidade {
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
    margin-left: 0;
}

.portal-link {
    white-space: normal;
    /* Permite quebra de linha */
    word-wrap: break-word;
    /* Força a quebra de palavras longas */
    width: 100%;
    /* Define a largura para 100% do contêiner */
    max-width: 500px;
    padding: 8px;
    /* Adiciona um espaçamento interno para parecer com um input */
    border: 1px solid #ccc;
    /* Adiciona uma borda semelhante ao input */
    border-radius: 4px;
    /* Arredonda os cantos como em um input */
    background-color: #f9f9f9;
    /* Fundo mais claro para parecer com um campo de texto */
    box-sizing: border-box;
    /* Garante que o padding e a borda sejam incluídos na largura total */
    overflow-wrap: break-word;
    /* Garante que as palavras sejam quebradas quando necessário */
}

input[type="radio"] {
    margin-right: 10px;
}

/* Estilo para Radio Buttons */
input[type="radio"]+label {
    font-size: 14px;
    color: #333;
}

/* Estilo para Radio Buttons do Comprovante */
div p {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

label[for="comprovante_sim"],
label[for="comprovante_nao"] {
    display: inline-block;
    margin-right: 20px;
    font-size: 14px;
    color: #333;
}

input[type="radio"]+label {
    margin-left: 5px;
}

/* Campo de Upload de Comprovante */
.form-group label[for="comprovante"] {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    display: block;
}

input[type="file"] {
    display: block;
    width: 100%;
    box-sizing: border-box;
}

/* Botão de Envio */
.btn-send {
    background-color: #ff5722;
    color: white;
    padding: 10px 15px;
    font-size: 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
    box-sizing: border-box;
    margin-top: 20px;
}

.btn-send:hover {
    background-color: #e64a19;
}

/* Alert Box */
#alertBox {
    margin: 20px 0;
    padding: 15px;
    border-radius: 4px;
    display: none;
    color: white;
    text-align: center;
    transition: opacity 0.5s ease;
}

/* Estilo para alerta de sucesso */
.alert-success {
    background-color: #4CAF50;
}

/* Estilo para alerta de erro */
.alert-danger {
    background-color: #f44336;
}

/* Estilo para alerta de aviso */
.alert-warning {
    background-color: #ff9800;
}

/* Estilo para alerta informativo */
.alert-info {
    background-color: #2196F3;
}

/* Estilo para campos readonly */
input[readonly] {
    background-color: #f0f0f0;
    /* Fundo acinzentado */
    color: #888888;
    /* Cor do texto acinzentada */
    cursor: not-allowed;
    /* Cursor que indica campo desabilitado */
    border-color: #ddd;
    /* Borda mais clara */
}

/***************************************************************************/
/** GESTAO-FASTMONEY.HTML **/
/***************************************************************************/
.gestaoFastMoney {
    margin: 0.8rem;
    background-color: #f9f9f9;
    font-family: "Inter Tight", Sans-serif;
}

.gestaoFastMoney .pn-header {
    height: 10%;
}

.gestaoFastMoney main.content-container {
    padding: 0px;
    border-radius: 0;
    width: 95%;
    box-shadow: none;
}

.gestaoFastMoney #footer-box {
    margin-left: -0.6%;
}

.gestaoFastMoney .board {
    display: flex;
    gap: 20px;
    flex-direction: column;
    align-items: center;
}

.gestaoFastMoney .limiter-board {
    display: flex;
    flex-direction: row;
    margin: 2% 0;
    justify-content: space-between;
    gap: 7rem;
}

.gestaoFastMoney .column {
    background-color: #1B80F3;
    padding: 10px;
    width: 70%;
    min-width: 35%;
    min-height: 400px;
    max-height: 800px;
    overflow-y: auto;
    transition: box-shadow 0.3s ease-in-out;
    border-radius: 20px 20px 20px 20px;
    margin-left: -4%;
}

.gestaoFastMoney .column h2 {
    text-align: center;
    margin-top: 0;
    color: #fff;
    margin-bottom: 1.3rem;
}

.gestaoFastMoney .card {
    background-color: #fff;
    border-radius: 20px 20px 20px 20px;
    padding: 15px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: transform 0.2s ease-in-out, box-shadow 0.3s ease-in-out;
    margin: 0 10px 10px 10px;
}

.gestaoFastMoney .card h3 {
    margin-top: 0;
}

.gestaoFastMoney .modal-content p {
    color: #000000;
}

.gestaoFastMoney .view-details {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #FF6600;
    color: white;
    border: none;
    border-radius: 14px 14px 14px 14px;
    cursor: pointer;
    font-family: "Inter Tight", Sans-serif;
    font-size: 20px;
    font-weight: 600;
    text-transform: none;
}

.gestaoFastMoney .modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.gestaoFastMoney .modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    width: 500px;
    max-width: 90%;
}

.gestaoFastMoney .close-button {
    float: right;
    font-size: 20px;
    cursor: pointer;
}

.gestaoFastMoney .approve-btn,
.gestaoFastMoney .reject-btn {
    margin-top: 10px;
    padding: 10px 15px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    color: white;
}

.gestaoFastMoney .approve-btn {
    background-color: #28a745;
}

.gestaoFastMoney .reject-btn {
    background-color: #dc3545;
}

/* Estilização aplicada ao arrastar um card */
.gestaoFastMoney .card.dragging {
    transform: rotate(5deg);
    box-shadow: 1px 1px 20px #ff6600;
    opacity: 0.8;
    cursor: grabbing;
}

/* Estilização aplicada ao box sobre o qual o card está sendo arrastado */
.gestaoFastMoney .column.drag-over {
    box-shadow: 1px 1px 20px #ff6600;
}

input#dateRangePicker {
    text-align: center;
    max-width: 50%;
    background: #fff;
    color: black;
    margin-left: 25%;
    font-size: 1.3rem;
    margin-top: 1%;
}

input#dateRangePicker:focus {
    background: #f0f0f0;
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

/************************************/
/** GESTAO-CORRETORES.CSS **/
/************************************/
.gestaoCorretores {
    font-family: 'Montserrat', sans-serif;
    background-color: #E8E9EC;
    margin: 0;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.gestaoCorretores .pn-header {
    height: 10%;
}

.gestaoCorretores main.content-container {
    padding: 3px;
    margin-left: -0.5px;
    border-radius: 0;
}

.dashboard {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
}

.kpi {
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    text-align: center;
    width: 30%;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.kpi h3 {
    color: #013977;
    margin-bottom: 10px;
}

.kpi p {
    font-size: 24px;
    font-weight: bold;
    color: #F2753D;
}

.corretores-table {
    width: 100%;
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.gestaoCorretores table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.gestaoCorretores th,
.gestaoCorretores td {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #F2753D;
}

.gestaoCorretores th {
    background-color: #F2753D;
    color: white;
}

.gestaoCorretores tr:nth-child(even) {
    background-color: #f9f9f9;
}

.gestaoCorretores tr:hover {
    background-color: #f1f1f1;
}

button {
    background-color: #013977;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

.gestaoCorretores button:hover {
    background-color: #F2753D;
}

.gestaoCorretores .modal-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
    width: 80%;
    /* Tente definir a largura */
    max-width: 1000px;
    /* Defina um valor máximo */
    margin: 0 auto;
    text-align: left;
    display: block;
    /* Garante que o modal ocupe o espaço */
}

.modal {
    display: flex;
    /* Alinhamento flexível */
    justify-content: center;
    /* Centraliza horizontalmente */
    align-items: center;
    /* Centraliza verticalmente */
    height: 100vh;
    /* Garante que o modal ocupe a altura da tela */
}

.close-button {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close-button:hover,
.close-button:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.approve-btn,
.reject-btn {
    margin-right: 10px;
    margin-top: 20px;
    background-color: #013977;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
}

.approve-btn:hover,
.reject-btn:hover {
    background-color: #F2753D;
}

.tab {
    overflow: hidden;
    border-bottom: 1px solid #ccc;
    background-color: #f1f1f1;
    display: flex;
    /* As abas agora ficam em uma linha horizontal */
    justify-content: space-around;
    /* Distribui as abas uniformemente */
}

.tab button {
    background-color: #F2753D;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 14px 16px;
    transition: 0.3s;
    font-size: 17px;
    flex: 1;
    /* Garante que as abas ocupem a mesma largura */
    text-align: center;
    /* Centraliza o texto em cada aba */
}

.tab button:hover {
    background-color: #ddd;
}

.modal-content p,
.modal-content h3 {
    color: #013977;
}

.tab button.active {
    background-color: #013977;
    /* Fundo azul escuro para a aba ativa */
    color: white;
    /* Texto branco para contraste */
}

.tabcontent {
    display: none;
    padding: 20px;
    border: 1px solid #ccc;
    border-top: none;
    background-color: #f9f9f9;
    /* Fundo claro para destacar o conteúdo */
}

.filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    /* Espaçamento entre os filtros */
    margin-bottom: 20px;
}

.filters input[type="text"],
.filters select,
.filters input[type="month"] {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
    width: 150px;
    /* Define uma largura padrão */
}

.filters input[type="text"] {
    flex: 2;
    /* Faz o campo de pesquisa ser mais largo */
}

.filters select {
    flex: 1;
}

.filters button {
    background-color: #013977;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

.filters button:hover {
    background-color: #F2753D;
}

/* Spinner de Carregamento de busca API */
/* Estilo do fundo do pop-up */
.loading-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Fundo escurecido */
    z-index: 9999;
    /* Para sobrepor todo o conteúdo */
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Estilo do conteúdo do pop-up */
.loading-popup-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

/* Estilo do spinner */
.loader {
    border: 16px solid #f3f3f3;
    border-top: 16px solid #3498db;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    margin: 0 auto 10px;
    animation: spin 1.5s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Estilo do texto dentro do pop-up */
.loading-popup-content p {
    margin-top: 10px;
    font-size: 16px;
    color: #333;
}


/***************************************************************************/
/** PERMISSOES.HTML **/
/***************************************************************************/
.permissoes .pn-header {
    height: 7%;
}

.permissoes main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.permissoes #sideBar-permissoes {
    width: 20%;
    height: 100%;
    background: #ffffff;
    padding: 1rem;
    border-right-style: solid;
    border-color: #235397;
}

.permissoes #painel-permissoes {
    width: 80%;
    height: 100%;
    background: #f1f1f1;
    padding: 1rem;
    border-left-style: solid;
    border-color: #235397;
}

.permissoes .nav-link {
    font-size: 1.2rem;
    font-weight: bold;
    color: #000;
}

.permissoes .nav-link:hover {
    color: #007bff;
}

.permissoes .collapse ul a {
    text-decoration: none !important;
    font-size: 1.1rem;
    color: #000;
    padding-left: 10px;
    display: block;
    transition: all 0.3s ease;
}

.permissoes .collapse ul a:hover {
    text-decoration: none !important;
    color: #007bff;
}

.permissoes .collapse.show ul a {
    text-decoration: none !important;
}

.permissoes .rotate-icon {
    transition: transform 0.3s ease;
}

.permissoes .nav-link[aria-expanded="true"] .rotate-icon {
    transform: rotate(45deg);
}

.permissoes .table.permissoes-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.permissoes .table .permissoes-th,
.permissoes .table .permissoes-td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: left;
}

.permissoes .table .permissoes-th {
    background-color: #235397;
    font-weight: bold;
}

.permissoes .table .permissoes-td {
    background-color: #fff;
}

.permissoes .delete-icon {
    color: red;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
}

/* Centraliza o texto na div */
.message-center {
    text-align: center;
    margin-top: 50px;
    /* Ajuste conforme necessário */
    font-weight: bold;
    color: #555;
    /* Ajuste conforme seu design */
}

/* O conteúdo da tabela não será afetado */
.permissoes .table.permissoes-table {
    width: 100%;
    margin-top: 20px;
    /* A tabela terá seu próprio espaçamento */
}

.permissoes .permissoes-td i.delete-icon {
    display: block;
    margin: 0 auto;
    text-align: center;
}

.permissoes .col-md-4 {
    margin-left: 10%;
    margin-right: -10%;
    margin-top: 1%;
}

.permissoes .btn.btn-success {
    margin-top: 4%;
    margin-left: 4%;
}


/***************************************************************************/
/** TABELA_PORGRADE.HTML **/
/***************************************************************************/
.tabelaPorGrade {
    font-family: 'Montserrat', sans-serif;
    background: #ffffff;
    color: #000;
}

.tabelaPorGrade .pn-header {
    height: 7%;
}

.tabelaPorGrade main.content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 3rem;
}

/* Estilização da tabela de comissões similar à da página de permissões */
.tabelaPorGrade .table.permissoes-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.tabelaPorGrade .table .permissoes-th,
.tabelaPorGrade .table .permissoes-td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: left;
}

.tabelaPorGrade .table .permissoes-th {
    background-color: #235397;
    font-weight: bold;
    color: white;
}

.tabelaPorGrade .table .permissoes-td {
    background-color: #fff;
}

/* Responsividade e rolagem horizontal */
.tabelaPorGrade .table-responsive {
    width: 100%;
    overflow-x: auto;
    /* Permite rolagem horizontal */
}

.tabelaPorGrade .table td {
    max-width: 200px;
    /* Limite para a largura das células */
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    /* Impede quebra de linha */
}

/* Estilo para hover */
.tabelaPorGrade .table-hover tbody tr:hover {
    background-color: #f1f1f1;
    /* Cor de fundo ao passar o mouse */
}


/***************************************************************************/
/** DETALHE_CHAMADO.HTML **/
/***************************************************************************/
.detalhesChamado {
    font-family: 'Montserrat', sans-serif;
    background: #ffffff;
    color: #000;
}

.detalhesChamado .pn-header {
    height: 7rem;
}

.detalhesChamado .content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.detalhesChamado .container {
    min-width: 95%;
}

.detalhesChamado.card-header {
    background-color: #235397 !important;
}

.detalhesChamado .card-primary .card-header-chat {
    color: #fff;
    background-color: #235397 !important;
    border-radius: .25rem .25rem 0 0;
    border-bottom: 1px solid rgba(0, 0, 0, .125);
    padding: .75rem 1.25rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.detalhesChamado a#documentos-tab,
.detalhesChamado a#dados-basicos-tab,
.detalhesChamado a#formulario-tab,
.detalhesChamado a#beneficiarios-tab {
    color: black;
}

.detalhesChamado .nav-link.active {
    font-weight: bold;
}

.detalhesChamado #chamadoTabs {
    margin-top: 3px;
}

.detalhesChamado .direct-chat-messages {
    overflow-y: auto;
    padding: 10px;
    border-radius: .25rem;
    min-height: 450px;
}

.detalhesChamado .direct-chat-img {
    border-radius: 50%;
    width: 40px;
    height: 40px;
}

.detalhesChamado .direct-chat-msg.right .direct-chat-text {
    background-color: #235397;
    color: #fff;
    border-radius: .3rem;
    border-color: #235397;
}

.detalhesChamado .direct-chat-primary .right>.direct-chat-text::after,
.detalhesChamado .direct-chat-primary .right>.direct-chat-text::before {
    border-left-color: #235397 !important;
}

.detalhesChamado .direct-chat-msg.left .direct-chat-text {
    background-color: #808080;
    color: #ffffff;
    border-radius: .3rem;
    border-color: #808080;
}

.detalhesChamado .direct-chat-text::after,
.detalhesChamado .direct-chat-text::before {
    border-right-color: #808080;
}

.detalhesChamado .chat-paragraph {
    color: #fff;
    font-weight: normal;
}

.detalhesChamado .direct-chat-msg {
    max-width: 52%;
}

.detalhesChamado .direct-chat-msg.right {
    margin-left: auto;
}

.detalhesChamado .direct-chat-msg.left {
    margin-right: auto;
}

.detalhesChamado .direct-chat-text {
    word-wrap: break-word;
    padding: 12px;
    border-radius: 10px;
    font-size: 1rem;
    min-height: 30px;
}

.detalhesChamado a {
    color: #a1eba0;
}

.detalhesChamado a:hover {
    color: #4886de;
}

.detalhesChamado #text-format-modal {
    z-index: 1001;
    left: 20px !important;
    top: 570px !important;
    border: 1px solid #b8b8b8;
    gap: 40px;
    transition: transform 0.5s ease, opacity 0.5s ease;
}

.detalhesChamado #bold-btn,
.detalhesChamado #italic-btn,
.detalhesChamado #strike-btn {
    background-color: #f4f6f9;
    color: black;
    font-size: 1.4rem;
    border-color: #f4f6f9;
}

.detalhesChamado .card-footer {
    padding: 1rem;
    background-color: #f4f6f9;
    border-top: 1px solid #ddd;
}

.detalhesChamado .card-footer .btn {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.detalhesChamado .badge-light {
    background-color: #f8f9fa;
    color: #000;
}

.detalhesChamado .form-control {
    height: 45px;
    margin-top: 0.55%;
}

.detalhesChamado .progress-container.d-flex.justify-content-between.align-items-center {
    margin: 5rem 0;
}

.detalhesChamado .progress-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.detalhesChamado .progress-step {
    position: relative;
    flex: 1;
    height: 10px;
    margin: 0 5px;
}

.detalhesChamado .progress-bar {
    height: 10px;
    border-radius: 5px;
}

.detalhesChamado .progress-step i {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    background-color: #f8f9fa;
    border-radius: 50%;
    padding: 5px;
}

.detalhesChamado .progress-step small {
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
}

.detalhesChamado .w-100 {
    width: 100%;
}

.detalhesChamado .w-50 {
    width: 50%;
}

.detalhesChamado .w-0 {
    width: 0%;
}

.detalhesChamado .btn.btn-link {
    padding: 0;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    border-radius: 0;
}

.detalhesChamado .input-group {
    position: relative !important;
}

.detalhesChamado #chat-message {
    border-radius: 8px !important;
    padding-right: 45px !important;
    resize: none;
    overflow: hidden;
    min-height: 45px;
    max-height: 120px;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.detalhesChamado #chat-message::-webkit-scrollbar {
    display: none;
}

.detalhesChamado .send-button {
    position: absolute !important;
    right: 10px !important;
    top: 57% !important;
    transform: translateY(-50%) !important;
    cursor: pointer !important;
    width: 30px !important;
    height: 30px !important;
    opacity: 1 !important;
    z-index: 10 !important;
    transition: opacity 0.3s ease !important;
}

.detalhesChamado .input-group:hover .send-button {
    opacity: 1 !important;
    transform: translateY(-50%) !important;
    z-index: 10 !important;
}

.detalhesChamado .input-group {
    position: relative !important;
    overflow: visible !important;
}

.check-icon {
    position: absolute;
    bottom: 5px;
    right: 10px;
    color: #fff;
    font-size: 1.2rem;
}

.direct-chat-msg {
    position: relative;
}

.direct-chat-msg.viewed .check-icon {
    color: #2fcc00;
}

th.cName,
tr.cArquivo {
    min-width: 15%;
    max-width: 37.5%;
}

th.cAction,
tr.cAction {
    min-width: 15%;
    max-width: 37.5%;
}

th.cData,
tr.cData {
    width: 10%;
}

th.cAction,
tr.cAction {
    width: 10%;
}

.detalhesChamado .form-group-btns {
    margin-left: 1rem;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-weight: bold;
}

.detalhesChamado .fa-solid.fa-file-zipper {
    margin-top: 0;
}

.detalhesChamado #download-all {
    color: #fff;
    background-color: #235397 !important;
}

.detalhesChamado .table th {
    background-color: #235397 !important;
}

.detalhesChamado .card-header {
    background-color: #235397 !important;
}

.detalhesChamado .col-md-12 {
    margin-bottom: 3rem;
}

.detalhesChamado .bi.bi-check-circle-fill.text-success {
    background-color: #f8f9fa;
}

.detalhesChamado .bi.bi-circle-fill.text-secondary {
    color: #ccc !important;
}

.detalhesChamado .row {
    --bs-gutter-x: 0;
}

.detalhesChamado .card-body {
    min-height: 511px;
}

.detalhesChamado .fa-solid,
.fas {
    font-size: large;
    font-weight: bold;
    margin-top: 6%;
}

.detalhesChamado .info-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    text-align: center;
    line-height: 20px;
    font-size: 14px;
    cursor: pointer;
    position: relative;
    margin-left: 10px;
    font-weight: bold;
}

.detalhesChamado .info-tooltip {
    display: none;
    position: absolute;
    top: 50%;
    left: 110%;
    transform: translateY(-50%);
    background-color: #333;
    color: white;
    padding: 5px;
    border-radius: 5px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
}

.detalhesChamado .info-icon:hover .info-tooltip {
    display: block;
}

.detalhesChamado .form-group {
    margin-left: 1rem;
    display: flex;
    justify-content: flex-start;
}

.detalhesChamado .file-list {
    margin-top: 15px;
}

.detalhesChamado .file-list .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.detalhesChamado .file-entry {
    margin-bottom: 20px;
    margin-left: 1rem;
    margin-right: 1rem;
}

.detalhesChamado .file-entry .custom-file-label {
    cursor: pointer;
}

.detalhesChamado .custom-file-label {
    color: #6c757d;
}

.detalhesChamado .file-entry .btn-upload {
    margin-top: 10px;
}

.detalhesChamado #propC_chamado {
    margin-left: 3rem;
    margin-top: -0.3%;
    font-size: 1rem;
    background-color: #235397 !important;
}

.detalhesChamado .tab-pane dl.row {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.detalhesChamado .tab-pane dl dt {
    font-weight: bold;
    color: #4a4a4a;
}

.detalhesChamado .tab-pane dl dd {
    margin-bottom: 5px;
    color: #6c757d;
}

.detalhesChamado #edit-button {
    color: #235397;
}


/***************************************************************************/
/** GESTAO_BOLETOS_CORRETORES.HTML **/
/***************************************************************************/
.gestao_boletos_corretores {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #fff;
}

.gestao_boletos_corretores .pn-header {
    height: 10%;
}

.gestao_boletos_corretores .content-container {
    margin-left: -0.55%;
    border-radius: 0;
}

.gestao_boletos_corretores #footer-box {
    margin-left: -0.6%;
}

.gestao_boletos_corretores .content-header {
    margin-top: 1%;
}

.gestao_boletos_corretores .content-wrapper {
    margin-left: 10px !important;
}

.gestao_boletos_corretores .card-header .card-tools {
    flex: 1 1 auto;
    max-width: 100%;
}

.card-header select,
.card-header input[type="text"],
.card-header input[type="month"] {
    min-width: 150px;
    /* Define um tamanho mínimo para os elementos */
    max-width: calc(100% / 3 - 10px);
    /* Mantém os elementos menores se necessário */
}

.gestao_boletos_corretores .card-header {
    background-color: #fff !important;
}


.gestao_boletos_corretores .fa-solid,
.fas {
    margin-top: 0px !important;
}

/***************************************************************************/
/** UPLOAD-ACOMPANHAMENTO-AMIL.HTML **/
/***************************************************************************/
.uploadAcompanhamento {
    margin: 0.8rem;
    background-color: #fff;
    font-family: 'Montserrat', sans-serif;
}

body.uploadAcompanhamento .pn-header {
    height: 10%;
}

.uploadAcompanhamento .content-container {
    margin-left: -0.55%;
    border-radius: 0;
    display: flex;
    justify-content: center;
    align-items: stretch;
}

.uploadAcompanhamento #footer-box {
    margin-left: -0.6%;
}

.upload-container {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    padding: 20px;
    margin-top: 10%;
}

.upload-h1 {
    text-align: center;
}

.upload-h3 {
    margin-bottom: 50px;
    font-weight: 500;
}

.uploadAcompanhamento #upload-form {
    margin: 2rem;
}

.uploadAcompanhamento .form-group {
    gap: 2rem;
}

.uploadAcompanhamento #modalidade {
    padding: 4px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
    margin-left: 0;
}

.uploadAcompanhamento input#file {
    padding: 4px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
    margin-left: 0;
    flex-basis: 100%;
}


/***************************************************************************/
/** ACOMPANHAMENTO-AMIL.HTML **/
/***************************************************************************/
.acompanhamentoAmil {
    margin: 0.8rem;
    background-color: #fff;
    font-family: 'Montserrat', sans-serif;
}

body.acompanhamentoAmil .pn-header {
    height: 10%;
}

.acompanhamentoAmil .content-container {
    margin-left: -0.55%;
    border-radius: 0;
}

.acompanhamentoAmil #footer-box {
    margin-left: -0.6%;
}

.acompanhamentoAmil .modal-header {
    padding: 3rem;
}

.acompanhamentoAmil .custom-modal {
    display: none;
    /* Inicialmente oculto */
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Fundo semitransparente */
    overflow: auto;
    /* Permite rolagem no modal */
}

.acompanhamentoAmil .custom-modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 90%;
    /* Largura de 90% da tela */
    max-height: 80vh;
    /* Limita a altura a 80% da tela */
    overflow-y: auto;
    /* Adiciona barra de rolagem se o conteúdo exceder a altura */
    position: relative;
    border-radius: 15px;
}

.acompanhamentoAmil .close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.acompanhamentoAmil .close-btn:hover,
.acompanhamentoAmil .close-btn:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.acompanhamentoAmil .modal-footer {
    text-align: right;
}

.acompanhamentoAmil .modal-backdrop {
    z-index: 1;
}

.acompanhamentoAmil #assistente-table_wrapper {
    width: 95%;
    margin: 0 auto;
}

.acompanhamentoAmil .table-responsive {
    overflow-x: auto;
}

@media (max-width: 768px) {
    .acompanhamentoAmil #assistente-table_wrapper {
        width: 100%;
        margin: 0;
    }

    .acompanhamentoAmil .card-body {
        padding: 0.5rem;
    }
}


/***************************************************************************/
/** PROCESSO_SELETIVO.HTML **/
/***************************************************************************/
.seletivo {
    height: 100%;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #fff;
}

.seletivo .seletivos {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 30px;
}

.seletivo .pn-header {
    height: 10%;
}

.seletivo .content-container {
    margin-left: -0.55%;
    border-radius: 0;
}

.seletivo #footer-box {
    margin-left: -0.6%;
}

.seletivo .seletivo-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    gap: 20px;
    margin: 0 auto;
    max-width: 1200px;
}

.seletivo .header-seletivo {
    text-align: center;
    margin-bottom: 20px;
}

.seletivo h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 3rem;
}

.seletivo .card {
    position: relative;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    background-color: #fff;
    transition: box-shadow 0.3s ease;
}

.seletivo .card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Ribbon Estilo */
.seletivo .ribbon {
    width: 150px;
    height: 150px;
    overflow: hidden;
    position: absolute;
    top: -10px;
    right: -10px;
}

.seletivo .ribbon::before,
.seletivo .ribbon::after {
    position: absolute;
    z-index: -1;
    content: '';
    display: block;
    border: 5px solid #2980b9;
}

.seletivo .ribbon span {
    position: absolute;
    display: block;
    width: 225px;
    padding: 15px 0;
    background-color: #3498db;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
    /* Suavizei a sombra */
    color: #fff;
    font-size: 14px;
    /* Aumentei um pouco o tamanho da fonte */
    font-weight: bold;
    text-transform: uppercase;
    text-align: center;
    left: -25px;
    top: 30px;
    transform: rotate(45deg);
}

/* Ajuste das bordas */
.seletivo .ribbon::before {
    top: 0;
    left: 0;
    border-top-color: transparent;
    border-left-color: transparent;
}

.seletivo .ribbon::after {
    bottom: 0;
    right: 0;
    border-bottom-color: transparent;
    border-right-color: transparent;
}

/* Ribbon estilo para "Encerrada" */
.seletivo .ribbon-encerrada span {
    background-color: #e74c3c;
    border-color: #c0392b;
}

.seletivo .ribbon-encerrada::before,
.seletivo .ribbon-encerrada::after {
    border-color: #c0392b;
}

/* Ribbon estilo para "Vaga Aberta" */
.seletivo .ribbon-aberta span {
    background-color: #27ae60;
}

.seletivo .ribbon-aberta::before,
.seletivo .ribbon-aberta::after {
    border-color: #1e8449;
}

/* Ajustes no botão */
.seletivo .btn {
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.seletivo .btn:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.seletivo .modal-content {
    max-width: 20000px;
    width: 95%;
    height: 90%;
    padding: 2rem;
}

.seletivo .header-modal {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
}

.seletivo h2.modal-title {
    margin-top: -2%;
    margin-bottom: 3%;
}

.seletivo .modal-content p {
    color: #000;
    font-weight: 300;
}

.seletivo strong {
    font-weight: bold;
    font-size: 1.2rem;
}

.seletivo #vaga,
#requisitos,
#desc {
    font-size: 1.1rem;
    padding-right: 1rem;
}

.seletivo #dt-final {
    padding-left: 1rem;
}

.seletivo label {
    font-size: 0.9rem;
}

.seletivo .form-group label {
    flex-basis: 19%;
}

.seletivo label.cargo_atual {
    margin-left: 2rem;
}

.seletivo .form-group {
    margin-top: 20px;
}

.seletivo #motivo {
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: calc(100% - 20px);
    box-sizing: border-box;
    height: 90px
}

.seletivo .modal-dialog {
    max-width: 80%;
    width: 80%;
}

.seletivo #formacao,
.seletivo #cnh,
.seletivo #carro {
    width: calc(100% - 20px);
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    outline: 0;
}


/***************************************************************************/
/**CREATE_TICKET.HTML **/
/***************************************************************************/
.ticket .form-group {
    display: block !important;
    width: 100%;
}

.ticket .pn-header {
    height: 15%;
}

.ticket .container {
    max-width: 3000px;
}

.ticket .btn.btn-primary,
.ticket .btn.btn-secondary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.ticket .file-list {
    margin-top: 15px;
}

.ticket .file-list .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.ticket .file-entry {
    margin-bottom: 20px;
}

.ticket .file-entry .custom-file-label {
    cursor: pointer;
}

.ticket .custom-file-label {
    color: #6c757d;
}

.ticket .file-entry .btn-upload {
    margin-top: 10px;
}

.ticket .progress {
    margin-top: 10px;
    height: 25px;
}

.ticket .info-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    text-align: center;
    line-height: 20px;
    font-size: 14px;
    cursor: pointer;
    position: relative;
    margin-left: 10px;
}

.ticket .info-tooltip {
    display: none;
    position: absolute;
    top: 50%;
    left: 110%;
    transform: translateY(-50%);
    background-color: #333;
    color: white;
    padding: 5px;
    border-radius: 5px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
}

.ticket .info-icon:hover .info-tooltip {
    display: block;
}

.ticket #modalidade {
    display: block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.ticket .form-control {
    margin-bottom: 1rem !important;
    width: 100% !important;
}

.ticket input[type="date"] {
    color: #495057;
    border: 1px solid #ced4da;
}

.ticket #sidebar {
    margin-top: 1.2rem;
}

.ticket .highlight {
    border: 2px solid red;
}

.ticket .row.justify-content-center {
    padding: 0rem 5rem;
}

.ticket #main-form {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, .125);
    border-radius: .25rem;
    padding: 1rem;
}


/***************************************************************************/
/** GESTAO_CHAMADOS.HTML **/
/***************************************************************************/
.gestaoChamados {
    font-family: 'Montserrat', sans-serif;
    background: #ffffff;
    color: #000;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.gestaoChamados .pn-header {
    height: 7rem;
}

.gestaoChamados h3 {
    margin: 1rem 0;
}

.gestaoChamados .content-container {
    flex: 1;
    padding: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
}

.gestaoChamados .small-box .icon>i {
    top: 8px;
}

.gestaoChamados .col-lg-6.col-12 {
    max-width: 20%;
    margin-top: 5%;
}

.gestaoChamados .container {
    max-width: 97%;
}

.gestaoChamados .modal-dialog {
    max-width: 90%;
    width: 100%;
    margin: auto;
    top: 10%;
    left: 15%;
}

.gestaoChamados .modal-content {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: .3rem;
    box-shadow: 0 .25rem .5rem rgba(0, 0, 0, .5);
    width: 100%;
    max-width: 1400px;
    height: 55vh;
}

.gestaoChamados .modal-header {
    background-color: #f5f5f5;
    border-bottom: 1px solid #dee2e6;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.gestaoChamados .modal-title {
    font-size: 18px;
    font-weight: 600;
}

.gestaoChamados .modal-body {
    padding: 20px;
    background-color: #fff;
    flex-grow: 1;
    overflow-y: auto;
}

.gestaoChamados .chart-container {
    width: 100%;
    height: 400px;
}

.gestaoChamados .modal-footer {
    padding: 10px;
    background-color: #f5f5f5;
    border-top: 1px solid #dee2e6;
    text-align: right;
}

.gestaoChamados .close {
    background-color: transparent;
    border: 0;
    font-size: 1.5rem;
    line-height: 1;
    cursor: pointer;
    opacity: 0.5;
}

.gestaoChamados .close:hover {
    opacity: 1;
}

.gestaoChamados .chart-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    margin-left: 53px;
}

.gestaoChamados .d-flex {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
}

.gestaoChamados .chart-legend {
    min-width: 150px;
    margin-right: 20px;
    font-size: 14px;
}

.gestaoChamados .chart-legend li {
    list-style: none;
    margin-bottom: 5px;
}

.gestaoChamados .chart-legend li span {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 10px;
}

.gestaoChamados .container {
    flex: 1;
    margin-bottom: 2rem;
}

.gestaoChamados footer.footer-container {
    clear: both;
    width: 100%;
    position: relative;
    bottom: 0;
}

.gestaoChamados .select2-container--default .select2-selection--multiple {
    height: calc(2.25rem + 2px) !important;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da !important;
    border-radius: .25rem !important;
    box-shadow: inset 0 0 0 transparent;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.gestaoChamados .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #e4e4e4;
    color: #000;
}

.gestaoChamados .input-icon {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    color: #aaa;
}

.gestaoChamados #search-input {
    padding-right: 40px;
}

.gestaoChamados .table-responsive {
    max-width: 100%;
    /* Limite máximo da tabela */
    margin: 0 auto;
    /* Centraliza a tabela */
    overflow-x: auto;
    /* Adiciona barra de rolagem horizontal */
    -webkit-overflow-scrolling: touch;
}

.gestaoChamados table {
    width: 100%;
    table-layout: fixed;
}

.gestaoChamados th,
.gestaoChamados td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.gestaoChamados th {
    cursor: col-resize;
}

.gestaoChamados th:not(:last-child) {
    border-right: 2px solid #ddd;
}

.gestaoChamados td {
    text-align: center;
}

#chamados-table {
    width: 100%;
    table-layout: fixed;
}

#chamados-table th {
    min-width: 50px;
    width: auto;
}

@media (max-width: 768px) {
    .gestaoChamados .table-responsive {
        max-width: 100%;
        font-size: 10px;
        padding: 0 5px;
    }

    .gestaoChamados th,
    .gestaoChamados td {
        font-size: 10px;
    }

    .gestaoChamados .container {
        padding: 0 10px;
    }
}


/***************************************************************************/
/** CHAMADOS_COLABORADOR.HTML **/
/***************************************************************************/
.chamados_colaborador {
    font-family: 'Montserrat', sans-serif;
    background: #ffffff;
    color: #000;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.chamados_colaborador .container {
    max-width: 90%;
}

.chamados_colaborador .row.mt-4 {
    gap: 1rem;
    margin-left: 0.1%;
    margin-bottom: 1.5rem;
}

.chamados_colaborador .col-md-6 {
    margin-top: 1.5%;
    margin-left: -.8%;
}

.chamados_colaborador #chamados-table {
    margin-top: 1rem;
}

.chamados_colaborador th.assunto,
.chamados_colaborador td.assunto {
    width: 30%;
}

.chamados_colaborador th.tpChamado,
.chamados_colaborador td.tpChamado {
    width: 10%;
}

.chamados_colaborador th.setor,
.chamados_colaborador td.setor {
    width: 10%;
}

.chamados_colaborador th.dtAbertura,
.chamados_colaborador td.dtAbertura {
    width: 10%;
}

.chamados_colaborador th.andamento,
.chamados_colaborador td.andamento {
    width: 10%;
}

.chamados_colaborador th.user_responsavel,
.chamados_colaborador td.user_responsavel {
    width: 20%;
}

.chamados_colaborador .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #e4e4e4;
    color: #000;
}