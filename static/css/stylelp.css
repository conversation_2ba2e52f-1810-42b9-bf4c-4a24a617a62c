/* style.css */

/* Reset básico e fontes */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: var(--background-color);
    padding-top: 120px; /* Ajuste este valor de acordo com a altura do seu header */
  }
  .container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  /* Definição dos temas via CSS Variables */
  :root {
    /* <PERSON><PERSON>lar<PERSON> (inspirado no Brazil Health) */
    --primary-color: #003366;  /* Azul escuro */
    --secondary-color: #FFD700; /* Amarelo vibrante */
    --text-color: #333;
    --background-color: #fff;
    --overlay-color: rgba(0, 0, 0, 0.5);
  }
  
  body.dark-theme {
    /* Tema Escuro (alternativo) */
    --primary-color: #1a1a1a;  
    --secondary-color: #4d4d4d;
    --text-color: #eee;
    --background-color: #121212;
    --overlay-color: rgba(0, 0, 0, 0.7);
  }
  
  /* Estilos do botão switch */
  .theme-switch {
    margin-right: 15px;
  }
  .switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 26px;
  }
  .switch input { 
    opacity: 0;
    width: 0;
    height: 0;
  }
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0; left: 0;
    right: 0; bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 26px;
  }
  .slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
  }
  input:checked + .slider {
    background-color: var(--primary-color);
  }
  input:checked + .slider:before {
    transform: translateX(24px);
  }
  
  /* Header */
  .site-header {
    background: var(--background-color);
    border-bottom: 1px solid #eaeaea;
    position: fixed;
    width: 100%;
    z-index: 1000;
    padding: 5px 0;
    top: 0;
    left: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }
  .header-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 5px 0;
  }
  .logo h1 {
    font-size: 1.8rem;
    color: var(--primary-color);
  }
  .nav-menu ul {
    display: flex;
    list-style: none;
  }
  .nav-menu ul li a {
    color: var(--primary-color);
    padding: 0 15px;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
  }
  .nav-menu ul li a:hover {
    color: var(--secondary-color);
  }
  .menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
  }
  
  /* Estilo para o logo */
  #logo img {
    max-width: 1em;
    height: auto;
    margin: 0 0.625rem;
    transition: transform 0.3s ease;
  }

  #logo img:hover {
    transform: scale(1.1);
  }

  /* Hero Section */
  .hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-image: url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding-top: 160px;
  }
  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 51, 102, 0.85), rgba(0, 51, 102, 0.75)); /* Gradiente azulado profissional */
    margin-top: 0;
  }
  .hero-content {
    position: relative;
    color: #fff;
    z-index: 1;
    animation: fadeInUp 1s ease-out;
    padding-top: 20px;
  }
  .hero-content h2 {
    font-size: 3rem;
    margin-bottom: 20px;
  }
  .hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
  }
  .btn-primary {
    background: var(--primary-color);
    color: #fff;
    padding: 12px 25px;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: background 0.3s;
    text-decoration: none;
    font-weight: 500;
  }
  .btn-primary:hover {
    background: #002244;
  }
  
  /* Sections */
  .section {
    padding: 80px 0;
    text-align: center;
  }
  .section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 10px;
  }
  .section-header p {
    font-size: 1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto 40px;
  }
  
  /* Solutions Grid */
  .solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
  }
  .solution-card {
    background: var(--background-color);
    border-radius: 15px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s;
  }
  .solution-card:hover {
    transform: translateY(-5px);
  }
  .solution-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
  }
  .card-content {
    padding: 20px;
  }
  .card-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--primary-color);
  }
  .card-content p {
    font-size: 0.95rem;
    margin-bottom: 15px;
    color: #555;
  }
  .btn-secondary {
    background: var(--secondary-color);
    color: var(--primary-color);
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.3s;
  }
  .btn-secondary:hover {
    background: #e6c200;
  }
  
  /* Promoções */
  .promo-form-wrapper {
    max-width: 500px;
    margin: 0 auto;
  }
  #promoForm {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  #promoForm input[type="text"] {
    flex: 1;
    padding: 12px;
    border: 1px solid #ccc;
    border-top-left-radius: 30px;
    border-bottom-left-radius: 30px;
    font-size: 1rem;
  }
  #promoForm button {
    padding: 12px 25px;
    border: none;
    background: var(--secondary-color);
    color: var(--primary-color);
    font-weight: 600;
    border-top-right-radius: 30px;
    border-bottom-right-radius: 30px;
    cursor: pointer;
    transition: background 0.3s;
  }
  #promoForm button:hover {
    background: #e6c200;
  }
  
  /* Contato */
  .contact {
    background-color: #f8f9fa;
    padding: 80px 0;
  }

  .contact-form {
    max-width: 700px;
    margin: 0 auto;
    background: #fff;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  }

  .contact-form .form-group {
    margin-bottom: 25px;
  }

  .contact-form label,
  .contact-form .group-label {
    display: block;
    margin-bottom: 8px;
    color: var(--primary-color);
    font-weight: 500;
    font-size: 1rem;
  }

  .contact-form input,
  .contact-form select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
  }

  .contact-form input:focus,
  .contact-form select:focus {
    border-color: var(--primary-color);
    outline: none;
  }

  .contact-form .radio-group {
    margin-bottom: 25px;
  }

  .contact-form .radio-options {
    display: flex;
    gap: 30px;
    margin-top: 10px;
  }

  .contact-form .radio-option {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .contact-form .radio-option input[type="radio"] {
    width: auto;
    margin-right: 5px;
  }

  .contact-form .radio-option label {
    margin: 0;
    font-weight: normal;
  }

  .contact-form select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M8 11.5l-6-6h12z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    padding-right: 40px;
  }

  .contact-form button {
    width: 100%;
    padding: 15px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
  }

  .contact-form button i {
    font-size: 1.2rem;
  }

  .form-disclaimer {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
    margin-top: 15px;
  }

  /* Responsividade do formulário */
  @media screen and (max-width: 768px) {
    .contact {
      padding: 60px 0;
    }

    .contact-form {
      padding: 30px 20px;
      margin: 0 15px;
    }

    .contact-form .radio-options {
      flex-direction: column;
      gap: 15px;
    }
  }

  @media screen and (max-width: 480px) {
    .contact {
      padding: 40px 0;
    }

    .contact-form {
      padding: 20px 15px;
    }

    .contact-form input,
    .contact-form select {
      padding: 10px 12px;
      font-size: 0.95rem;
    }

    .contact-form button {
      padding: 12px;
      font-size: 1rem;
    }
  }
  
  /* Footer */
  .site-footer {
    background: var(--primary-color);
    color: #fff;
    padding: 30px 0;
  }
  .site-footer p {
    margin-bottom: 10px;
  }
  .footer-nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
  }
  .footer-nav ul li a {
    color: #fff;
    margin: 0 10px;
    text-decoration: none;
    transition: color 0.3s;
  }
  .footer-nav ul li a:hover {
    color: var(--secondary-color);
  }
  
  /* Animações */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Elementos animados */
  .animate-element {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
  }

  .animate-element.animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Delay nas animações para criar efeito cascata */
  .about-card:nth-child(1) { transition-delay: 0.1s; }
  .about-card:nth-child(2) { transition-delay: 0.2s; }
  .about-card:nth-child(3) { transition-delay: 0.3s; }

  .stat-item:nth-child(1) { transition-delay: 0.2s; }
  .stat-item:nth-child(2) { transition-delay: 0.3s; }
  .stat-item:nth-child(3) { transition-delay: 0.4s; }

  .partner-card { transition-delay: 0.2s; }

  /* Animação suave para links âncora */
  html {
    scroll-behavior: smooth;
  }

  /* Responsividade */
  @media (max-width: 768px) {
    .hero-content h2 {
      font-size: 2.2rem;
    }
    .nav-menu {
      display: none;
    }
    .menu-toggle {
      display: block;
      color: var(--primary-color);
    }
  }
  
  .highlight {
    font-size: 1.5em;
    font-weight: bold;
    color: #27ae1f;
    text-decoration: underline;
    text-align: center;
    margin: 8px 0;
    line-height: 1.2;
  }

  /* Estilos para o container de logos */
  .logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    padding: 5px 0;
    width: 100%;
    max-width: 500px;
    margin-bottom: 10px;
  }

  .logo-wrapper {
    flex: 0 1 auto;
    max-width: 150px;
    min-width: 100px;
    text-align: center;
  }

  .logo-img {
    width: auto;
    height: auto;
    max-height: 40px;
    max-width: 100%;
    object-fit: contain;
  }

  .discount-offer {
    width: 100%;
    text-align: center;
    margin: 10px 0;
  }

  /* Ajustes responsivos */
  @media screen and (max-width: 768px) {
    .header-container {
      padding: 3px 0;
    }

    .logo-container {
      gap: 10px;
      padding: 3px 0;
    }

    .logo-wrapper {
      max-width: 120px;
      min-width: 80px;
    }

    .logo-img {
      max-height: 35px;
    }

    .highlight { 
      font-size: 1.2em;
      margin: 5px 0;
    }
  }

  @media screen and (max-width: 480px) {
    .logo-container {
      gap: 8px;
    }

    .logo-wrapper {
      max-width: 100px;
      min-width: 70px;
    }

    .logo-img {
      max-height: 30px;
    }

    .highlight {
      font-size: 1em;
      margin: 4px 0;
    }
  }

  /* Sobre a Empresa */
  .about-section {
    background-color: #f8f9fa;
    padding: 100px 0;
  }

  .about-content {
    display: flex;
    flex-direction: column;
    gap: 60px;
    margin-top: 40px;
  }

  .about-text {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
  }

  .about-card {
    background: #fff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    text-align: left;
  }

  .about-card:hover {
    transform: translateY(-5px);
  }

  .about-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
  }

  .about-card h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
  }

  .about-card p {
    color: #666;
    line-height: 1.6;
  }

  .about-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    text-align: center;
    margin-top: 20px;
  }

  .stat-item {
    padding: 20px;
    background: var(--primary-color);
    border-radius: 15px;
    color: #fff;
    transition: transform 0.3s ease;
  }

  .stat-item:hover {
    transform: scale(1.05);
  }

  .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    display: block;
    margin-bottom: 10px;
    color: var(--secondary-color);
  }

  .stat-item p {
    font-size: 1.1rem;
    color: #fff;
  }

  /* Responsividade para a seção Sobre */
  @media screen and (max-width: 768px) {
    .about-section {
      padding: 60px 0;
    }

    .about-text {
      grid-template-columns: 1fr;
    }

    .about-card {
      padding: 20px;
    }

    .about-card i {
      font-size: 2rem;
    }

    .about-card h3 {
      font-size: 1.3rem;
    }

    .stat-item {
      padding: 15px;
    }

    .stat-number {
      font-size: 2rem;
    }

    .stat-item p {
      font-size: 1rem;
    }
  }

  @media screen and (max-width: 480px) {
    .about-section {
      padding: 40px 0;
    }

    .about-stats {
      grid-template-columns: 1fr;
    }

    .stat-item {
      padding: 10px;
    }

    .stat-number {
      font-size: 1.8rem;
    }
  }

  /* Promoções */
  .promotions {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }

  .promo-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
    margin-top: 30px;
  }

  .promo-card {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 40px;
    position: relative;
    overflow: hidden;
  }

  .offer-badge {
    position: absolute;
    top: 20px;
    right: -30px;
    background: var(--secondary-color);
    color: var(--primary-color);
    padding: 8px 40px;
    transform: rotate(45deg);
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .offer-content {
    text-align: left;
    max-width: 800px;
    margin: 0 auto;
  }

  .offer-content i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
  }

  .offer-content h3 {
    font-size: 2.2rem;
    color: var(--primary-color);
    margin-bottom: 15px;
  }

  .offer-benefits {
    list-style: none;
    margin: 25px 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
  }

  .offer-benefits li {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
  }

  .offer-benefits li i {
    font-size: 1.2rem;
    color: #27ae60;
    margin: 0;
  }

  .offer-action {
    text-align: center;
    margin-top: 30px;
  }

  .partnership-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    padding: 20px;
  }

  .partner-card {
    background: #fff;
    border-radius: 15px;
    padding: 25px;
    flex: 1;
    min-width: 280px;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  }

  .partner-logo {
    width: 180px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .partner-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  .partner-text {
    text-align: center;
  }

  .partner-text h4 {
    color: var(--primary-color);
    font-size: 1.3rem;
    margin-bottom: 10px;
  }

  .partner-text p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
  }

  .partner-text h4 {
    color: var(--primary-color);
    font-size: 1.3rem;
    margin-bottom: 10px;
  }

  .partner-text p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
  }

  .partnership-plus {
    font-size: 2rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  /* Responsividade para Promoções */
  @media screen and (max-width: 768px) {
    .promo-card {
      padding: 30px 20px;
    }

    .offer-badge {
      font-size: 0.9rem;
      padding: 6px 30px;
    }

    .offer-content h3 {
      font-size: 1.8rem;
    }

    .offer-benefits {
      grid-template-columns: 1fr;
    }

    .offer-benefits li {
      font-size: 1rem;
    }

    .partnership-info {
      gap: 20px;
    }

    .partner-card {
      padding: 20px;
      min-width: 250px;
    }

    .partner-logo {
      width: 150px;
      height: 50px;
    }
  }

  @media screen and (max-width: 480px) {
    .promo-card {
      padding: 20px 15px;
    }

    .offer-content h3 {
      font-size: 1.5rem;
    }

    .offer-badge {
      font-size: 0.8rem;
      padding: 5px 25px;
    }

    .partner-card {
      min-width: 100%;
    }

    .partnership-plus {
      width: 40px;
      height: 40px;
      font-size: 1.5rem;
    }
  }

  /* Carrossel de Parceiros */
  .partners-carousel {
    background: #fff;
    padding: 60px 0;
    border-top: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea;
    margin-top: -1px;
  }

  .partners-carousel .section-header {
    text-align: center;
    margin-bottom: 40px;
  }

  .partners-carousel .section-header h2 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 700;
  }

  .partners-carousel .section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
  }

  /* Swiper Carrossel */
  .partners-swiper {
    padding: 20px 40px;
    position: relative;
  }

  .swiper-slide {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .swiper-slide img {
    max-width: 100%;
    max-height: 80px;
    object-fit: contain;
    filter: grayscale(100%);
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  .swiper-slide:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }

  .swiper-slide:hover img {
    filter: grayscale(0%);
    opacity: 1;
    transform: scale(1.05);
  }

  /* Navegação do Swiper */
  .swiper-button-next,
  .swiper-button-prev {
    color: var(--primary-color);
    background: #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .swiper-button-next:after,
  .swiper-button-prev:after {
    font-size: 18px;
    font-weight: bold;
  }

  .swiper-button-next:hover,
  .swiper-button-prev:hover {
    background: var(--primary-color);
    color: #fff;
    transform: scale(1.1);
  }

  .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background: var(--primary-color);
    opacity: 0.5;
  }

  .swiper-pagination-bullet-active {
    opacity: 1;
    background: var(--primary-color);
    transform: scale(1.2);
  }

  /* Responsividade */
  @media screen and (max-width: 1024px) {
    .partners-swiper {
      padding: 15px 35px;
    }

    .swiper-slide {
      height: 100px;
    }

    .swiper-slide img {
      max-height: 60px;
    }
  }

  @media screen and (max-width: 768px) {
    .partners-carousel {
      padding: 40px 0;
    }

    .partners-swiper {
      padding: 10px 30px;
    }

    .swiper-slide {
      height: 90px;
      padding: 15px;
    }

    .swiper-slide img {
      max-height: 50px;
    }

    .swiper-button-next,
    .swiper-button-prev {
      width: 35px;
      height: 35px;
    }

    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
  }

  @media screen and (max-width: 480px) {
    .partners-carousel {
      padding: 30px 0;
    }

    .partners-swiper {
      padding: 10px 25px;
    }

    .swiper-slide {
      height: 80px;
      padding: 10px;
    }

    .swiper-slide img {
      max-height: 40px;
    }

    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }

    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 14px;
    }
  }

  /* Seção WhatsApp */
  .whatsapp-section {
    margin-top: 40px;
    text-align: center;
  }

  .whatsapp-section p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 20px;
  }

  .whatsapp-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background-color: #25D366;
    color: #fff;
    padding: 15px 30px;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.2);
  }

  .whatsapp-button i {
    font-size: 1.4rem;
  }

  .whatsapp-button:hover {
    background-color: #128C7E;
    color: #fff;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.3);
  }

  .whatsapp-button:active {
    transform: translateY(-1px);
  }

  /* Animação de pulso para o botão */
  @keyframes whatsappPulse {
    0% {
      box-shadow: 0 4px 15px rgba(37, 211, 102, 0.2);
    }
    50% {
      box-shadow: 0 4px 25px rgba(37, 211, 102, 0.4);
    }
    100% {
      box-shadow: 0 4px 15px rgba(37, 211, 102, 0.2);
    }
  }

  .whatsapp-button {
    animation: whatsappPulse 2s infinite;
  }

  /* Responsividade para WhatsApp */
  @media screen and (max-width: 768px) {
    .whatsapp-section {
      margin-top: 30px;
    }

    .whatsapp-section p {
      font-size: 1rem;
      margin-bottom: 15px;
    }

    .whatsapp-button {
      padding: 12px 25px;
      font-size: 1rem;
    }

    .whatsapp-button i {
      font-size: 1.2rem;
    }
  }

  @media screen and (max-width: 480px) {
    .whatsapp-section {
      margin-top: 25px;
    }

    .whatsapp-button {
      width: 100%;
      padding: 12px 20px;
    }
  }

  /* WhatsApp Flutuante */
  .whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
  }

  .whatsapp-float-btn {
    display: flex;
    align-items: center;
    background-color: #25D366;
    color: white;
    padding: 12px 20px;
    border-radius: 30px;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    transition: all 0.3s ease;
  }

  .whatsapp-float-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
    background-color: #128C7E;
  }

  .whatsapp-float-btn i {
    font-size: 24px;
    margin-right: 8px;
  }

  .whatsapp-float-text {
    font-weight: 500;
    font-size: 14px;
  }

  /* Animação de pulso para o botão flutuante */
  @keyframes floatPulse {
    0% {
      box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    }
    50% {
      box-shadow: 0 4px 25px rgba(37, 211, 102, 0.5);
    }
    100% {
      box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    }
  }

  .whatsapp-float-btn {
    animation: floatPulse 2s infinite;
  }

  /* Responsividade do botão flutuante */
  @media screen and (max-width: 768px) {
    .whatsapp-float {
      bottom: 15px;
      right: 15px;
    }

    .whatsapp-float-btn {
      padding: 10px 16px;
    }

    .whatsapp-float-btn i {
      font-size: 20px;
    }

    .whatsapp-float-text {
      font-size: 12px;
    }
  }

  @media screen and (max-width: 480px) {
    .whatsapp-float-text {
      display: none;
    }

    .whatsapp-float-btn {
      padding: 12px;
      border-radius: 50%;
    }

    .whatsapp-float-btn i {
      margin: 0;
    }
  }