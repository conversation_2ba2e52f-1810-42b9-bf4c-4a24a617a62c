lockdown-install.js:1 Removing unpermitted intrinsics
contas_home.js:26 DOM completamente carregado e analisado
contas_home.js:61 Função initializePageEvents chamada
contas_home.js:61 Função initializePageEvents chamada
contas_home:1 Uncaught (in promise) Error: Access to storage is not allowed from this context.
contas_home:1 Uncaught (in promise) Error: Access to storage is not allowed from this context.
contas_home:1 Uncaught (in promise) Error: Access to storage is not allowed from this context.
initial.M36isyzh.js:988 Uncaught (in promise) Error: Access to storage is not allowed from this context.
contas_home.js:132 Front-end log: Iniciando fetch da URL: /contas_funcionarios
contas_home.js:132 Front-end log: Iniciando fetch da URL: /contas_funcionarios
contas_home.js:142 Front-end log: HTML recebido com sucesso.
contas_home.js:383 Carregando Funcionários...
contas_home.js:142 Front-end log: HTML recebido com sucesso.
contas_home.js:383 Carregando Funcionários...
contas_home.js:421 Funcionários carregados.
contas_home.js:421 Funcionários carregados.
contas_home.js:449 Buscando dados do funcionário ID: 12
contas_home.js:454 Resposta da API: Response
contas_home.js:461 Dados do funcionário recebidos da API: Object
contas_home.js:462 Tipo de dados recebidos: object
contas_home.js:463 É array? false
contas_home.js:464 Propriedades: Array(5)
contas_home.js:481 Dados do funcionário recebidos na função openEditModalFuncionario: Object
contas_home.js:618 Tipo de dados recebidos: object
contas_home.js:630 Dados para preencher o formulário: Object
contas_home.js:651 Campos preenchidos: ID: 12 Nome: CASSIA DE SOUSA VITORINO CPF: 381.797.218-09 Email: <EMAIL> Telefone: (11) 99368-1551
contas_home.js:449 Buscando dados do funcionário ID: 12
contas_home.js:454 Resposta da API: Response
contas_home.js:461 Dados do funcionário recebidos da API: Object
contas_home.js:462 Tipo de dados recebidos: object
contas_home.js:463 É array? false
contas_home.js:464 Propriedades: Array(5)
contas_home.js:481 Dados do funcionário recebidos na função openEditModalFuncionario: Object
contas_home.js:618 Tipo de dados recebidos: object
contas_home.js:630 Dados para preencher o formulário: Object
contas_home.js:651 Campos preenchidos: ID: 12 Nome: CASSIA DE SOUSA VITORINO CPF: 381.797.218-09 Email: <EMAIL> Telefone: (11) 99368-1551
