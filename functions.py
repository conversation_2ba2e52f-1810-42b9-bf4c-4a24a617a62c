import logging
import re
from datetime import datetime
from flask import request, jsonify, send_file
import pytz
import unicodedata
import requests
import io
import os
import zipfile
from dependencies import (
    insert_log,
    consulta_ticket_file,
)

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def limpar_valor(valor):
    return float(valor.replace("R$", "").replace(".", "").replace(",", ".")) if valor else 0.0

def formatar_telefone(telefone):
    return re.sub(r'\D', '', telefone) if telefone else ''

def formatar_cpf(cpf):
    return (
        f"{cpf[:3]}.{cpf[3:6]}.{cpf[6:9]}-{cpf[9:]}" if cpf and len(cpf) == 11 else cpf
    )

def formatar_tel(telefone):
    telefone = str(telefone)  # Converte o telefone para string
    if telefone and len(telefone) == 10:
        return f"({telefone[:2]}) {telefone[2:6]}-{telefone[6:]}"
    elif telefone and len(telefone) == 11:
        return f"({telefone[:2]}) {telefone[2:7]}-{telefone[7:]}"
    else:
        return telefone

def senha_valida(senha):
    if len(senha) < 6 or len(senha) > 64:
        return False
    if not re.search("[a-zA-Z]", senha):
        return False
    if not re.search("[0-9]", senha):
        return False
    if not re.search("[!@#$%&*]", senha):
        return False
    return True

def gerar_saudacao():
    """Retorna uma saudação com base no horário de São Paulo."""
    fuso_sao_paulo = pytz.timezone("America/Sao_Paulo")
    hora_atual = datetime.now(fuso_sao_paulo).hour

    if hora_atual < 12:
        return "Bom dia"
    elif 12 <= hora_atual < 18:
        return "Boa tarde"
    else:
        return "Boa noite"
    
def gerar_protocolo(id_chamado):
    data_atual = datetime.now().strftime('%Y%m%d')
    id_formatado = f"{id_chamado:06d}"
    logging.info(f"{data_atual}{id_formatado}")
    return f"{data_atual}{id_formatado}"
    
def record_log(user_id, action):
    """
    Registra o log da ação do usuário.
    """
    try:
        route = request.path
        ip_address = request.headers.get('X-Forwarded-For', request.remote_addr)
        windows_username = request.form.get("windows_username", "Unknown")
        insert_log(user_id, route, action, ip_address, windows_username)
    except Exception as e:
        logging.error("Erro ao registrar log: %s", e)

def normalize_text(text: str) -> str:
    """
    Recebe uma string e retorna outra onde:
    - espaços são substituídos por underline (_)
    - todas as letras ficam em minúsculas
    - acentos são removidos (por ex. "ã" → "a", "é" → "e")
    """
    # 1) passa para minúsculas
    text = text.lower()
    # 2) substitui espaços por _
    text = text.replace(' ', '_')
    # 3) normaliza e remove acentos
    nkfd = unicodedata.normalize('NFKD', text)
    return ''.join(c for c in nkfd if not unicodedata.combining(c))

def download_all_files_as_zip(ticket_id):
    try:
        # Obtém a lista de arquivos para o ticket
        files = consulta_ticket_file(ticket_id)

        # Cria um arquivo ZIP em memória
        memory_zip = io.BytesIO()
        
        # Adiciona arquivos ao ZIP em memória
        with zipfile.ZipFile(memory_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file in files:
                file_url = file['url_arquivo']  # URL do S3
                file_name = file['nome_arquivo']
                
                # Extrai a extensão do arquivo a partir da URL do S3
                _, file_extension = os.path.splitext(file_url)
                
                # Nome completo com extensão para salvar no ZIP
                temp_file_name = f"{file_name}{file_extension}"

                # Baixar o arquivo do S3 e adicioná-lo ao ZIP em memória
                try:
                    response = requests.get(file_url, stream=True)
                    if response.status_code == 200:
                        # Lê o conteúdo do arquivo e adiciona ao ZIP em memória
                        zipf.writestr(temp_file_name, response.content)
                    else:
                        logging.warning(f"Não foi possível baixar o arquivo {temp_file_name} do S3.")
                except Exception as e:
                    logging.error(f"Erro ao baixar o arquivo {temp_file_name} do S3: {e}")

        # Define o ponteiro do ZIP para o início
        memory_zip.seek(0)

        # Nome do arquivo ZIP para o usuário
        zip_filename = f"ticket_{ticket_id}_files_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"

        # Envia o arquivo ZIP em memória como resposta
        return send_file(memory_zip, as_attachment=True, download_name=zip_filename, mimetype='application/zip')

    except Exception as e:
        logging.error(f"Erro ao criar o arquivo ZIP para o chamado {ticket_id}: {e}")
        return jsonify({"error": "Erro ao baixar todos os arquivos"}), 500
