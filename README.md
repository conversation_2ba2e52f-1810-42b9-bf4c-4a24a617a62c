# 🏥 Brazil Health - Intranet

<div align="center">
  
  ![Logo Brazil Health](https://brazilhealth.com.br/wp-content/uploads/2024/05/logobrazilhealthog.png)
  
  [![Status](https://img.shields.io/badge/Status-Produção-success?style=for-the-badge)]()
  [![Python](https://img.shields.io/badge/Python-3.12.9-blue?style=for-the-badge&logo=python&logoColor=white)]()
  [![Flask](https://img.shields.io/badge/Flask-2.2.3-lightgrey?style=for-the-badge&logo=flask&logoColor=white)]()
  [![PostgreSQL](https://img.shields.io/badge/PostgreSQL-Latest-336791?style=for-the-badge&logo=postgresql&logoColor=white)]()
  [![License](https://img.shields.io/badge/License-Proprietary-red?style=for-the-badge)]()

  *Plataforma interna para otimização dos processos de gestão da Brazil Health*
</div>

## 📋 Índice

- [📝 Introdução](#-introdução)
- [🔧 Tecnologias Utilizadas](#-tecnologias-utilizadas)
- [📁 Estrutura do Projeto](#-estrutura-do-projeto)
- [📋 Pré-requisitos](#-pré-requisitos)
- [🚀 Instalação](#-instalação)
- [⚙️ Configuração](#️-configuração)
- [▶️ Executando o Projeto](#️-executando-o-projeto)
- [📦 Módulos Principais](#-módulos-principais)
- [☁️ Deploy](#️-deploy)
- [🤝 Diretrizes de Contribuição](#-diretrizes-de-contribuição)
- [⚖️ Licença](#️-licença)

## 📝 Introdução

A Brazil Health - Intranet é uma plataforma interna desenvolvida para otimizar os processos de gestão da Brazil Health, oferecendo um conjunto abrangente de funcionalidades para o gerenciamento de operações no setor de saúde. A plataforma integra módulos de gestão de chamados, acompanhamento de comissões, gestão financeira, controle de acesso, gerenciamento de reembolsos, suporte a corretores e análise de dados de performance.

## 🔧 Tecnologias Utilizadas

<table>
  <tr>
    <td>
      <h3>🐍 Backend</h3>
      <ul>
        <li>✅ Python 3.12.9</li>
        <li>✅ Flask 2.2.3</li>
        <li>✅ Flask-Mail</li>
        <li>✅ Flask-SocketIO</li>
        <li>✅ Flask-Limiter</li>
        <li>✅ PostgreSQL (psycopg2)</li>
        <li>✅ Redis</li>
        <li>✅ AWS S3 (boto3)</li>
        <li>✅ APScheduler 3.10.4</li>
      </ul>
    </td>
    <td>
      <h3>🖌️ Frontend</h3>
      <ul>
        <li>✅ JavaScript</li>
        <li>✅ Bootstrap</li>
        <li>✅ FontAwesome</li>
        <li>✅ FullCalendar 6.1.11</li>
        <li>✅ Chart.js</li>
        <li>✅ Plotly</li>
        <li>✅ Dash 2.9.3</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td>
      <h3>📊 Processamento de Dados</h3>
      <ul>
        <li>✅ Pandas</li>
        <li>✅ NumPy</li>
        <li>✅ Dateutil</li>
        <li>✅ PDF2Image</li>
        <li>✅ PyTesseract</li>
        <li>✅ PyPDF2</li>
        <li>✅ PDFPlumber</li>
        <li>✅ Pyzbar</li>
      </ul>
    </td>
    <td>
      <h3>🔐 Integração e Autenticação</h3>
      <ul>
        <li>✅ Microsoft Azure (MSAL)</li>
        <li>✅ OAuth2</li>
        <li>✅ Flask-SocketIO</li>
        <li>✅ Requests</li>
        <li>✅ Werkzeug</li>
        <li>✅ itsdangerous</li>
        <li>✅ Tenacity</li>
      </ul>
    </td>
  </tr>
</table>

## 📁 Estrutura do Projeto

```bash
brazil-health-intranet/
├── 📂 static/                # Arquivos estáticos
│   ├── 📂 css/               # Folhas de estilo
│   ├── 📂 js/                # Arquivos JavaScript 
│   ├── 📂 images/            # Imagens
│   ├── 📂 uploads/           # Arquivos enviados pelos usuários
│   └── 📂 data/              # Dados para importação/exportação
├── 📂 templates/             # Templates HTML (mais de 90 templates)
│   ├── 📄 base.html          # Template base do layout
│   ├── 📄 login.html         # Página de login
│   ├── 📄 homepage.html      # Página inicial após login
│   ├── 📄 gestao.html        # Página de gestão administrativa
│   ├── 📄 gestao_chamados.html # Sistema de chamados
│   ├── 📄 create_ticket.html # Criação de tickets
│   ├── 📄 ranking.html       # Páginas de ranking e performance
│   └── 📄 [+80 templates]    # Outros templates específicos
├── 📂 __pycache__/           # Cache de arquivos Python compilados
├── 📂 venv/                  # Ambiente virtual Python
├── 📂 node_modules/          # Módulos Node.js
├── 📂 .vscode/               # Configurações do VS Code
├── 📄 main.py                # Aplicação principal Flask (rotas e lógica)
├── 📄 dependencies.py        # Funções relacionadas ao banco de dados
├── 📄 functions.py           # Funções utilitárias gerais
├── 📄 requirements.txt       # Dependências Python
├── 📄 package.json           # Dependências Node.js
├── 📄 package-lock.json      # Lock das versões do Node.js
├── 📄 .env                   # Variáveis de ambiente
├── 📄 Procfile               # Configuração para deploy
├── 📄 Aptfile                # Dependências de sistema para deploy
├── 📄 .gitignore             # Arquivos ignorados pelo Git
├── 📄 empresas.json          # Dados de empresas em formato JSON
├── 📄 teste.py               # Arquivo para testes
├── 📄 teste.html             # HTML para testes
└── 📄 LICENSE                # Informações de licença
```

## 📋 Pré-requisitos

<div align="center">
  <table>
    <tr>
      <td align="center">
        <img src="https://img.icons8.com/color/48/000000/python.png" alt="Python"/>
        <br />Python 3.12.9
      </td>
      <td align="center">
        <img src="https://img.icons8.com/color/48/000000/nodejs.png" alt="Node.js"/>
        <br />Node.js 20.x
      </td>
      <td align="center">
        <img src="https://img.icons8.com/?size=50&id=38561&format=png&color=000000" alt="PostgreSQL"/>
        <br />PostgreSQL
      </td>
    </tr>
    <tr>
      <td align="center">
        <img src="https://img.icons8.com/color/48/000000/redis.png" alt="Redis"/>
        <br />Redis
      </td>
      <td align="center">
        <img src="https://img.icons8.com/color/48/000000/amazon-web-services.png" alt="AWS S3"/>
        <br />AWS S3
      </td>
      <td align="center">
        <img src="https://img.icons8.com/?size=48&id=2EQJhNzsbRj3&format=png&color=000000" alt="Tesseract OCR"/>
        <br />Tesseract OCR
      </td>
    </tr>
    <tr>
      <td align="center">
        <img src="https://img.icons8.com/fluency/48/000000/pdf.png" alt="Poppler"/>
        <br />Poppler
      </td>
      <td align="center">
        <img src="https://img.icons8.com/color/48/000000/microsoft.png" alt="MSAL"/>
        <br />Microsoft MSAL
      </td>
      <td align="center">
        <img src="https://img.icons8.com/fluency/48/000000/code-file.png" alt="gunicorn"/>
        <br />Gunicorn
      </td>
    </tr>
  </table>
</div>

## 🚀 Instalação

Clone o repositório e configure o ambiente de desenvolvimento:

<details>
<summary><b>📋 Passos Detalhados para Instalação</b></summary>

```bash
# 📥 Clonar o repositório
git clone https://github.com/BrazilHealth/intranet-brazilhealth.git
cd intranet-brazilhealth

# 🔧 Criar e ativar ambiente virtual Python
python -m venv venv

# 🔧 Para Windows:
.\venv\Scripts\activate

# 🔧 Para Linux/Mac:
source venv/bin/activate

# 📦 Instalar dependências Python
pip install -r requirements.txt

# 📦 Instalar dependências Node.js
npm install

# 🖥️ Instalar Tesseract OCR (para Windows)
# Baixe o instalador em: https://github.com/UB-Mannheim/tesseract/wiki
# OU para Linux:
sudo apt-get install tesseract-ocr

# 📄 Instalar Poppler (para processamento de PDFs)
# Para Windows: Baixe em https://github.com/oschwartz10612/poppler-windows/releases
# Para Ubuntu/Debian:
sudo apt-get install poppler-utils
```
</details>

## ⚙️ Configuração

<details>
<summary><b>📝 Configuração de Ambiente (.env)</b></summary>

1. Crie um arquivo `.env` na raiz do projeto com as seguintes variáveis:

```ini
# 🛢️ Configuração do Banco de Dados PostgreSQL
DATABASE=auth_table
HOST=seu_host_postgres
USERSERVER=seu_usuario
PASSWORD=sua_senha
PORT=5432

# 📧 Configuração de Email
EMAIL_REMETENTE=<EMAIL>
EMAIL_SENHA=sua_senha_email
MAIL_SALT=sua_chave_salt
SECRET_KEY=sua_chave_secreta

# 🔑 Microsoft Azure Integration
CLIENT_ID=seu_client_id
CLIENT_SECRET=seu_client_secret
AUTHORITY=https://login.microsoftonline.com/seu_tenant_id
REDIRECT_URI_ONEDRIVE=https://seu_dominio.com/getAToken
SCOPE=Files.ReadWrite.All offline_access User.Read

# ☁️ Configuração AWS S3
AWS_ACCESS_KEY_ID=sua_key_id
AWS_SECRET_ACCESS_KEY=sua_secret_key
S3_BUCKET=seu_bucket_name

# 💾 Configuração Redis
REDIS_URL=redis://localhost:6379

# 🌐 Ambiente Flask
FLASK_ENV=development
```

2. Configuração do PostgreSQL:
   ```sql
   CREATE DATABASE auth_table;
   CREATE USER seu_usuario WITH PASSWORD 'sua_senha';
   GRANT ALL PRIVILEGES ON DATABASE auth_table TO seu_usuario;
   ```

3. Configuração do Redis:
   ```bash
   # Instalação do Redis (Ubuntu/Debian)
   sudo apt-get install redis-server
   
   # Iniciar o serviço Redis
   sudo systemctl start redis-server
   
   # Para Windows, use o Redis no WSL2 ou Redis Windows Port
   ```

4. Configuração do AWS S3:
   - Crie um bucket S3 para armazenar arquivos
   - Crie um usuário IAM com permissões para o bucket
   - Obtenha as credenciais ACCESS_KEY_ID e SECRET_ACCESS_KEY
</details>

## ▶️ Executando o Projeto

Para iniciar o servidor de desenvolvimento:

```bash
# Definir a variável de ambiente FLASK_APP
set FLASK_APP=main.py  # Windows
export FLASK_APP=main.py  # Linux/Mac

# Executar o servidor de desenvolvimento
flask run
```

> 🌐 O aplicativo estará disponível em `http://localhost:5000`

## 📦 Módulos Principais

<div align="center">
  <table>
    <tr>
      <td align="center"><img src="https://img.icons8.com/?size=50&id=pGvuUPsPlO0O&format=png&color=000000"/><br/><b>Gestão de Usuários</b><br/><small>Cadastro, autenticação e gerenciamento de perfis</small></td>
      <td align="center"><img src="https://img.icons8.com/fluency/48/000000/commercial.png"/><br/><b>CRM</b><br/><small>Gestão de corretores, assistentes e operadoras</small></td>
      <td align="center"><img src="https://img.icons8.com/fluency/48/000000/service.png"/><br/><b>Chamados</b><br/><small>Sistema de emissão e acompanhamento de tickets</small></td>
    </tr>
    <tr>
      <td align="center"><img src="https://img.icons8.com/fluency/48/000000/money.png"/><br/><b>Financeiro</b><br/><small>Adiantamentos, reembolsos e comissões</small></td>
      <td align="center"><img src="https://img.icons8.com/fluency/48/000000/dashboard.png"/><br/><b>Análise de Desempenho</b><br/><small>Dashboards e relatórios</small></td>
      <td align="center"><img src="https://img.icons8.com/fluency/48/000000/key.png"/><br/><b>Gestão de Acessos</b><br/><small>Controle de acesso a portais</small></td>
    </tr>
    <tr>
      <td align="center" colspan="3"><img src="https://img.icons8.com/fluency/48/000000/conference-call.png"/><br/><b>Recursos Humanos</b><br/><small>Processos seletivos, benefícios e documentos</small></td>
    </tr>
  </table>
</div>

## ☁️ Deploy

### 🔄 Fluxo de Branches (Git Flow)

A estratégia de branches deste projeto segue o modelo Git Flow, com duas branches principais:

<div align="center">
  <img src="https://wac-cdn.atlassian.com/dam/jcr:************************affa053d9ea7/04%20(1).svg?cdnVersion=1091" alt="Git Flow" width="600"/>
</div>

<details>
<summary><b>📋 Estrutura de Branches</b></summary>

- `main` - Branch de produção, sempre estável e pronta para deploy
- `develop` - Branch de desenvolvimento e integração
- `feature/*` - Branches para desenvolvimento de novas funcionalidades
- `fix/*` - Branches para correção de bugs
- `release/*` - Branches para preparação de uma nova release
- `hotfix/*` - Branches para correções urgentes em produção

</details>

<details>
<summary><b>📋 Fluxo de Trabalho</b></summary>

1. **Desenvolvimento de nova funcionalidade:**
   ```bash
   # Criar branch a partir da develop no mesmo repositório
   git checkout develop
   git checkout -b feature/nova-funcionalidade
   
   # Trabalhar na funcionalidade...
   
   # Ao concluir, mesclar na develop via Pull Request
   git push origin feature/nova-funcionalidade
   # Crie PR da branch feature/nova-funcionalidade para develop no mesmo repositório
   ```

2. **Ambiente de QA:**
   - Após o merge na branch `develop`, a aplicação é implantada automaticamente no ambiente de QA
   - Testes são realizados neste ambiente usando a branch develop

3. **Release para produção:**
   ```bash
   # Quando develop estiver pronta para produção
   # Crie PR da branch develop para main no mesmo repositório
   # Não é necessário clonar ou mudar de repositório, apenas mudar de branch
   ```

4. **Hotfixes (correções emergenciais):**
   ```bash
   # Em caso de bugs críticos em produção
   git checkout main
   git checkout -b hotfix/correcao-urgente
   
   # Após a correção
   git push origin hotfix/correcao-urgente
   # Crie PR da branch hotfix/correcao-urgente para main e develop no mesmo repositório
   ```
</details>

### 🚀 Ambientes de Deploy

<details>
<summary><b>📋 Ambientes disponíveis</b></summary>

| Ambiente | Branch | URL | Descrição |
|----------|--------|-----|-----------|
| Produção | `main` | https://www.brh-intranet.com.br/ | Ambiente de produção (mesmo repositório) |
| QA | `develop` | https://intranet-brazilhealth-qa-325e0317d57d.herokuapp.com/ | Ambiente de testes (mesmo repositório) |
| Desenvolvimento | `feature/*` | Localhost | Ambiente local (mesmo repositório) |

> **Nota:** Todos os ambientes utilizam o mesmo repositório Git, diferenciando-se apenas pela branch que está sendo utilizada em cada um.

</details>

<details>
<summary><b>📋 Processo de Deploy</b></summary>

1. **Deploy para QA (automático):**
   - Cada merge na branch `develop` do repositório aciona automaticamente um deploy para o ambiente de QA
   - Este deploy inclui testes automatizados e verificação de qualidade do código

2. **Deploy para Produção:**
   - Após aprovação do PR de `develop` para `main` (no mesmo repositório), o deploy para produção é iniciado
   - O processo inclui:
     - Backup do banco de dados
     - Deploy do código da branch main
     - Execução de migrações de banco de dados
     - Reinicialização dos serviços
   
3. **Rollback (em caso de problemas):**
   ```bash
   # Em caso de problemas após deploy
   git revert [hash-do-commit-problemático]
   git push origin main  # Ou develop, dependendo do ambiente
   # ou usar o painel de controle do provedor de hospedagem para reverter para a versão anterior
   ```
</details>

## 🤝 Diretrizes de Contribuição

### 🌿 Branching

<details>
<summary><b>Estratégia de Branches</b></summary>

1. Crie uma branch para cada nova feature ou correção:
   ```bash
   git checkout -b feature/nome-da-feature
   git checkout -b fix/nome-do-bug
   ```

2. Mantenha sua branch atualizada com a main:
   ```bash
   git pull origin main
   git rebase main
   ```
</details>

### 💬 Commits

<details>
<summary><b>Padrão de Commits Semânticos</b></summary>

Siga o padrão de commits semânticos:

```
feat: adiciona novo recurso
fix: corrige um bug
docs: alterações na documentação
style: formatação, ponto e vírgula faltando; não altera o código
refactor: refatoração do código de produção
test: adicionando testes, refatorando testes; não altera o código de produção
chore: atualizando tarefas de build, configurações do gerenciador de pacotes
```
</details>

### 🔄 Pull Requests

<details>
<summary><b>Diretrizes para PRs</b></summary>

1. Abra o PR utilizando a interface do GitHub
2. Forneça uma descrição clara do que foi implementado ou corrigido
3. Vincule o PR às issues relacionadas
4. Solicite revisão de pelo menos um membro da equipe
</details>

## ⚖️ Licença

<div align="center">
  <img src="https://img.icons8.com/?size=50&id=119277&format=png&color=000000"/>
  <p><b>Copyright © 2024 Brazil Health. Todos os direitos reservados.</b></p>
</div>

Este software é proprietário e confidencial. A utilização, redistribuição, modificação, ou qualquer outro uso deste código ou software, total ou parcial, não é permitido sem autorização expressa por escrito do proprietário. 