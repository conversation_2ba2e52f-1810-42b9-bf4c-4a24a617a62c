# Ignorar o arquivo de variáveis de ambiente
.env

# Ignorar o diretório do ambiente virtual
.venv/
venv/

# Ignorar o diretório de cache do Python
_pycache_/

# Ignorar dependências do Node.js
node_modules/

# Ignorar arquivos de pacotes do Node.js
npm-debug.log
yarn-error.log

# Ignorar logs do Python
*.log

# Ignorar arquivos de compilação do Python
*.pyc
*.pyo
*.pyd

# Ignorar arquivos de cache do Python
*.cache

# Ignorar diretórios de build do Python
build/
dist/

# Ignorar pacotes instalados no local (pip)
*.egg-info/

# Ignorar configurações do editor de texto/IDE
.vscode/
.idea/

# Ignorar arquivos do sistema operacional
.DS_Store
Thumbs.db

# Ignorar arquivos de teste (se não quiser incluir no repositório)
test/

# Ignorar configuração de versões do Node.js
.nvmrc

# Ignorar configurações de workspace do Visual Studio Code
*.code-workspace

# Ignorar arquivos temporários
*.tmp
*.swp

# Ignorar arquivos de build de frontend (se aplicável)
static/build/

# Ignorar o arquivo de teste
teste.py
teste.html
rules.md