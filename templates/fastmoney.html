<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitação de Adiantamento</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <style>
        /* Custom styles to enhance the form */
        .input,
        .file-input {
            background-color: #F5F5F5;
            border: 1px solid #D1D1D1;
            box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
            color: black;
        }

        .input:focus,
        .file-input:focus {
            border-color: #00D1B2;
            box-shadow: 0px 0px 0px 2px rgba(0, 209, 178, 0.25);
        }

        .input[readonly],
        .file-input[readonly] {
            background-color: #E0E0E0;
            cursor: default;
            color: #4A4A4A;
        }

        .file-input {
            cursor: pointer;
        }

        .field.is-grouped {
            margin-bottom: 1.5rem;
        }

        /* Custom styles for the "Enviar Solicitação" button */
        .button.is-primary {
            background-color: #FF5722;
            border-color: transparent;
        }

        .button.is-primary:hover {
            background-color: #E64A19;
        }

        .formulariofast,
        .radio {
            color: black;
        }

        .has-icons-left .file-input {
            padding-left: 2.5em;
        }

        /* Hidden by default */
        .hidden {
            display: none;
        }

        .colaborador .dropdown {
            display: block !important;
        }

        .colaborador .pn-container img.home-header-image {
            margin-top: 0 !important;
        }
    </style>
</head>

<body class="fastmoney-page">

    <form id="form-adiantamento" action="{{ url_for('process_fastmoney') }}" method="POST"
        enctype="multipart/form-data">
        <!-- Tipo de Adiantamento com Radio Buttons -->
        <div class="field">
            <label class="radio">
                <input type="radio" id="fast_money" name="tipo_adiantamento" value="fast_money" required>
                Adiantamento de Comissão (Fast-Money)
            </label>
        </div>
        <div class="field">
            <label class="radio">
                <input type="radio" id="primeira_parcela" name="tipo_adiantamento" value="primeira_parcela" required>
                Adiantamento de 1ª Parcela
            </label>
        </div>

        <!-- Primeira Linha -->
        <div class="columns">
            <div class="column">
                <div class="field">
                    <label class="formulariofast" for="codigo_proposta">Código da Proposta</label>
                    <div class="control">
                        <input class="input" type="text" id="codigo_proposta" name="codigo_proposta" required>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="field">
                    <label class="formulariofast" for="corretor">Corretor</label>
                    <div class="control">
                        <input class="input" type="text" id="corretor" name="corretor" readonly required>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="field">
                    <label class="formulariofast" for="supervisor">Supervisor</label>
                    <div class="control">
                        <input class="input" type="text" id="supervisor" name="supervisor" readonly required>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="field">
                    <label class="formulariofast" for="modalidade">Modalidade</label>
                    <div class="control">
                        <input class="input" type="text" id="modalidade" name="modalidade" readonly required>
                    </div>
                </div>
            </div>
        </div>

        <!-- Segunda Linha -->
        <div class="columns">
            <div class="column">
                <div class="field">
                    <label class="formulariofast" for="operadora">Operadora</label>
                    <div class="control">
                        <input class="input" type="text" id="operadora" name="operadora" readonly required>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="field">
                    <label class="formulariofast" for="subproduto">Sub Produto</label>
                    <div class="control">
                        <input class="input" type="text" id="subproduto" name="subproduto" readonly required>
                    </div>
                </div>
            </div>
        </div>

        <!-- Terceira Linha -->
        <div class="columns">
            <div class="column">
                <div class="field">
                    <label class="formulariofast" for="segurado">Segurado</label>
                    <div class="control">
                        <input class="input" type="text" id="segurado" name="segurado" readonly required>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="field">
                    <label class="formulariofast" for="valor_proposta">Valor da Proposta (R$)</label>
                    <div class="control">
                        <input class="input" type="number" id="valor_proposta" name="valor_proposta" step="0.01"
                            readonly required>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quarta Linha -->
        <div class="columns">
            <div class="column">
                <div class="field">
                    <label class="formulariofast" for="acordo">Acordo</label>
                    <div class="control">
                        <input class="input" type="text" id="acordo" name="acordo" readonly required>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="field">
                    <label class="formulariofast" for="tabela_padrao">Tabela Padrão</label>
                    <div class="control">
                        <input class="input" type="text" id="tabela_padrao" name="tabela_padrao" readonly required>
                    </div>
                </div>
            </div>
        </div>

        <!-- Seção para Comprovante -->
        <div class="field">
            <p class="formulariofast">Possui Comprovante?</p>
            <label class="radio">
                <input type="radio" id="comprovante_sim" name="tem_comprovante" value="sim" required>
                Sim, possuo comprovante
            </label>
            <label class="radio">
                <input type="radio" id="comprovante_nao" name="tem_comprovante" value="nao" required>
                Não possuo comprovante
            </label>
        </div>

        <!-- Campo de Upload de Comprovante e Boleto (inicialmente ocultos) -->
        <div id="upload-section" class="hidden">
            <div class="columns">
                <div class="column">
                    <div class="field has-icons-left">
                        <label class="formulariofast" for="comprovante">Anexar Comprovante</label>
                        <div class="control">
                            <input class="file-input" type="file" id="comprovante" name="comprovante"
                                accept=".pdf, .jpg, .png">
                            <span class="icon is-left">
                                <i class="fas fa-upload"></i>
                            </span>
                        </div>
                        <p id="comprovante-name" class="has-text-info"></p>
                        <button type="button" id="remove-comprovante"
                            class="button is-small is-danger hidden">Remover</button>
                    </div>
                </div>
                <div class="column">
                    <div class="field has-icons-left">
                        <label class="formulariofast" for="boleto">Anexar Boleto</label>
                        <div class="control">
                            <input class="file-input" type="file" id="boleto" name="boleto" accept=".pdf, .jpg, .png">
                            <span class="icon is-left">
                                <i class="fas fa-file-invoice"></i>
                            </span>
                        </div>
                        <p id="boleto-name" class="has-text-info"></p>
                        <button type="button" id="remove-boleto"
                            class="button is-small is-danger hidden">Remover</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="loadingScreen" class="loading-screen" style="display: none;">
            <div class="spinner"></div>
        </div>

        <!-- Botão de Envio -->
        <div class="field">
            <div class="control">
                <button type="submit" class="button is-primary">Enviar Solicitação</button>
            </div>
        </div>
    </form>

</body>

</html>