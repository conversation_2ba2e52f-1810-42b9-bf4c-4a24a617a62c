<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intranet | Boletos de Corretores</title>
    <!-- Importação do Bulma -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.3/css/bulma.min.css">
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</head>
<style>
    .colaborador .dropdown {
        display: block !important;
    }

    .colaborador .pn-container img.home-header-image {
        margin-top: 0 !important;
    }

    /* Estilo para o campo de senha falso */
    .fake-field {
        position: absolute;
        top: -999px;
        left: -999px;
        opacity: 0;
    }
</style>

<body class="gestaoCorretores">

    <div class="container">
        <p>Preencha o código da proposta para buscar em seguida faça o upload do boleto.</p>

        <!-- Campos invisíveis para enganar o autocomplete -->
        <input type="text" name="username" class="fake-field" aria-hidden="true">
        <input type="password" name="password" class="fake-field" aria-hidden="true">

        <!-- Formulário para Preenchimento de Dados via API -->
        <form id="formulario-proposta" method="POST" action="/enviar_solicitacao" enctype="multipart/form-data"
            autocomplete="new-form">
            <!-- Primeira Linha -->
            <div class="columns">
                <div class="column is-one-quarter">
                    <div class="field">
                        <label class="label" for="codigo_proposta">Código da Proposta</label>
                        <div class="control">
                            <input class="input" type="text" id="codigo_proposta" name="codigo_proposta"
                                autocomplete="off" required>
                        </div>
                    </div>
                </div>
                <div class="column is-one-quarter">
                    <div class="field">
                        <label class="label" for="corretor">Corretor</label>
                        <input class="input is-readonly" type="text" id="corretor" name="corretor" readonly
                            autocomplete="new-corretor" required>
                    </div>
                </div>
                <div class="column is-one-quarter">
                    <div class="field">
                        <label class="label" for="supervisor">Supervisor</label>
                        <div class="control">
                            <input class="input is-readonly" type="text" id="supervisor" name="supervisor" readonly
                                autocomplete="off" required>
                        </div>
                    </div>
                </div>
                <div class="column is-one-quarter">
                    <div class="field">
                        <label class="label" for="modalidade">Modalidade</label>
                        <div class="control">
                            <input class="input is-readonly" type="text" id="modalidade" name="modalidade" readonly
                                autocomplete="off" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Segunda Linha -->
            <div class="columns">
                <div class="column is-half">
                    <div class="field">
                        <label class="label" for="operadora">Operadora</label>
                        <div class="control">
                            <input class="input is-readonly" type="text" id="operadora" name="operadora" readonly
                                autocomplete="off" required>
                        </div>
                    </div>
                </div>
                <div class="column is-half">
                    <div class="field">
                        <label class="label" for="subproduto">Sub Produto</label>
                        <div class="control">
                            <input class="input is-readonly" type="text" id="subproduto" name="subproduto" readonly
                                autocomplete="off" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Terceira Linha -->
            <div class="columns">
                <div class="column is-half">
                    <div class="field">
                        <label class="label" for="segurado">Segurado</label>
                        <div class="control">
                            <input class="input is-readonly" type="text" id="segurado" name="segurado" readonly
                                autocomplete="off" required>
                        </div>
                    </div>
                </div>
                <div class="column is-half">
                    <div class="field">
                        <label class="label" for="valor_proposta">Valor da Proposta (R$)</label>
                        <div class="control">
                            <input class="input is-readonly" type="number" id="valor_proposta" name="valor_proposta"
                                step="0.01" readonly autocomplete="off" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quarta Linha -->
            <div class="columns">
                <div class="column">
                    <div class="field">
                        <label class="label" for="acordo">Acordo</label>
                        <div class="control">
                            <input class="input" type="text" id="acordo" name="acordo" readonly autocomplete="off"
                                required>
                        </div>
                    </div>
                </div>
                <div class="column">
                    <div class="field">
                        <label class="label" for="tabela_padrao">Tabela Padrão</label>
                        <div class="control">
                            <input class="input" type="text" id="tabela_padrao" name="tabela_padrao" readonly
                                autocomplete="off" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upload do boleto e campo de código de barras -->
            <div class="columns">
                <div class="column is-half">
                    <div class="field">
                        <label class="label" for="boleto_nome">Upload do Boleto</label>
                        <div class="file has-name is-fullwidth">
                            <label class="file-label">
                                <input class="file-input" type="file" id="boleto" name="boleto"
                                    accept=".pdf,.png,.jpg,.jpeg" onchange="uploadBoleto()">
                                <span class="file-cta">
                                    <span class="icon">
                                        <i class="fas fa-upload"></i>
                                    </span>
                                    <span>Escolher Arquivo</span>
                                </span>
                                <span id="file-name" class="file-name">Nenhum arquivo selecionado</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="column is-half">
                    <div class="field">
                        <label class="label" for="codigo_barras">Código de Barras</label>
                        <div class="control">
                            <input class="input" type="text" id="codigo_barras" name="codigo_barras"
                                autocomplete="false" data-lpignore="true" data-form-type="other" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campo para senha de boletos -->
            <div class="columns">
                <div class="column is-half">
                    <div class="field">
                        <label class="label" for="boletoSenha">Senha do Boleto (se aplicável)</label>
                        <div class="control">
                            <input class="input" type="text" id="boletoSenha" name="boleto_senha" autocomplete="false"
                                data-lpignore="true" data-form-type="other">
                        </div>
                    </div>
                </div>
                <!-- Botão de Enviar Solicitação -->
                <div class="field is-grouped is-grouped-right">
                    <p class="control">
                        <button type="submit" class="button is-warning">Enviar Solicitação</button>
                    </p>
                </div>
            </div>
        </form>

        <hr>
    </div>

    <div id="loadingScreen" class="loading-screen" style="display: none;">
        <div class="spinner"></div>
    </div>

    <!-- Modal para digitar o código de barras manualmente -->
    <div id="barcodeModal" class="modal">
        <div class="modal-background"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">Digite o Código de Barras</p>
                <button class="delete" aria-label="close" onclick="closeBarcodeModal()"></button>
            </header>
            <section class="modal-card-body">
                <div class="field">
                    <label class="label" for="manualBarcodeInput">Código de Barras</label>
                    <div class="control">
                        <input class="input" type="text" id="manualBarcodeInput" autocomplete="off" data-lpignore="true"
                            data-form-type="other" required>
                    </div>
                </div>
            </section>
            <footer class="modal-card-foot">
                <button class="button is-success" onclick="saveBarcode()">Salvar</button>
                <button class="button" onclick="closeBarcodeModal()">Cancelar</button>
            </footer>
        </div>
    </div>

    <!-- Modal para solicitar a senha do boleto -->
    <div class="modal fade" id="senhaModal" tabindex="-1" role="dialog" aria-labelledby="senhaModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="senhaModalLabel">Senha do Boleto</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="senhaForm" autocomplete="off">
                        <div class="form-group">
                            <label for="senha">Digite a senha do boleto</label>
                            <input type="password" class="form-control" id="senha" name="senha" autocomplete="off"
                                data-lpignore="true" data-form-type="other" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Enviar</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para solicitar o código de barras manualmente -->
    <div class="modal fade" id="codigoBarrasModal" tabindex="-1" role="dialog" aria-labelledby="codigoBarrasModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="codigoBarrasModalLabel">Código de Barras</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="codigoBarrasForm" autocomplete="off">
                        <div class="form-group">
                            <label for="codigo_barras_manual">Digite o código de barras</label>
                            <input type="text" class="form-control" id="codigo_barras_manual"
                                name="codigo_barras_manual" autocomplete="off" data-lpignore="true"
                                data-form-type="other" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Enviar</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Link da FontAwesome -->
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>

    <script>
        // Função para gerenciar o nome do arquivo selecionado para upload do boleto
        function uploadBoleto() {
            const boletoInput = document.getElementById('boleto');
            const fileNameSpan = document.getElementById('file-name');

            if (boletoInput.files.length > 0) {
                fileNameSpan.textContent = boletoInput.files[0].name;
            } else {
                fileNameSpan.textContent = 'Nenhum arquivo selecionado';
            }
        }

        // Remover atributo readonly nos campos quando a página carregar
        document.addEventListener("DOMContentLoaded", function () {
            document.getElementById('codigo_barras').removeAttribute('readonly');
            document.getElementById('boletoSenha').removeAttribute('readonly');
        });

        // Funções para abrir e fechar o modal de código de barras
        function openBarcodeModal() {
            document.getElementById('barcodeModal').classList.add('is-active');
        }

        function closeBarcodeModal() {
            document.getElementById('barcodeModal').classList.remove('is-active');
        }

        // Função para salvar o código de barras digitado manualmente
        function saveBarcode() {
            const manualBarcodeInput = document.getElementById('manualBarcodeInput').value;
            document.getElementById('codigo_barras').value = manualBarcodeInput;
            closeBarcodeModal();
        }

        // Função para abrir o modal quando necessário
        function solicitarSenha() {
            $('#senhaModal').modal('show');
        }

        // Enviar a senha para o backend
        $('#senhaForm').on('submit', function (event) {
            event.preventDefault();
            var senha = $('#senha').val();
            var formData = new FormData();
            formData.append('file', $('#boleto')[0].files[0]);
            formData.append('senha', senha);

            $.ajax({
                url: '/upload_boleto',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    // Lógica de sucesso
                    console.log(response);
                },
                error: function (response) {
                    // Lógica de erro
                    if (response.responseJSON && response.responseJSON.solicitar_senha) {
                        solicitarSenha();
                    } else {
                        console.error(response);
                    }
                }
            });
        });
    </script>

    </div>

</body>

</html>