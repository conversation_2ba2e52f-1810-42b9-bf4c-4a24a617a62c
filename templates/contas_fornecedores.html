<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Fornecedores</title>
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/css/adminlte.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .feedback-container {
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1050;
            width: 90%;
            max-width: 600px;
            pointer-events: none;
            /* Evita cliques acidentais */
        }

        .feedback-container .alert {
            pointer-events: all;
            /* Permite fechar o alerta */
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>

<body class="hold-transition sidebar-mini">
    <div id="feedback-container" class="feedback-container"></div>
    <div class="wrapper">
        <div class="content-wrapper">
            <div class="wrapper">
                <!-- Exibir mensagens de flash -->
                <div class="container mt-3">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                    {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    {% endfor %}
                    {% endif %}
                    {% endwith %}
                </div>
                <div class="container">
                    <!-- Header -->
                    <section class="content-header">
                        <div class="container-fluid">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <h1>Gestão de Fornecedores</h1>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Main Content -->
                    <section class="content">
                        <div class="container-fluid">
                            <div class="card card-primary">
                                <div class="card-header">
                                    <h3 class="card-title">Gerenciar Fornecedores</h3>
                                </div>
                                <div class="card-body">
                                    <ul class="nav nav-tabs" id="tabFornecedores" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="tab-cadastrar-fornecedor" data-toggle="tab"
                                                href="#cadastrar-fornecedor" role="tab"
                                                aria-controls="cadastrar-fornecedor" aria-selected="true">
                                                <i class="fas fa-plus-circle"></i> Cadastrar Fornecedor
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="tab-listar-fornecedores" data-toggle="tab"
                                                href="#listar-fornecedores" role="tab"
                                                aria-controls="listar-fornecedores" aria-selected="false">
                                                <i class="fas fa-list"></i> Fornecedores Cadastrados
                                            </a>
                                        </li>
                                    </ul>
                                    <div class="tab-content" id="tabContentFornecedores">
                                        <!-- Aba Cadastrar Fornecedor -->
                                        <div class="tab-pane fade show active" id="cadastrar-fornecedor" role="tabpanel"
                                            aria-labelledby="tab-cadastrar-fornecedor">
                                            <div class="card mt-3">
                                                <div class="card-header bg-primary text-white">
                                                    <h3 class="card-title">Cadastrar Novo Fornecedor</h3>
                                                </div>
                                                <form id="form-fornecedores">
                                                    <div class="card-body">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="nome">Nome</label>
                                                                    <input type="text" class="form-control" id="nome"
                                                                        name="nome"
                                                                        placeholder="Digite o nome do fornecedor"
                                                                        required>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="cnpj_cpf">CNPJ/CPF</label>
                                                                    <input type="text" class="form-control"
                                                                        id="cnpj_cpf" name="cnpj_cpf"
                                                                        placeholder="Digite o CNPJ ou CPF do fornecedor"
                                                                        required>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="endereco">Endereço</label>
                                                                    <input type="text" class="form-control"
                                                                        id="endereco" name="endereco"
                                                                        placeholder="Digite o endereço do fornecedor"
                                                                        required>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="telefone">Telefone</label>
                                                                    <input type="text" class="form-control"
                                                                        id="telefone" name="telefone"
                                                                        placeholder="Digite o telefone do fornecedor"
                                                                        required>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="email">Email</label>
                                                                    <input type="email" class="form-control" id="email"
                                                                        name="email"
                                                                        placeholder="Digite o email do fornecedor"
                                                                        required>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="categoria">Categoria</label>
                                                                    <select class="form-control" id="categoria"
                                                                        name="categoria" required>
                                                                        <option value="">Selecione a categoria</option>
                                                                        <option value="servicos">Serviços</option>
                                                                        <option value="produtos">Produtos</option>
                                                                        <option value="outros">Outros</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="website">Website</label>
                                                                    <input type="text" class="form-control" id="website"
                                                                        name="website"
                                                                        placeholder="Digite o website do fornecedor">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="observacoes">Observações</label>
                                                                    <textarea class="form-control" id="observacoes"
                                                                        name="observacoes"
                                                                        placeholder="Digite observações sobre o fornecedor"
                                                                        rows="3"></textarea>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="card-footer text-right">
                                                        <button id="botaoCadastrarFornecedor" type="button"
                                                            class="btn btn-primary">Cadastrar</button>
                                                        <button type="reset" class="btn btn-secondary">Limpar</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>

                                        <!-- Aba Listar Fornecedores -->
                                        <div class="tab-pane fade" id="listar-fornecedores" role="tabpanel"
                                            aria-labelledby="tab-listar-fornecedores">
                                            <div class="card mt-3">
                                                <div class="card-header bg-secondary text-white">
                                                    <h3 class="card-title">Fornecedores Cadastrados</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <select id="filtro-nome-fornecedor"
                                                                    class="form-control">
                                                                    <option value="">Todos os Fornecedores</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="table-responsive">
                                                        <table class="table table-bordered table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>ID</th>
                                                                    <th>Nome</th>
                                                                    <th>CNPJ/CPF</th>
                                                                    <th>Endereço</th>
                                                                    <th>Telefone</th>
                                                                    <th>Email</th>
                                                                    <th>Categoria</th>
                                                                    <th>Website</th>
                                                                    <th>Observações</th>
                                                                    <th>Ações</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="fornecedores-list">
                                                                <!-- Os dados serão inseridos aqui dinamicamente -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Modal Editar Fornecedor -->
                                        <div class="modal fade" id="modalEditarFornecedor" tabindex="-1" role="dialog"
                                            aria-labelledby="modalEditarFornecedorLabel" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="modalEditarFornecedorLabel">Editar
                                                            Fornecedor</h5>
                                                        <button type="button" class="close" data-dismiss="modal"
                                                            aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <form id="form-editar-fornecedor">
                                                            <input type="hidden" id="editar-fornecedor-id">
                                                            <div class="form-group">
                                                                <label for="editar-nome-fornecedor">Nome</label>
                                                                <input type="text" class="form-control"
                                                                    id="editar-nome-fornecedor" name="nome" required>
                                                            </div>
                                                            <div class="form-group">
                                                                <label
                                                                    for="editar-documento-fornecedor">Documento</label>
                                                                <input type="text" class="form-control date-picker"
                                                                    id="editar-documento-fornecedor" name="cnpj_cpf"
                                                                    required>
                                                            </div>
                                                            <div class="form-group">
                                                                <label for="editar-endereco-fornecedor">Endereço</label>
                                                                <input type="text" class="form-control"
                                                                    id="editar-endereco-fornecedor" name="endereco">
                                                            </div>
                                                            <div class="form-group">
                                                                <label for="editar-telefone-fornecedor">Telefone</label>
                                                                <input type="text" class="form-control"
                                                                    id="editar-telefone-fornecedor" name="telefone"
                                                                    required>
                                                            </div>
                                                            <div class="form-group">
                                                                <label for="editar-email-fornecedor">E-mail</label>
                                                                <input type="text" class="form-control"
                                                                    id="editar-email-fornecedor" name="email" required>
                                                            </div>
                                                            <div class="form-group">
                                                                <label
                                                                    for="editar-categoria-fornecedor">Categoria</label>
                                                                <input type="text" class="form-control"
                                                                    id="editar-categoria-fornecedor" name="categoria"
                                                                    required>
                                                            </div>
                                                            <div class="form-group">
                                                                <label for="editar-site-fornecedor">Website</label>
                                                                <input type="text" class="form-control"
                                                                    id="editar-site-fornecedor" name="site">
                                                            </div>
                                                            <div class="form-group">
                                                                <label
                                                                    for="editar-observacoes-fornecedor">Observações</label>
                                                                <textarea class="form-control"
                                                                    id="editar-observacoes-fornecedor"
                                                                    name="observacoes" rows="3"></textarea>
                                                            </div>
                                                        </form>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-dismiss="modal">Cancelar</button>
                                                        <button id="salvar-editar-fornecedor"
                                                            class="btn btn-primary">Salvar</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>

        <!-- Flash Messages Container -->
        <div class="flash-message-container">
            {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            {% endfor %}
            {% endif %}
            {% endwith %}
        </div>


        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="spinner-border" role="status">
                <span class="sr-only">Carregando...</span>
            </div>
        </div>

        <!-- AdminLTE JS -->
        <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/js/adminlte.min.js"></script>
        <!-- JQuery -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
        <!-- Custom JS -->
        <script src="/static/js/contas_home.js"></script>
</body>

</html>