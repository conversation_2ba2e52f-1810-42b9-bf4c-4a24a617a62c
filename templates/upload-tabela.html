{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}uploadTabela{% endblock %}

{% block content %}
<div class="uploadTabela-container">
    <h1 class="upload-h1">Gestão - Tabela de Comissões</h1>

    <!-- Formulário de Upload -->
    <div class="upload-container">
        <form id="upload-form" action="/upload" method="post" enctype="multipart/form-data">
            <h3 class="upload-h3">Faça o upload da nova tabela de comissão ou atualize uma já existente.</h3>
            <div class="form-group">
                <label for="modalidade">Modalidade:</label>
                <select id="modalidade" name="modalidade" class="form-control" required>
                    <option value="adesao">Adesão</option>
                    <option value="individual">Individual</option>
                    <option value="empresarial">Empresarial</option>
                    <option value="familiar">Familiar</option>
                    <option value="familiar-odonto">Familiar Odonto</option>
                    <option value="individual-odonto">Individual Odonto</option>
                    <option value="pme">PME</option>
                    <option value="pme-odonto">PME Odonto</option>
                    <option value="vida">Vida</option>
                </select>
            </div>

            <!-- Novo Select para Comissionamento -->
            <div class="form-group">
                <label for="ordem">Comissionamento:</label>
                <select id="ordem" name="ordem" class="form-control" required>
                    <option value="Total">TOTAL</option>
                    <option value="brh-solution">BRH SOLUTION</option>
                    <option value="confiance">CONFIANCE</option>
                    <option value="co-working">CO-WORKING</option>
                    <option value="ext-susep">EXT-SUSEP</option>
                    <option value="franquia-2021">FRANQUIA 2021</option>
                    <option value="luanca-brh">LUANCA-BRH</option>
                    <option value="yolo">YOLO</option>
                </select>
            </div>

            <div class="form-group">
                <label for="file">Arquivo Excel:</label>
                <input type="file" id="file" name="file" class="form-control" required>
            </div>
            <button type="submit" class="btn btn-primary">Upload</button>
        </form>
    </div>

    <div id="loadingScreen" class="loading-screen" style="display: none;">
        <div class="spinner"></div>
    </div> 
</div>

{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/upload-comissao.js') }}"></script>
{% endblock %}
