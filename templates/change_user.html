{% extends "base.html" %}

{% block title %}Alterar Senha - Intranet | BrazilHealth{% endblock %}

{% block body_class %}change-user{% endblock %}

{% block content %}
<div class="editUser-container">
    <div class="container-users">
        <h1>Editar Usuário</h1>
        <a href="/admin" class="btn-voltar" style="margin-left: 95.8%;">Voltar</a>
        <form id="updateUserForm" action="/atualizar_usuario" method="post">
            <!-- O valor do user_id é inserido aqui -->
            <input type="hidden" id="editUserId" name="user_id" value="{{ user.id }}">

            <div class="user-info">
                <span id="user-id">
                    ID do Usuário: <span id="idUser">
                        {{ user.id }}
                    </span>
                </span>
                <div id="editAtivo">
                    <label for="ativo">Ativo:</label>
                    <input type="hidden" name="ativo" value="off">
                    <input type="checkbox" id="ativo" name="ativo" value="on" {% if user.ativo %}checked{% endif %}>
                </div>
            </div>

            <div class="form-group">
                <label for="editNome">Nome:</label>
                <!-- O valor do nome é inserido aqui -->
                <input type="text" id="editNome" name="nome" value="{{ user.nome }}" required>
            </div>

            <div class="form-group">
                <label for="editCPF">CPF:</label>
                <!-- O valor do CPF é inserido aqui -->
                <input type="text" id="editCPF" name="cpf" value="{{ user.cpf }}" required>
            </div>

            <div class="form-group">
                <label for="editEmail">E-mail:</label>
                <!-- O valor do email é inserido aqui -->
                <input type="email" id="editEmail" name="email" value="{{ user.email }}" required>
            </div>

            <div class="form-group">
                <label for="editDataNascimento">Data de Nascimento:</label>
                <!-- O valor da data de nascimento é inserido aqui -->
                <input type="date" id="editDataNascimento" name="data_nascimento" value="{{ user.data_nascimento }}"
                    required>
            </div>

            <div class="form-group">
                <label for="editTel">Telefone:</label>
                <!-- O valor do telefone é inserido aqui -->
                <input type="text" id="editTel" name="telefone" value="{{ user.telefone }}" required>
            </div>

            <div class="form-group">
                <label for="editSenha">Senha:</label>
                <div class="input-group-register">
                    <input type="password" id="senha" name="senha">
                    <span id="togglePassword" class="fa fa-eye input-group-admin-addon"></span>
                </div>
            </div>
            <div id="senha-reqs" style="margin-top: 10px;">
                <p id="req-length" style="color: #555;">Deve conter 6 a 64 caracteres</p>
                <p id="req-letter" style="color: #555;">Deve conter ao menos uma letra</p>
                <p id="req-number" style="color: #555;">Deve conter ao menos um número</p>
                <p id="req-special" style="color: #555;">Deve conter ao menos um caracter especial (!@#$%&*)</p>
            </div>

            <div class="form-group">
                <label for="editTipoUsuario">Tipo de Usuário:</label>
                <select id="editTipoUsuario" name="tipo_usuario" required>
                    <option value="" {% if user.tipo_usuario is none %}selected{% endif %}></option>
                    <option value="1" {{ 'selected' if user.tipo_usuario==1 }}>Master</option>
                    <option value="2" {{ 'selected' if user.tipo_usuario==2 }}>Sócio</option>
                    <option value="3" {{ 'selected' if user.tipo_usuario==3 }}>Superintendente</option>
                    <option value="4" {{ 'selected' if user.tipo_usuario==4 }}>Supervisor</option>
                    <option value="5" {{ 'selected' if user.tipo_usuario==5 }}>Funcionário</option>
                    <option value="6" {{ 'selected' if user.tipo_usuario==6 }}>Franqueado</option>
                    <option value="7" {{ 'selected' if user.tipo_usuario==7 }}>Dev</option>
                </select>
            </div>

            <div class="form-group">
                <label for="editUnidade">Unidade:</label>
                <select id="editUnidade" name="unidade_id" required>
                    <option value="" {% if user.unidade_id is none %}selected{% endif %}></option>
                    <option value="1" {{ 'selected' if user.unidade_id==1 }}>Matriz</option>
                    <option value="2" {{ 'selected' if user.unidade_id==2 }}>Asche Saúde</option>
                    <option value="3" {{ 'selected' if user.unidade_id==3 }}>BRH Solution</option>
                    <option value="4" {{ 'selected' if user.unidade_id==4 }}>Confiance BRH</option>
                    <option value="5" {{ 'selected' if user.unidade_id==5 }}>Luanca BRH</option>
                    <option value="6" {{ 'selected' if user.unidade_id==6 }}>Yolo BRH</option>
                    <option value="7" {{ 'selected' if user.unidade_id==7 }}>Brazil Call</option>
                    <option value="8" {{ 'selected' if user.unidade_id==8 }}>BRH Corporate</option>
                    <option value="9" {{ 'selected' if user.unidade_id==9 }}>BRH Rio de Janeiro</option>
                    <option value="10" {{ 'selected' if user.unidade_id==10 }}>BRH Campinas</option>
                </select>
            </div>

            <div class="form-group">
                <label for="editEquipe">Equipe:</label>
                <select id="editEquipe" name="equipe_id">
                    <option value="" {% if user.equipe_id is none %}selected{% endif %}></option>
                    <option value="1" {{ 'selected' if user.equipe_id==1 }}>Silvana Zanardi</option>
                    <option value="2" {{ 'selected' if user.equipe_id==2 }}>Priscila Ventre</option>
                    <option value="3" {{ 'selected' if user.equipe_id==3 }}>Sérgio Andrade</option>
                    <option value="4" {{ 'selected' if user.equipe_id==4 }}>Franquia</option>
                    <option value="5" {{ 'selected' if user.equipe_id==5 }}>Solange Fernandes</option>
                    <option value="6" {{ 'selected' if user.equipe_id==6 }}>Yolo</option>
                    <option value="7" {{ 'selected' if user.equipe_id==7 }}>BRH Solution</option>
                    <option value="8" {{ 'selected' if user.equipe_id==8 }}>Asche</option>
                    <option value="9" {{ 'selected' if user.equipe_id==9 }}>Luanca</option>
                    <option value="10" {{ 'selected' if user.equipe_id==10 }}>Co-Working</option>
                    <option value="11" {{ 'selected' if user.equipe_id==11 }}>Brazil Call</option>
                    <option value="12" {{ 'selected' if user.equipe_id==12 }}>Confiance BRH</option>
                    <option value="13" {{ 'selected' if user.equipe_id==13 }}>BRH Corporate</option>
                    <option value="14" {{ 'selected' if user.equipe_id==14 }}>Brazil Health RJ</option>
                    <option value="15" {{ 'selected' if user.equipe_id==15 }}>Brazil Health Campinas</option>
                </select>
            </div>

            <div class="botton-conteiner-users">
                <button type="button" class="btn-excluir" id="deleteUserButton">Excluir Usuário</button>
                <button type="submit" id="UpdateUserButton">Salvar Alterações</button>
            </div>
        </form>
    </div>
</div>
{% block extra_scripts %}
<script src="{{ url_for('static', filename='js/update-master.js') }}"></script>
{% endblock %}
{% endblock %}