<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>50% de desconto na sua primeira mensalidade</title>
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;700&display=swap" rel="stylesheet">
  <!-- Ícones -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <!-- Swiper CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">
  <!-- CSS Principal -->
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/stylelp.css') }}">
  <script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "qnph5v5c2r");
</script>
</head>

<body>
  <!-- Header -->
  <header class="site-header">
    <div class="container header-container">

      <!-- Container específico para as duas logos -->
      <div class="logo-container">
        <div class="logo-wrapper">
          <img src="{{ url_for('static', filename='images/logobrazilhealthog.png') }}" alt="Logo 1" class="logo-img">
        </div>
        <div class="logo-wrapper">
          <img src="{{ url_for('static', filename='images/logoaccounttech.png') }}" alt="Logo 2" class="logo-img">
        </div>
      </div>

      <!-- Texto de destaque (seu h1) -->
      <div class="discount-offer">
        <h1 class="highlight">50% de desconto na primeira mensalidade do seu plano!</h1>
      </div>

      <!-- Menu de navegação existente -->
      <nav class="nav-menu">
        <ul>
          <li><a href="#inicio">Início</a></li>
          <li><a href="#sobre">Soluções</a></li>
          <li><a href="#promocoes">Promoções</a></li>
          <li><a href="#contato">Contato</a></li>
        </ul>
      </nav>

    </div>
  </header>

  <!-- Hero Section -->
  <section id="inicio" class="hero"> 
    <div class="hero-overlay"></div>
    <div class="hero-content container">
      <h2>Aproveite já nossa condição exclusiva para você, sua família ou empresa!</h2>
      <p>Somente aqui na Brazil Health, você tem 50% de desconto na primeira mensalidade do seu plano!</p>
      <a href="#sobre" class="btn-primary">Saiba Mais</a>
    </div>
  </section>

  <!-- Carrossel de Parceiros -->
  <section class="partners-carousel">
    <div class="container">
      <header class="section-header">
        <h2>Principais Parceiros</h2>
        <p>As maiores operadoras de saúde do Brasil</p>
      </header>
      <div class="swiper partners-swiper">
        <div class="swiper-wrapper">
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/amil.png') }}" alt="Amil">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/Allcare.png') }}" alt="Allcare">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/assim.png') }}" alt="Assim">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/bradesco.png') }}" alt="Bradesco">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/Careplus.png') }}" alt="Careplus">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/hapvidandi.png') }}" alt="Hapvida">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/medsenior.png') }}" alt="Medsenior">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/Omint.png') }}" alt="Omint">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/porto.png') }}" alt="Porto">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/Qualicorp.png') }}" alt="Qualicorp">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/Sami.png') }}" alt="Sami">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/trasmontano.png') }}" alt="Trasmontano">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/Unimed.png') }}" alt="Unimed">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/unimednacional.png') }}" alt="Unimed Nacional">
          </div>
          <div class="swiper-slide">
            <img src="{{ url_for('static', filename='images/parceiros/vrbeneficios.png') }}" alt="VR Benefícios">
          </div>
        </div>
        <div class="swiper-pagination"></div>
        <div class="swiper-button-prev"></div>
        <div class="swiper-button-next"></div>
      </div>
    </div>
  </section>

  <!-- Sobre a Empresa -->
  <section id="sobre" class="section about-section">
    <div class="container">
      <header class="section-header">
        <h2>Sobre a Brazil Health</h2>
        <p>Sua parceira em soluções completas de saúde e bem-estar</p>
      </header>

      <div class="about-content">
        <div class="about-text">
          <div class="about-card">
            <i class="fas fa-shield-alt"></i>
            <h3>Nossa Missão</h3>
            <p>Prestar soluções inteligentes em benefícios saúde, odonto, pet, alimentação e vida. Gerar serviços
              otimizados a cada cliente e corretor, buscando suprir as necessidades com eficácia, qualidade, gestão e
              prevenção. Além disso, oferecemos serviços para garantir a satisfação total de nossos clientes. Foco em
              atendimento, pós-vendas e relacionamento, buscando conquistar satisfação e mercado com nossos serviços.
            </p>
          </div>

          <div class="about-card">
            <i class="fas fa-star"></i>
            <h3>Nossa Visão</h3>
            <p>Ser reconhecida como a melhor consultoria e corretora no segmento saúde, odonto e vida no país para
              nossos parceiros e clientes, através de serviços diferenciados, inteligentes e competentes. Buscando
              superar todas as expectativas.</p>
          </div>

          <div class="about-card">
            <i class="fas fa-heart"></i>
            <h3>Nossos Valores</h3>
            <p>Inovação, Ética, Comprometimento, Relacionamento, Respeito e Transparência.</p>
          </div>
        </div>

        <div class="about-stats">
          <div class="stat-item">
            <span class="stat-number">+175 mil</span>
            <p>Vidas Atendidas</p>
          </div>
          <div class="stat-item">
            <span class="stat-number">120</span>
            <p>Colaboradores</p>
          </div>
          <div class="stat-item">
            <span class="stat-number">8</span>
            <p>Unidades</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Soluções / Produtos -->

  <!-- Promoções -->
  <section id="promocoes" class="section promotions">
    <div class="container">
      <header class="section-header">
        <h2>Promoção Exclusiva</h2>
        <p>Uma parceria Brazil Health e AccountTech para cuidar ainda melhor de você</p>
      </header>

      <div class="promo-content">
        <div class="promo-card main-offer">
          <div class="offer-badge">
            <span>Exclusivo</span>
          </div>
          <div class="offer-content">
            <i class="fas fa-percentage"></i>
            <h3>50% OFF na Primeira Mensalidade</h3>
            <p>Aproveite esta condição especial em qualquer plano Brazil Health</p>
            <ul class="offer-benefits">
              <li><i class="fas fa-check-circle"></i> Válido para novos contratos</li>
              <li><i class="fas fa-check-circle"></i> Disponível para pessoa física e jurídica</li>
              <li><i class="fas fa-check-circle"></i> Atendimento personalizado</li>
            </ul>
          </div>
          <div class="offer-action">
            <a href="#contato" class="btn-primary">Aproveitar Agora</a>
          </div>
        </div>

        <div class="partnership-info">
          <div class="partner-card">
            <div class="partner-logo">
              <img src="{{ url_for('static', filename='images/logobrazilhealthog.png') }}" alt="Brazil Health Logo">
            </div>
            <div class="partner-text">
              <h4>Brazil Health</h4>
              <p>Especialista em soluções completas de saúde e bem-estar para você, sua família e empresa</p>
            </div>
          </div>

          <div class="partnership-plus">
            <i class="fas fa-plus"></i>
          </div>

          <div class="partner-card">
            <div class="partner-logo">
              <img src="{{ url_for('static', filename='images/logoaccounttech.png') }}" alt="AccountTech Logo">
            </div>
            <div class="partner-text">
              <h4>AccountTech</h4>
              <p>Tecnologia e inovação em serviços contábeis e financeiros</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contato -->
  <section id="contato" class="section contact">
    <div class="container">
      <header class="section-header">
        <h2>Solicite um Contato</h2>
        <p>Preencha o formulário abaixo e nossa equipe especializada entrará em contato para oferecer a melhor solução
          para você.</p>
      </header>
      <form id="contactForm" class="contact-form">
        <div class="form-group">
          <label for="fullName">Nome Completo*</label>
          <input type="text" id="fullName" name="fullName" placeholder="Digite seu nome completo" required>
        </div>
        <div class="form-group">
          <label for="email">E-mail*</label>
          <input type="email" id="email" name="email" placeholder="Digite seu e-mail" required>
        </div>
        <div class="form-group">
          <label for="phone">Telefone*</label>
          <input type="tel" id="phone" name="phone" placeholder="(00) 00000-0000" required>
        </div>
        <div class="form-group radio-group">
          <label class="group-label">Possui CNPJ?*</label>
          <div class="radio-options">
            <div class="radio-option">
              <input type="radio" id="cnpjYes" name="hasCnpj" value="True" required>
              <label for="cnpjYes">Sim</label>
            </div>
            <div class="radio-option">
              <input type="radio" id="cnpjNo" name="hasCnpj" value="False">
              <label for="cnpjNo">Não</label>
            </div>
          </div>
        </div>
        <div class="form-group">
          <label for="currentPlan">Plano Atual</label>
          <input type="text" id="currentPlan" name="currentPlan" placeholder="Se possuir, informe seu plano atual">
        </div>
        <div class="form-group">
          <label for="city">Cidade*</label>
          <input type="text" id="city" name="city" placeholder="Digite sua cidade" required>
        </div>
        <div class="form-group">
          <label for="contactPreference">Preferência de Contato*</label>
          <select id="contactPreference" name="contactPreference" required>
            <option value="">Selecione o melhor período</option>
            <option value="manha">Manhã (08:00 - 12:00)</option>
            <option value="tarde">Tarde (13:00 - 18:00)</option>
            <option value="noite">Noite (18:00 - 20:00)</option>
          </select>
        </div>
        <button type="submit" class="btn-primary">
          <i class="fas fa-paper-plane"></i>
          Solicitar Contato
        </button>
        <p class="form-disclaimer">*Campos obrigatórios</p>
      </form>

      <div class="whatsapp-section">
        <p>Ou se preferir, entre em contato direto pelo WhatsApp:</p>
        <a href="https://api.whatsapp.com/send?phone=*************&text=Ol%C3%A1%2C%20vim%20pela%20condi%C3%A7%C3%A3o%20especial%20de%2050%25%20na%20primeira%20mensalidade%2C%20em%20parceria%20com%20a%20AccountTech!"
          target="_blank" class="whatsapp-button">
          <i class="fab fa-whatsapp"></i>
          Chamar no WhatsApp
        </a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="site-footer">
    <div class="container">
      <p>&copy; 2025 Brazil Health. Todos os direitos reservados.</p>
      <nav class="footer-nav">
        <ul>
          <li><a href="#inicio">Início</a></li>
          <li><a href="#sobre">Soluções</a></li>
          <li><a href="#promocoes">Promoções</a></li>
          <li><a href="#contato">Contato</a></li>
        </ul>
      </nav>
    </div>
  </footer>

  <!-- Custom JS -->
  <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
  <script src="{{ url_for('static', filename='js/lpaccounttech.js') }}"></script>

  <!-- WhatsApp Flutuante -->
  <div class="whatsapp-float">
    <a href="https://api.whatsapp.com/send?phone=*************&text=Ol%C3%A1%2C%20vim%20pela%20condi%C3%A7%C3%A3o%20especial%20de%2050%25%20na%20primeira%20mensalidade%2C%20em%20parceria%20com%20a%20AccountTech!" target="_blank" class="whatsapp-float-btn">
      <i class="fab fa-whatsapp"></i>
      <span class="whatsapp-float-text">Fale Conosco</span>
    </a>
  </div>
</body>

</html>