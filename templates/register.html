<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <title>Registro | BrazilHealth</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- Adicionando o favicon -->
    <link rel="icon" href="{{ url_for('static', filename='images/brh-logo.png') }}" type="image/png">
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/brh-logo.png') }}" type="image/png">
</head>

<body class="register">
    <div class="header-image-register">
        <img src="https://brazilhealth.com.br/wp-content/uploads/2020/03/07_Prancheta-1-cópia-6-e1583518361332-1.png"
            alt="Logo BrazilHealth">
    </div>
    <div class="register-container">
        <form id="formRegister" class="register-form" action="{{ url_for('register') }}" method="post"
            enctype="multipart/form-data">
            <h2>Crie uma conta</h2>
            <label for="nome">Nome Completo:</label>
            <input type="text" id="nome" name="nome" required>

            <label for="data_nascimento">Data de Nascimento:</label>
            <input type="date" id="data_nascimento" name="data_nascimento" required oninput="limitarData(this)">

            <label for="telefone">Celular:</label>
            <input type="text" id="telefone" name="telefone" required oninput="aplicarMascaraTelefone(this)"
                pattern="\(\d{2}\)\s\d{5}-\d{4}" maxlength="15"
                title="O telefone deve estar no formato (xx) xxxxx-xxxx">

            <label for="cpf">CPF:</label>
            <input type="text" id="cpf" name="cpf" required>
            <div id="cpf-error" style="color: red; display: none;">CPF inválido</div>

            <label for="email">E-mail:</label>
            <input type="email" id="email" name="email" required>

            <label for="profile_image">Imagem de Perfil:</label>
            <input type="file" id="profile_image" name="profile_image" accept="image/*" required>
            <div id="image-error" style="color: red; display: none;">Selecione uma imagem de perfil.</div>

            <div class="select-content">
                <div class="form-group-register">
                    <label for="cargo">Cargo:</label>
                    <select id="cargo" class="editSelect-register" name="tipo_usuario" required>
                        <option value="5">Funcionário</option>
                        <option value="2">Sócio</option>
                        <option value="3">Superintendente</option>
                        <option value="4">Supervisor</option>
                        <option value="6">Franqueado</option>
                    </select>
                </div>

                <div class="form-group-register">
                    <label for="editUnidade">Unidade:</label>
                    <select class="editSelect-register" name="unidade_id" required>
                        <option value="1">Matriz</option>
                        <option value="3">BRH Solution</option>
                        <option value="4">Confiance BRH</option>
                        <option value="5">Luanca BRH</option>
                        <option value="7">Brazil Call</option>
                        <option value="8">BRH Corporate</option>
                        <option value="9">Brazil Health RJ</option>
                        <option value="10">Brazil Health Campinas</option>
                    </select>
                </div>

                <div class="form-group-register hidden" id="equipe-container" style="display: none;">
                    <label for="editEquipe">Equipe:</label>
                    <select class="editSelect-register" name="equipe_id">
                        <option value="1">Silvana Zanardi</option>
                        <option value="2">Priscila Ventre</option>
                        <option value="3">Sérgio Andrade</option>
                        <option value="4">Franquia</option>
                        <option value="5">Solange Fernandes</option>
                        <option value="7">BRH Solution</option>
                        <option value="9">Luanca</option>
                        <option value="10">Co-Working</option>
                        <option value="11">Brazil Call</option>
                        <option value="12">Confiance BRH</option>
                        <option value="13">BRH Corporate</option>
                        <option value="14">Brazil Health RJ</option>
                        <option value="15">Brazil Health Campinas</option>
                    </select>
                </div>
            </div>

            <label for="senha">Senha:</label>
            <div class="input-group-register">
                <input type="password" id="senha" name="senha" required>
                <span id="togglePassword" class="fa fa-eye input-group-register-addon"></span>
            </div>
            <div id="senha-reqs" style="margin-top: 10px;">
                <p id="req-length" style="color: #555;">Deve conter 6 a 64 caracteres</p>
                <p id="req-letter" style="color: #555;">Deve conter ao menos uma letra</p>
                <p id="req-number" style="color: #555;">Deve conter ao menos um número</p>
                <p id="req-special" style="color: #555;">Deve conter ao menos um caracter especial (!@#$%&*)</p>
                <br>
            </div>
            <label for="confirm_senha">Confirme a Senha:</label>
            <div class="input-group-register">
                <input type="password" id="confirm_senha" name="confirm_senha" required>
                <span id="toggleConfirmPassword" class="fa fa-eye input-group-register-addon"></span>
            </div>
            <p id="req-match" style="color: #555;">Certifique-se de que as senhas são iguais</p>

            <div class="submit-register">
                <button type="submit" name="submit-register">Continuar</button>
            </div>
        </form>
        {% if get_flashed_messages() %}
        <ul>
            {% for message in get_flashed_messages() %}
            <li>{{ message }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    <div id="loadingScreen" class="loading-screen" style="display: none;">
        <div class="spinner"></div>
    </div>

    <p class="login-link">Já tem uma conta? <a href="{{ url_for('index') }}">Entrar</a></p>

    <script src="{{ url_for('static', filename='js/register.js') }}"></script>
</body>

</html>