{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}brazil{% endblock %}
{% block content %}
    <div class="layout-container">
        <aside class="sidebar">
            <h3>Empresas</h3>
            <div class="empresa-lista">
                <button class="empresa-btn" data-empresa="Brazil Health">Brazil Health</button>
                <button class="empresa-btn" data-empresa="Luanca BRH">Luanca BRH</button>
                <button class="empresa-btn" data-empresa="Confiance BRH">Confiance BRH</button>
                <button class="empresa-btn" data-empresa="BRH Solution">BRH Solution</button>
                <button class="empresa-btn" data-empresa="BrazilCall">BrazilCall</button>
                <button class="empresa-btn" data-empresa="Asche Saúde BRH">Asche Saúde BRH</button>
                <button class="empresa-btn" data-empresa="Yolo BRH">Yolo BRH</button>
                <button class="empresa-btn" data-empresa="BRH Corporate">BRH Corporate</button>
            </div>
        </aside>
        <div class="content-container">
            <div id="header-documentos">
                <h1>Documentos Empresas</h1>
            </div>
            <div id="select-empresa">
                <h2 class="text-primary">Selecione uma Empresa</h2>
            </div>
            <div id="documentos-container" class="documentos-container-gestao">
                <!-- Os documentos serão renderizados aqui -->
            </div>
        </div>
    </div>
    {% block extra_scripts %}
    <script src="{{ url_for('static', filename='js/gestao.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const empresaButtons = document.querySelectorAll('.empresa-btn');
            empresaButtons.forEach(button => {
                button.addEventListener('click', function () {
                    const empresa = this.getAttribute('data-empresa');
                    fetch(`/api/documentos/${empresa}`)
                        .then(response => response.json())
                        .then(data => {
                            renderDocumentos(data, empresa);
                        });
                });
            });
        });

        function renderDocumentos(data, empresa) {
            const container = document.getElementById('documentos-container');
            container.innerHTML = `
                <h1>Documentos de ${empresa}</h1>
                <div class="documentos-lista">
                    ${data.map(categoria => `
                        <h2>${categoria.categoria}</h2>
                        <ul>
                            ${categoria.documentos.map(doc => `<li><a href="#">${doc}</a></li>`).join('')}
                        </ul>
                    `).join('')}
                </div>
            `;
        }
    </script>
    {% endblock %}
{% endblock %}
