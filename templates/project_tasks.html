{% extends "projects_base.html" %}
{% block title %}Minhas Tarefas{% endblock %}
{% block content %}
<style>
    /* Efeito de hover para os cabeçalhos da tabela */
    th {
        transition: background-color 0.3s ease;
    }
    th:hover {
        background-color: #f8f9fa;
        cursor: pointer;
    }
</style>
<div>
    <button type="button" class="btn btn-primary mb-4" data-toggle="modal" data-target="#independentTaskModal">
        Criar Tarefa Independente
    </button>
</div>
<!-- Modal de cadastro de tarefa independente -->
<div class="modal fade" id="independentTaskModal" tabindex="-1" role="dialog" aria-labelledby="independentTaskModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <form action="{{ url_for('independent_task') }}" method="POST">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="independentTaskModalLabel">Nova Tarefa Independente</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Campos do formulário -->
                    <div class="form-group">
                        <label for="titulo">Título</label>
                        <input type="text" class="form-control" name="titulo" id="titulo" placeholder="Título da tarefa" required>
                    </div>
                    <div class="form-group">
                        <label for="descricao">Descrição</label>
                        <textarea class="form-control" name="descricao" id="descricao" placeholder="Descrição da tarefa"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="data_inicio">Data de Início</label>
                        <input type="date" class="form-control" name="data_inicio" id="data_inicio">
                    </div>
                    <div class="form-group">
                        <label for="data_fim">Data de Fim</label>
                        <input type="date" class="form-control" name="data_fim" id="data_fim">
                    </div>
                    <div class="form-group">
                        <label for="prioridade">Prioridade</label>
                        <select class="form-control" name="prioridade" id="prioridade">
                            <option value="Alta">Alta</option>
                            <option value="Média">Média</option>
                            <option value="Baixa">Baixa</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="complexidade">Complexidade</label>
                        <select class="form-control" name="complexidade" id="complexidade">
                            <option value="Alta">Alta</option>
                            <option value="Média">Média</option>
                            <option value="Baixa">Baixa</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Salvar Tarefa</button>
                </div>
            </div>
        </form>
    </div>
</div>
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Minhas Tarefas</h3>
    </div>
    <div class="card-body">
        <!-- Kanban Board -->
        <div class="row mb-4">
            <!-- Coluna A Fazer -->
            <div class="col-md-4 mb-3">
                <div class="card card-primary h-100">
                    <div class="card-header">
                        <h3 class="card-title">A Fazer</h3>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        {% set todo_tasks = tasks|selectattr('7', 'equalto', 'a_fazer')|sort(attribute='7', reverse=True) %}
                        {% for task in todo_tasks %}
                        <div class="card card-widget mb-2">
                            <div class="card-header">
                                <h5 class="card-title">{{ task[3] }}</h5>
                            </div>
                            <div class="card-body">
                                <p>{{ task[4] }}</p>
                                <p>
                                    <small>Responsável: {{ task[13] }}</small><br>
                                    <small>Prazo: {{ task[6] }}</small>
                                </p>
                                {% if not task[1] %}
                                    <span class="badge badge-dark">Tarefa sem Projeto</span>
                                {% endif %}
                                <!-- Badges padronizados -->
                                <p>
                                  {% if task[15] == 'Alta' %}
                                    <span class="badge badge-danger">Prioridade: Alta</span>
                                  {% elif task[15] in ['Média', 'Media'] %}
                                    <span class="badge badge-warning">Prioridade: Média</span>
                                  {% elif task[15] == 'Baixa' %}
                                    <span class="badge badge-success">Prioridade: Baixa</span>
                                  {% else %}
                                    <span class="badge badge-secondary">Prioridade: {{ task[15] or 'Não definido' }}</span>
                                  {% endif %}
                                  {% if task[14] == 'Alta' %}
                                    <span class="badge badge-danger">Complexidade: Alta</span>
                                  {% elif task[14] in ['Média', 'Media'] %}
                                    <span class="badge badge-warning">Complexidade: Média</span>
                                  {% elif task[14] == 'Baixa' %}
                                    <span class="badge badge-success">Complexidade: Baixa</span>
                                  {% else %}
                                    <span class="badge badge-secondary">Complexidade: {{ task[14] or 'Não definido' }}</span>
                                  {% endif %}
                                </p>
                                <div class="mt-2">
                                    <form action="{{ url_for('iniciar_tarefa', tarefa_id=task[0]) }}" method="POST" style="display:inline;">
                                        <button type="submit" class="btn btn-primary btn-sm">Iniciar Tempo</button>
                                    </form>
                                    <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#modalEditarTarefa{{ task[0] }}">
                                        Editar Tarefa
                                    </button>
                                    {% if task[13] == 'Sem responsável' %}
                                    <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#modalAddMembroTarefa{{ task[0] }}">
                                        Adicionar Membro
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <!-- Modal para Editar Tarefa -->
                        <div class="modal fade" id="modalEditarTarefa{{ task[0] }}" tabindex="-1" role="dialog" aria-labelledby="modalEditarTarefaLabel{{ task[0] }}" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="modalEditarTarefaLabel{{ task[0] }}">Editar Tarefa</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <form action="{{ url_for('editar_tarefa', tarefa_id=task[0]) }}" method="POST">
                                            <div class="form-group">
                                                <label for="titulo{{ task[0] }}">Título</label>
                                                <input type="text" class="form-control" id="titulo{{ task[0] }}" name="titulo" value="{{ task[3] }}">
                                            </div>
                                            <div class="form-group">
                                                <label for="descricao{{ task[0] }}">Descrição</label>
                                                <textarea class="form-control" id="descricao{{ task[0] }}" name="descricao">{{ task[4] }}</textarea>
                                            </div>
                                            <div class="form-group">
                                                <label for="data_fim{{ task[0] }}">Data Fim</label>
                                                <input type="date" class="form-control" id="data_fim{{ task[0] }}" name="data_fim" value="{{ task[6] }}">
                                            </div>
                                            {% if not task[1] %}
                                            <div class="form-group">
                                                <label for="prioridade{{ task[0] }}">Prioridade</label>
                                                <select class="form-control" name="prioridade" id="prioridade{{ task[0] }}">
                                                    <option value="Alta" {% if task[15] == 'Alta' %}selected{% endif %}>Alta</option>
                                                    <option value="Média" {% if task[15] in ['Média', 'Media'] %}selected{% endif %}>Média</option>
                                                    <option value="Baixa" {% if task[15] == 'Baixa' %}selected{% endif %}>Baixa</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label for="complexidade{{ task[0] }}">Complexidade</label>
                                                <select class="form-control" name="complexidade" id="complexidade{{ task[0] }}">
                                                    <option value="Alta" {% if task[14] == 'Alta' %}selected{% endif %}>Alta</option>
                                                    <option value="Média" {% if task[14] in ['Média', 'Media'] %}selected{% endif %}>Média</option>
                                                    <option value="Baixa" {% if task[14] == 'Baixa' %}selected{% endif %}>Baixa</option>
                                                </select>
                                            </div>
                                            {% endif %}
                                            <button type="submit" class="btn btn-warning">Salvar</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% if task[13] == 'Sem responsável' %}
                        <!-- Modal para Adicionar Membro à Tarefa -->
                        <div class="modal fade" id="modalAddMembroTarefa{{ task[0] }}" tabindex="-1" role="dialog" aria-labelledby="modalAddMembroTarefaLabel{{ task[0] }}" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="modalAddMembroTarefaLabel{{ task[0] }}">Adicionar Membro à Tarefa</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <form action="{{ url_for('adicionar_membro_tarefa', tarefa_id=task[0]) }}" method="POST">
                                            <div class="form-group">
                                                <label for="membro_tarefa{{ task[0] }}">Selecione o Membro</label>
                                                <select class="form-control" id="membro_tarefa{{ task[0] }}" name="membro_tarefa" required>
                                                    <option value="">Selecione...</option>
                                                    {% for user in users %}
                                                    <option value="{{ user.id }}">{{ user.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <button type="submit" class="btn btn-info">Adicionar</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            <!-- Coluna Em Andamento -->
            <div class="col-md-4 mb-3">
                <div class="card card-warning h-100">
                    <div class="card-header">
                        <h3 class="card-title">Em Andamento</h3>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        {% set in_progress_tasks = tasks|selectattr('7', 'equalto', 'em_andamento')|sort(attribute='7', reverse=True) %}
                        {% for task in in_progress_tasks %}
                        <div class="card card-widget mb-2">
                            <div class="card-header">
                                <h5 class="card-title">{{ task[3] }}</h5>
                            </div>
                            <div class="card-body">
                                <p>{{ task[4] }}</p>
                                <p>
                                    <small>Responsável: {{ task[13] }}</small><br>
                                    <small>Prazo: {{ task[6] }}</small>
                                </p>
                                {% if not task[1] %}
                                    <span class="badge badge-dark">Tarefa sem Projeto</span>
                                {% endif %}
                                <!-- Badges padronizados -->
                                <p>
                                  {% if task[15] == 'Alta' %}
                                    <span class="badge badge-danger">Prioridade: Alta</span>
                                  {% elif task[15] in ['Média', 'Media'] %}
                                    <span class="badge badge-warning">Prioridade: Média</span>
                                  {% elif task[15] == 'Baixa' %}
                                    <span class="badge badge-success">Prioridade: Baixa</span>
                                  {% else %}
                                    <span class="badge badge-secondary">Prioridade: {{ task[15] or 'Não definido' }}</span>
                                  {% endif %}
                                  {% if task[14] == 'Alta' %}
                                    <span class="badge badge-danger">Complexidade: Alta</span>
                                  {% elif task[14] in ['Média', 'Media'] %}
                                    <span class="badge badge-warning">Complexidade: Média</span>
                                  {% elif task[14] == 'Baixa' %}
                                    <span class="badge badge-success">Complexidade: Baixa</span>
                                  {% else %}
                                    <span class="badge badge-secondary">Complexidade: {{ task[14] or 'Não definido' }}</span>
                                  {% endif %}
                                </p>
                                <div class="mt-2">
                                    <form action="{{ url_for('finalizar_tarefa', tarefa_id=task[0]) }}" method="POST" style="display:inline;">
                                        <button type="submit" class="btn btn-danger btn-sm">Parar Tempo</button>
                                    </form>
                                    <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#modalEditarTarefa{{ task[0] }}">
                                        Editar Tarefa
                                    </button>
                                    {% if task[13] == 'Sem responsável' %}
                                    <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#modalAddMembroTarefa{{ task[0] }}">
                                        Adicionar Membro
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <!-- Modal para Editar Tarefa (reutilizado) -->
                        <div class="modal fade" id="modalEditarTarefa{{ task[0] }}" tabindex="-1" role="dialog" aria-labelledby="modalEditarTarefaLabel{{ task[0] }}" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="modalEditarTarefaLabel{{ task[0] }}">Editar Tarefa</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <form action="{{ url_for('editar_tarefa', tarefa_id=task[0]) }}" method="POST">
                                            <div class="form-group">
                                                <label for="titulo{{ task[0] }}">Título</label>
                                                <input type="text" class="form-control" id="titulo{{ task[0] }}" name="titulo" value="{{ task[3] }}">
                                            </div>
                                            <div class="form-group">
                                                <label for="descricao{{ task[0] }}">Descrição</label>
                                                <textarea class="form-control" id="descricao{{ task[0] }}" name="descricao">{{ task[4] }}</textarea>
                                            </div>
                                            <div class="form-group">
                                                <label for="data_fim{{ task[0] }}">Data Fim</label>
                                                <input type="date" class="form-control" id="data_fim{{ task[0] }}" name="data_fim" value="{{ task[6] }}">
                                            </div>
                                            {% if not task[1] %}
                                            <div class="form-group">
                                                <label for="prioridade{{ task[0] }}">Prioridade</label>
                                                <select class="form-control" name="prioridade" id="prioridade{{ task[0] }}">
                                                    <option value="Alta" {% if task[15]=='Alta' %}selected{% endif %}>Alta</option>
                                                    <option value="Média" {% if task[15] in ['Média','Media'] %}selected{% endif %}>Média</option>
                                                    <option value="Baixa" {% if task[15]=='Baixa' %}selected{% endif %}>Baixa</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label for="complexidade{{ task[0] }}">Complexidade</label>
                                                <select class="form-control" name="complexidade" id="complexidade{{ task[0] }}">
                                                    <option value="Alta" {% if task[14]=='Alta' %}selected{% endif %}>Alta</option>
                                                    <option value="Média" {% if task[14] in ['Média','Media'] %}selected{% endif %}>Média</option>
                                                    <option value="Baixa" {% if task[14]=='Baixa' %}selected{% endif %}>Baixa</option>
                                                </select>
                                            </div>
                                            {% endif %}
                                            <button type="submit" class="btn btn-warning">Salvar</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% if task[13] == 'Sem responsável' %}
                        <!-- Modal para Adicionar Membro à Tarefa (reutilizado) -->
                        <div class="modal fade" id="modalAddMembroTarefa{{ task[0] }}" tabindex="-1" role="dialog" aria-labelledby="modalAddMembroTarefaLabel{{ task[0] }}" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="modalAddMembroTarefaLabel{{ task[0] }}">Adicionar Membro à Tarefa</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <form action="{{ url_for('adicionar_membro_tarefa', tarefa_id=task[0]) }}" method="POST">
                                            <div class="form-group">
                                                <label for="membro_tarefa{{ task[0] }}">Selecione o Membro</label>
                                                <select class="form-control" id="membro_tarefa{{ task[0] }}" name="membro_tarefa" required>
                                                    <option value="">Selecione...</option>
                                                    {% for user in users %}
                                                    <option value="{{ user.id }}">{{ user.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <button type="submit" class="btn btn-info">Adicionar</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            <!-- Coluna Concluído -->
            <div class="col-md-4 mb-3">
                <div class="card card-success h-100">
                    <div class="card-header">
                        <h3 class="card-title">Concluído</h3>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        {% set done_tasks = tasks|selectattr('7', 'equalto', 'concluido')|sort(attribute='7', reverse=True) %}
                        {% for task in done_tasks %}
                        <div class="card card-widget mb-2">
                            <div class="card-header">
                                <h5 class="card-title">{{ task[3] }}</h5>
                            </div>
                            <div class="card-body">
                                <p>{{ task[4] }}</p>
                                <p>
                                    <small>Responsável: {{ task[13] }}</small><br>
                                    <small>Prazo: {{ task[6] }}</small><br>
                                    <small>Tempo Gasto: {{ task[12] or '00:00' }}</small>
                                </p>
                                {% if not task[1] %}
                                    <span class="badge badge-dark">Tarefa sem Projeto</span>
                                {% endif %}
                                <!-- Badges padronizados -->
                                <p>
                                  {% if task[15] == 'Alta' %}
                                    <span class="badge badge-danger">Prioridade: Alta</span>
                                  {% elif task[15] in ['Média', 'Media'] %}
                                    <span class="badge badge-warning">Prioridade: Média</span>
                                  {% elif task[15] == 'Baixa' %}
                                    <span class="badge badge-success">Prioridade: Baixa</span>
                                  {% else %}
                                    <span class="badge badge-secondary">Prioridade: {{ task[15] or 'Não definido' }}</span>
                                  {% endif %}
                                  {% if task[14] == 'Alta' %}
                                    <span class="badge badge-danger">Complexidade: Alta</span>
                                  {% elif task[14] in ['Média', 'Media'] %}
                                    <span class="badge badge-warning">Complexidade: Média</span>
                                  {% elif task[14] == 'Baixa' %}
                                    <span class="badge badge-success">Complexidade: Baixa</span>
                                  {% else %}
                                    <span class="badge badge-secondary">Complexidade: {{ task[14] or 'Não definido' }}</span>
                                  {% endif %}
                                </p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        <hr class="my-5">
        <!-- Tasks Table Section com Filtro e Ordenação -->
        <h4 class="mb-3">Lista Completa de Tarefas</h4>
        <div class="mb-3">
            <input type="text" id="globalSearch" class="form-control" placeholder="Pesquisar em todas as colunas..." oninput="filterTable()">
        </div>
        {% if tasks %}
        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
            <table class="table table-bordered table-striped" id="tasksTable">
                <thead>
                    <tr>
                        <th onclick="sortTable(0)" data-toggle="tooltip" title="Clique para classificar">ID <i class="bi bi-arrow-down-up"></i></th>
                        <th onclick="sortTable(1)" data-toggle="tooltip" title="Clique para classificar">Título <i class="bi bi-arrow-down-up"></i></th>
                        <th onclick="sortTable(2)" data-toggle="tooltip" title="Clique para classificar">Descrição <i class="bi bi-arrow-down-up"></i></th>
                        <th onclick="sortTable(3)" data-toggle="tooltip" title="Clique para classificar">Data Início <i class="bi bi-arrow-down-up"></i></th>
                        <th onclick="sortTable(4)" data-toggle="tooltip" title="Clique para classificar">Data Fim <i class="bi bi-arrow-down-up"></i></th>
                        <th onclick="sortTable(5)" data-toggle="tooltip" title="Clique para classificar">Status <i class="bi bi-arrow-down-up"></i></th>
                        <th onclick="sortTable(6)" data-toggle="tooltip" title="Clique para classificar">Complexidade <i class="bi bi-arrow-down-up"></i></th>
                        <th onclick="sortTable(7)" data-toggle="tooltip" title="Clique para classificar">Prioridade <i class="bi bi-arrow-down-up"></i></th>
                        <th onclick="sortTable(8)" data-toggle="tooltip" title="Clique para classificar">Responsáveis <i class="bi bi-arrow-down-up"></i></th>
                    </tr>
                </thead>
                <tbody>
                    {% for task in tasks|sort(attribute='7', reverse=True) %}
                    <tr data-id="{{ task[0] }}">
                        <td>{{ task[0] }}</td>
                        <td>{{ task[3] }}</td>
                        <td>{{ task[4] }}</td>
                        <td>{{ task[5] }}</td>
                        <td>{{ task[6] }}</td>
                        <td>{{ task[7] }}</td>
                        <td>
                            {% if task[14] == 'Alta' %}
                                <span class="badge badge-danger">Alta</span>
                            {% elif task[14] in ['Média', 'Media'] %}
                                <span class="badge badge-warning">Média</span>
                            {% elif task[14] == 'Baixa' %}
                                <span class="badge badge-success">Baixa</span>
                            {% else %}
                                <span class="badge badge-secondary">{{ task[14] }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if task[15] == 'Alta' %}
                                <span class="badge badge-danger">Alta</span>
                            {% elif task[15] in ['Média', 'Media'] %}
                                <span class="badge badge-warning">Média</span>
                            {% elif task[15] == 'Baixa' %}
                                <span class="badge badge-success">Baixa</span>
                            {% else %}
                                <span class="badge badge-secondary">{{ task[15] }}</span>
                            {% endif %}
                        </td>
                        <td>{{ task[13] }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p>Você não possui tarefas atribuídas.</p>
        {% endif %}
    </div>
</div>
<script>
    function filterTable() {
        const input = document.getElementById("globalSearch");
        const filter = input.value.toUpperCase();
        const table = document.getElementById("tasksTable");
        const tr = table.getElementsByTagName("tr");
        for (let i = 1; i < tr.length; i++) {
            let displayRow = false;
            const td = tr[i].getElementsByTagName("td");
            for (let j = 0; j < td.length; j++) {
                if (td[j]) {
                    const txtValue = td[j].textContent || td[j].innerText;
                    if (txtValue.toUpperCase().indexOf(filter) > -1) {
                        displayRow = true;
                        break;
                    }
                }
            }
            tr[i].style.display = displayRow ? "" : "none";
        }
    }
    function sortTable(n) {
        const table = document.getElementById("tasksTable");
        const rows = Array.from(table.rows).slice(1);
        let direction = "asc";
        if (table.getAttribute("data-sort") === n.toString() && table.getAttribute("data-direction") === "asc") {
            direction = "desc";
        }
        rows.sort((a, b) => {
            const aValue = a.getElementsByTagName("td")[n].textContent;
            const bValue = b.getElementsByTagName("td")[n].textContent;
            if (direction === "asc") {
                return aValue.localeCompare(bValue, undefined, { numeric: true });
            } else {
                return bValue.localeCompare(aValue, undefined, { numeric: true });
            }
        });
        while (table.rows.length > 1) {
            table.deleteRow(1);
        }
        const tbody = table.getElementsByTagName("tbody")[0];
        rows.forEach(row => tbody.appendChild(row));
        table.setAttribute("data-sort", n);
        table.setAttribute("data-direction", direction);
    }
    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
{% endblock %}
