{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}gestaoReembolso{% endblock %}

{% block content %}
<script id="reembolsos-data" type="application/json">
    {{ reembolsos | tojson }}
</script>

<div class="board">
    <div class="limiter-board">
        <!-- Coluna de Solicitações Pendentes -->
        <div class="column pending">
            <h2>Solicitações Pendentes</h2>
            {% for reembolso in reembolsos %}
            {% if reembolso[10] == 'Pendente' %}
            <div class="card" draggable="true" id="{{ reembolso[0] }}">
                <h3>Solicitação de Reembolso</h3>
                <p><strong>Nome:</strong> {{ reembolso[1] }}</p>
                <p><strong>Departamento:</strong> {{ reembolso[2] }}</p>
                <p><strong>Valor:</strong> R$ {{ reembolso[7] }}</p>
                <p><strong>Data de Solicitação:</strong> {{ reembolso[15] or '--' }}</p>
                <button class="view-details" onclick="openModal(this.parentElement)">Ver Detalhes</button>
            </div>
            {% endif %}
            {% endfor %}
        </div>

        <!-- Coluna de Solicitações Aprovadas -->
        <div class="column approved">
            <h2>Solicitações Aprovadas</h2>
            {% for reembolso in reembolsos %}
            {% if reembolso[10] == 'Aprovado' %}
            <div class="card" draggable="true" id="{{ reembolso[0] }}">
                <h3>Solicitação de Reembolso</h3>
                <p><strong>Nome:</strong> {{ reembolso[1] }}</p>
                <p><strong>Departamento:</strong> {{ reembolso[2] }}</p>
                <p><strong>Valor:</strong> R$ {{ reembolso[7] }}</p>
                <p><strong>Data de Solicitação:</strong> {{ reembolso[15] or '--' }}</p>
                <p><strong>Aprovado por:</strong> {{ reembolso[14] or '--' }}</p>
                <p><strong>Data de Aprovação:</strong> {{ reembolso[13] or '--' }}</p>
                <button class="view-details" onclick="openModal(this.parentElement)">Ver Detalhes</button>
            </div>
            {% endif %}
            {% endfor %}
        </div>

        <div class="column rejected">
            <h2>Solicitações Rejeitadas</h2>
            {% for reembolso in reembolsos %}
            {% if reembolso[10] == 'Rejeitado' %}
            <div class="card" draggable="true" id="{{ reembolso[0] }}">
                <h3>Solicitação de Reembolso</h3>
                <p><strong>Nome:</strong> {{ reembolso[1] }}</p>
                <p><strong>Departamento:</strong> {{ reembolso[2] }}</p>
                <p><strong>Valor:</strong> R$ {{ reembolso[7] }}</p>
                <p><strong>Data de Solicitação:</strong> {{ reembolso[15] or '--' }}</p>
                <p><strong>Aprovado por:</strong> {{ reembolso[14] or '--' }}</p>
                <p><strong>Data de Aprovação:</strong> {{ reembolso[13] or '--' }}</p>
                <button class="view-details" onclick="openModal(this.parentElement)">Ver Detalhes</button>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
</div>

<!-- Modal para detalhes da solicitação -->
<div id="modal" class="modal">
    <div class="modal-content">
        <span class="close-button" onclick="closeModal()">&times;</span>
        <h2>Detalhes da Solicitação</h2>
        <p><strong>Nome Completo:</strong> <span id="modal-nome"></span></p>
        <p><strong>Departamento:</strong> <span id="modal-departamento"></span></p>
        <p><strong>E-mail:</strong> <span id="modal-email"></span></p>
        <p><strong>Telefone:</strong> <span id="modal-telefone"></span></p>
        <p><strong>Data da Despesa:</strong> <span id="modal-data"></span></p>
        <p><strong>Tipo de Despesa:</strong> <span id="modal-tipo"></span></p>
        <p><strong>Valor da Despesa:</strong> R$ <span id="modal-valor"></span></p>
        <p><strong>Data de Solicitação:</strong> <span id="modal-data-solicitacao"></span></p>
        <p><strong>Anexo:</strong> <a href="#" id="modal-anexo">Download</a></p>
        <p><strong>Descrição:</strong> <span id="modal-descricao"></span></p>
        <p><strong>Aprovado por:</strong> <span id="modal-aprovador"></span></p>
        <p><strong>Data de Aprovação:</strong> <span id="modal-data-aprovacao"></span></p>
        {% if session.get('user_type') in [1, 7] %}
        <button class="approve-btn" onclick="approveRequest()">Aprovar</button>
        <button class="reject-btn" onclick="rejectRequest()">Rejeitar</button>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script type="text/javascript">
    var userType = "{{ session.get('user_type') }}";
</script>
<script src="{{ url_for('static', filename='js/gestao-reembolsos.js') }}" defer></script>
{% endblock %}