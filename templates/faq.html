{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}faq{% endblock %}
{% block content %}
<div id="faq-float-box" class="container mt-5">
    <h2>Perguntas frequentes</h2>
    <p>E o mais importante: respostas inéditas.</p>
    <div id="faqAccordion">
        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center" id="headingOne">
                <h5 class="mb-0 w-100 d-flex justify-content-between align-items-center">
                    <span>Qual data base do dissídio?</span>
                    <button class="btn btn-link text-right" data-toggle="collapse" data-target="#collapseOne"
                        aria-expanded="true" aria-controls="collapseOne">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h5>
            </div>
            <div id="collapseOne" class="collapse" aria-labelledby="headingOne" data-parent="#faqAccordion">
                <div class="card-body">
                    As partes fixam a vigência do presente Acordo Coletivo de Trabalho data base dos profissionais em 1º de maio de cada ano. 
                </div>
            </div>
        </div>

        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center" id="headingTwo">
                <h5 class="mb-0 w-100 d-flex justify-content-between align-items-center">
                    <span>Qual data de pagamento do salário?</span>
                    <button class="btn btn-link text-right collapsed" data-toggle="collapse" data-target="#collapseTwo"
                        aria-expanded="false" aria-controls="collapseTwo">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h5>
            </div>
            <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#faqAccordion">
                <div class="card-body">
                    Pagamentos de salário é feito no 5º dia útil de cada mês 
                </div>
            </div>
        </div>

        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center" id="headingThree">
                <h5 class="mb-0 w-100 d-flex justify-content-between align-items-center">
                    <span>Qual data de pagamento dos benefícios vale transporte e refeição?</span>
                    <button class="btn btn-link text-right collapsed" data-toggle="collapse"
                        data-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h5>
            </div>
            <div id="collapseThree" class="collapse" aria-labelledby="headingThree" data-parent="#faqAccordion">
                <div class="card-body">
                    Pagamentos dos benefícios é feito no último dia útil de cada mês. 
                    O trabalhador custeará os serviços de vale-transporte através das empresas concessionárias de 4% (quatro por cento) para fins de desconto.
                </div>
            </div>
        </div>

        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center" id="headingFour">
                <h5 class="mb-0 w-100 d-flex justify-content-between align-items-center">
                    <span>Qual a jornada de trabalho?</span>
                    <button class="btn btn-link text-right collapsed" data-toggle="collapse" data-target="#collapseFour"
                        aria-expanded="false" aria-controls="collapseFour">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h5>
            </div>
            <div id="collapseFour" class="collapse" aria-labelledby="headingFour" data-parent="#faqAccordion">
                <div class="card-body">
                    A jornada de trabalho 40 horas semanais.
                </div>
            </div>
        </div>

        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center" id="headingFive">
                <h5 class="mb-0 w-100 d-flex justify-content-between align-items-center">
                    <span>Como faço para registrar meu ponto?</span>
                    <button class="btn btn-link text-right collapsed" data-toggle="collapse" data-target="#collapseFive"
                        aria-expanded="false" aria-controls="collapseFive">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h5>
            </div>
            <div id="collapseFive" class="collapse" aria-labelledby="headingFive" data-parent="#faqAccordion">
                <div class="card-body">
                    •	Temos um tablet na entrada da empresa.<br>
                    •	Abra o link que está no navegador Google Chrome.<br>
                    •	Clique em registro de entrada e saída.<br>
                    •	Coloque seu rosto dentro do quadro para ser reconhecido.<br>
                    •	O sistema procurará uma correspondência.<br>
                    •	Se sua correspondência estiver correta, sua entrada ou saída foi registrada com sucesso.<br>
                </div>
            </div>
        </div>

    </div>
</div>
{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const headers = document.querySelectorAll('.card-header');

        headers.forEach(function (header) {
            const button = header.querySelector('.btn-link');
            const icon = button.querySelector('.fas');

            button.addEventListener('click', function () {
                const isExpanded = button.getAttribute('aria-expanded') === 'true';
                icon.style.transform = isExpanded ? 'rotate(0deg)' : 'rotate(180deg)';
            });

            header.addEventListener('click', function (e) {
                if (!e.target.classList.contains('btn-link')) {
                    button.click();
                }
            });
        });
    });
</script>
{% endblock %}
{% endblock %}