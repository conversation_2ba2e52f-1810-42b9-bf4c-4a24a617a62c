{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}gestaoChamados{% endblock %}

{% block extra_styles %}{% endblock %}
{% block content %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<div class="container">
    <div class="row">
        <div class="col-lg-6 col-12">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3 id="atrasados-count">0</h3>
                    <p style="color: #fff;">Atrasados</p>
                </div>
                <div class="icon">
                    <i class="fa-solid fa-circle-exclamation"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-12">
            <div class="small-box bg-gradient-warning">
                <div class="inner" style="color: #fff;">
                    <h3 id="no-prazo-count">0</h3>
                    <p style="color: #fff;">No Prazo</p>
                </div>
                <div class="icon">
                    <i class="fa-solid fa-calendar-check"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-12">
            <div class="small-box bg-gradient-success">
                <div class="inner" style="color: #fff;">
                    <h3 id="encerrado-count">0</h3>
                    <p style="color: #fff;">Encerrado</p>
                </div>
                <div class="icon">
                    <i class="fa-solid fa-thumbs-up"></i>

                </div>
            </div>
        </div>
        <div class="col-lg-6 col-12">
            <div class="small-box bg-info">
                <div class="inner" style="color: #fff;">
                    <h3 id="total-chamados-count">0</h3>
                    <p style="color: #fff;">Total de Chamados</p>
                </div>
                <div class="icon">
                    <i class="fa-solid fa-chart-pie"></i>
                </div>
                <a href="#" class="small-box-footer" data-toggle="modal" data-target="#chamadosModal">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="input-group">
            <input type="text" id="search-input" class="form-control"
                placeholder="Buscar por Protocolo, Usuário Solicitante ou Assunto">
            <span class="input-icon"><i class="fa-solid fa-magnifying-glass"></i></span>
        </div>
    </div>
    <div class="row mt-4">
        {% if session.get('user_type') in [1, 7] %}
        <select id="setor-filter" class="select2" multiple="multiple" style="width: 33.33%;">
            <option value="">Filtrar por Setor</option>
            {% set unique_setores = chamados|map(attribute='setor_nome')|list|unique %}
            {% for setor in unique_setores %}
            <option value="{{ setor }}">{{ setor }}</option>
            {% endfor %}
        </select>
        {% endif %}

        <select id="status-filter" class="select2" multiple="multiple" style="width: 33.33%;">
            <option value="Aberto">Aberto</option>
            <option value="Em Tratativa">Em Tratativa</option>
            <option value="Retorno Operadora">Aguardando Retorno</option>
            <option value="Resolvido">Resolvido</option>
        </select>

        <select id="sla-filter" class="select2" multiple="multiple" style="width: 33.33%;">
            <option value="">Filtrar por SLA</option>
            <option value="Atrasado">Atrasado</option>
            <option value="Encerrado">Encerrado</option>
            <option value="No Prazo">No Prazo</option>
        </select>
    </div>

    <div class="row mt-4">
        {% if session.get('setor_id') in [1, 10, 12, 13] or session.get('user_type') in [1, 7] %}
        <select id="modalidade-filter" class="select2" multiple="multiple" style="width: 33.33%;">
            <option value="">Filtrar por Modalidade</option>
            <option value="Adesao">Adesão</option>
            <option value="PF">PF</option>
            <option value="PF_odonto">PF - Odontológico</option>
            <option value="PME">PME</option>
            <option value="PNE_odonto">PME - Odontológico</option>
        </select>

        <select id="operadora-filter" class="select2" multiple="multiple" style="width: 33.33%;">
            <option value="">Filtrar por Operadora</option>
            <option value="Aacl">AACL</option>
            <option value="Affix">Affix</option>
            <option value="Alice">Alice</option>
            <option value="Allcare">Allcare</option>
            <option value="Ameplan">Ameplan</option>
            <option value="Ampla">Ampla</option>
            <option value="Ana Costa">Ana Costa</option>
            <option value="Assim Saúde">Assim Saúde</option>
            <option value="Ativia">Ativia</option>
            <option value="Benevix">Benevix</option>
            <option value="Biovida">Biovida</option>
            <option value="Blue Med">Blue Med</option>
            <option value="Blue">Blue</option>
            <option value="Bradesco">Bradesco</option>
            <option value="Care Plus">Care Plus</option>
            <option value="Cnu">CNU</option>
            <option value="Corpe">Corpe</option>
            <option value="Cruz Azul">Cruz Azul</option>
            <option value="Dentalpar">Dentalpar</option>
            <option value="Garantia Saúde">Garantia Saúde</option>
            <option value="Go Care">Go Care</option>
            <option value="Grupo Amil">Grupo Amil</option>
            <option value="Grupo Notre Dame">Grupo Notre Dame</option>
            <option value="Hbc">HBC</option>
            <option value="Health Santaris">Health Santaris</option>
            <option value="Hebrom">Hebrom</option>
            <option value="Livri">Livri</option>
            <option value="Mais Dental">Mais Dental</option>
            <option value="Med Tour">Med Tour</option>
            <option value="Medical Health">Medical Health</option>
            <option value="Medsenior">Medsenior</option>
            <option value="Metlife">Metlife</option>
            <option value="Monthermonn">Monthermonn</option>
            <option value="New Leader">New Leader</option>
            <option value="Odontoprev">Odontoprev</option>
            <option value="Omint / Kipp">Omint / Kipp</option>
            <option value="Plansaude">Plansaúde</option>
            <option value="Plena">Plena</option>
            <option value="Plural Saúde">Plural Saúde</option>
            <option value="Porto">Porto</option>
            <option value="Porto Pet">Porto Pet</option>
            <option value="Prevent Senior">Prevent Senior</option>
            <option value="Qualicorp">Qualicorp</option>
            <option value="Safe Life">Safe Life</option>
            <option value="Sagrada Família">Sagrada Família</option>
            <option value="Sami">Sami</option>
            <option value="Santa Casa">Santa Casa</option>
            <option value="Santa Helena">Santa Helena</option>
            <option value="São Camilo">São Camilo</option>
            <option value="São Cristóvão">São Cristóvão</option>
            <option value="São Francisco">São Francisco</option>
            <option value="São Miguel">São Miguel</option>
            <option value="Saúde Beneficência">Saúde Beneficência</option>
            <option value="Seguros Unimed">Seguros Unimed</option>
            <option value="Sobam">Sobam</option>
            <option value="Sul América">Sul América</option>
            <option value="Supermed">Supermed</option>
            <option value="Tecgroup">Tecgroup</option>
            <option value="Total Medcare">Total Medcare</option>
            <option value="Trasmontano">Trasmontano</option>
            <option value="Única">Única</option>
            <option value="Uniconsult">Uniconsult</option>
            <option value="Unihosp">Unihosp</option>
            <option value="Unimeds Regionais">Unimeds - Regionais</option>
            <option value="Uniodonto">Uniodonto</option>
            <option value="Vera Cruz">Vera Cruz</option>
            <option value="Você Clube">Você Clube</option>
        </select>

        <select id="proposta-filter" class="select2" multiple="multiple" style="width: 33.33%;">
            <option value="">Proposta Cadastrada</option>
            <option value="Sim">Sim</option>
            <option value="Não">Não</option>
        </select>

        {% endif %}
    </div>

    <div class="row">
        <div class="col-md-12">
            <h3>Gestão de Chamados</h3>
            <div class="table-responsive">
                <table class="table table-striped" id="chamados-table">
                    <thead>
                        <tr>
                            <th>Protocolo</th>
                            <th>Usuário Solicitante</th>
                            <th>Assunto</th>
                            <th>Tipo Chamado</th>
                            <th>Setor</th>
                            <th>Status</th>
                            <th>Data de Abertura</th>
                            <th>Dias em Andamento</th>
                            <th>Última Interação</th>
                            <th>Data Limite Próxima Interação</th>
                            {% if session.get('setor_id') in [1, 10, 12, 13] or session.get('user_type') in [1, 7] %}
                            <th>Modalidade</th>
                            <th>Operadora</th>
                            <th>Proposta Cadastrada</th>
                            {% endif %}
                            <th>Usuário Responsável</th>
                            <th>SLA</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for chamado in chamados %}
                        <tr>
                            <td>{{ chamado.protocolo }}</td>
                            <td>{{ chamado.usuario_nome }}</td>
                            <td>{{ chamado.assunto }}</td>
                            <td>{{ chamado.tipo_chamado }}</td>
                            <td>{{ chamado.setor_nome }}</td>
                            <td>
                                <span
                                    class="badge {% if chamado.status == 'Aberto' %}badge-warning{% elif chamado.status == 'Em Tratativa' %}badge-info{% else %}badge-success{% endif %}">
                                    {{ chamado.status }}
                                </span>
                            </td>
                            <td>{{ chamado.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                            <td>{{ chamado.dias_aberto }}</td>
                            <td>
                                {% if chamado.ultima_interacao %}
                                {{ chamado.ultima_interacao.strftime('%d/%m/%Y %H:%M') }}
                                {% else %}
                                N/A
                                {% endif %}
                            </td>
                            <td>
                                {% if chamado.data_limite %}
                                {{ chamado.data_limite.strftime('%d/%m/%Y %H:%M') }}
                                {% else %}
                                --
                                {% endif %}
                            </td>
                            {% if session.get('setor_id') in [1, 10, 12, 13] or session.get('user_type') in [1, 7] %}
                            <td>{{ chamado.modalidade }}</td>
                            <td>{{ chamado.operadora }}</td>
                            <td>{{ chamado.prop_c }}</td>
                            {% endif %}
                            <td>{{ chamado.user_responsavel }}</td>
                            <td class="sla"
                                data-data-limite="{{ chamado.data_limite.isoformat() if chamado.data_limite else '' }}">
                            </td>
                            <td>
                                <a href="{{ url_for('detalhes_chamado', chamado_id=chamado.id) }}"
                                    class="btn btn-primary btn-sm">Ver Detalhado</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="modal fade" id="chamadosModal" tabindex="-1" aria-labelledby="chamadosModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg"> <!-- Altere para modal-lg ou modal-xl para um tamanho maior -->
            <div class="modal-content">
                <div class="modal-header"> <!-- Header do modal -->
                    <h5 class="modal-title" id="chamadosModalLabel">Detalhes dos Chamados</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="chart-title text-center">Total de Chamados</h6>
                            <div class="d-flex">
                                <div class="chart-legend" id="statusChartLegend"></div>
                                <div class="chart-container">
                                    <canvas id="statusChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="chart-title text-center">SLA (Atrasados e No Prazo)</h6>
                            <div class="d-flex">
                                <div class="chart-legend" id="slaChartLegend"></div>
                                <div class="chart-container">
                                    <canvas id="slaChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- jQuery (Necessário para Select2) -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

<!-- ColResizable -->
<script src="https://cdn.jsdelivr.net/npm/colresizable@1.6.0/colResizable-1.6.min.js"></script>

<!-- Select2 JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const searchInput = document.getElementById('search-input');
        const chamadosTable = document.getElementById('chamados-table').getElementsByTagName('tbody')[0];
        const rows = chamadosTable.getElementsByTagName('tr');

        let statusCounts = {};
        let slaCounts = {
            'No Prazo': 0,
            'Atrasado': 0,
            'Encerrado': 0
        };

        const now = new Date();

        // Função para calcular e exibir o SLA
        function calcularSLA() {
            slaCounts = { 'No Prazo': 0, 'Atrasado': 0, 'Encerrado': 0 }; // Resetar os contadores de SLA

            for (let row of rows) {
                const status = row.cells[5].textContent.trim(); // Coluna de status
                const dataLimiteStr = row.querySelector('.sla').getAttribute('data-data-limite');
                const dataLimite = dataLimiteStr ? new Date(dataLimiteStr) : null;
                const slaCell = row.querySelector('.sla'); // Celula onde o SLA será mostrado

                // Contagem de status - Adiciona automaticamente o status que estiver na tabela
                if (statusCounts.hasOwnProperty(status)) {
                    statusCounts[status]++;
                } else {
                    statusCounts[status] = 1; // Se o status não existir, inicializa a contagem
                }

                // Verifica se o status é "Resolvido", então marca como "Encerrado" independente da data
                if (status === 'Resolvido') {
                    slaCell.innerHTML = '<span class="badge badge-success">Encerrado</span>';
                    slaCounts['Encerrado']++;
                    row.setAttribute('data-sla', 'Encerrado'); // Atribui "Encerrado" como valor de SLA
                }
                // Verifica se o status é "Aguardando Solicitante", então marca como "No Prazo" independente da data
                else if (status === 'Aguardando Solicitante') {
                    slaCell.innerHTML = '<span class="badge badge-warning">No Prazo</span>';
                    slaCounts['No Prazo']++;
                    row.setAttribute('data-sla', 'No Prazo'); // Atribui "No Prazo" como valor de SLA
                }
                // Calcula o SLA com base na data limite, se existir
                else if (dataLimite) {
                    if (dataLimite < now) {
                        slaCell.innerHTML = '<span class="badge badge-danger">Atrasado</span>';
                        slaCounts['Atrasado']++;
                        row.setAttribute('data-sla', 'Atrasado'); // Adiciona atributo data-sla para facilitar o filtro
                    } else {
                        slaCell.innerHTML = '<span class="badge badge-warning">No Prazo</span>';
                        slaCounts['No Prazo']++;
                        row.setAttribute('data-sla', 'No Prazo'); // Atribui atributo data-sla para facilitar o filtro
                    }
                } else {
                    slaCell.innerHTML = 'N/A';
                    row.setAttribute('data-sla', 'N/A'); // Atribui "N/A" como valor de SLA
                }
            }

            // Atualiza os contadores de SLA na interface
            document.getElementById('atrasados-count').textContent = slaCounts['Atrasado'];
            document.getElementById('no-prazo-count').textContent = slaCounts['No Prazo'];
            document.getElementById('encerrado-count').textContent = slaCounts['Encerrado'];
        }

        $('#chamados-table').colResizable({
            liveDrag: true,
            resizeMode: 'fit',
            minWidth: 10,
            onDragEnd: function () {
                console.log('Redimensionamento finalizado');
                $('#chamados-table').css('table-layout', 'auto'); // Atualiza layout
            }
        });

        // Função para filtrar a tabela com base nos filtros de status, SLA e busca
        function filterTable() {
            const selectedSetor = $('#setor-filter').val();
            const selectedStatuses = $('#status-filter').val();
            const selectedSLA = $('#sla-filter').val();
            const selectedModalidade = $('#modalidade-filter').val();
            const selectedOperadora = $('#operadora-filter').val();
            const selectedProposta = $('#proposta-filter').val();
            const searchTerm = searchInput.value.toLowerCase(); // Valor da busca, convertido para minúsculas

            for (let row of rows) {
                const setor = row.cells[4].textContent.trim();
                const status = row.cells[5].textContent.trim();
                const sla = row.getAttribute('data-sla');
                const protocolo = row.cells[0].textContent.toLowerCase(); // Protocolo
                const usuarioSolicitante = row.cells[1].textContent.toLowerCase(); // Usuário Solicitante
                const assunto = row.cells[2].textContent.toLowerCase(); // Assunto

                let shouldShow = true;

                // Verifica se o termo de busca corresponde a protocolo, usuário solicitante ou assunto
                if (searchTerm && !(protocolo.includes(searchTerm) || usuarioSolicitante.includes(searchTerm) || assunto.includes(searchTerm))) {
                    shouldShow = false;
                }

                // Verifica se o setor corresponde ao filtro
                if (selectedSetor && selectedSetor.length > 0 && !selectedSetor.includes(setor)) {
                    shouldShow = false;
                }

                // Verifica se o status corresponde ao filtro
                if (selectedStatuses && selectedStatuses.length > 0 && !selectedStatuses.includes(status)) {
                    shouldShow = false;
                }

                // Verifica se o SLA corresponde ao filtro
                if (selectedSLA && selectedSLA.length > 0 && !selectedSLA.includes(sla)) {
                    shouldShow = false;
                }

                // Verifica se a coluna Modalidade está presente antes de tentar o filtro
                const modalidadeCell = row.cells[10];
                if (modalidadeCell && selectedModalidade && selectedModalidade.length > 0) {
                    const modalidade = modalidadeCell.textContent.trim();
                    if (!selectedModalidade.includes(modalidade)) {
                        shouldShow = false;
                    }
                }

                    const operadoraCell = row.cells[11];
                    if (operadoraCell && selectedOperadora && selectedOperadora.length > 0) {
                        const operadora = operadoraCell.textContent.trim();
                        if (!selectedOperadora.includes(operadora)) {
                            shouldShow = false;
                        }
                    }

                // Verifica se a coluna Operadora está presente antes de tentar o filtro
                const propostaCell = row.cells[12];
                if (propostaCell && selectedProposta && selectedProposta.length > 0) {
                    const proposta = propostaCell.textContent.trim();
                    if (!selectedProposta.includes(proposta)) {
                        shouldShow = false;
                    }
                }

                // Mostra ou esconde a linha com base nos filtros
                row.style.display = shouldShow ? '' : 'none';
            }
        }

        // Inicializa o select2 para os filtros
        $('#setor-filter').select2({
            placeholder: "Filtrar por Setor",
            allowClear: true
        });

        $('#status-filter').select2({
            placeholder: "Filtrar por Status",
            allowClear: true
        });

        $('#sla-filter').select2({
            placeholder: "Filtrar por SLA",
            allowClear: true
        });

        $('#modalidade-filter').select2({
            placeholder: "Filtrar por Modalidade",
            allowClear: true
        });

        $('#operadora-filter').select2({
            placeholder: "Filtrar por Operadora",
            allowClear: true
        });

        $('#proposta-filter').select2({
            placeholder: "Proposta Cadastrada",
            allowClear: true
        });

        // Aplica a lógica de filtro ao mudar os selects e quando a busca for alterada
        $('#setor-filter').on('change', filterTable);
        $('#status-filter').on('change', filterTable);
        $('#sla-filter').on('change', filterTable);
        $('#modalidade-filter').on('change', filterTable);
        $('#operadora-filter').on('change', filterTable);
        $('#proposta-filter').on('change', filterTable);
        searchInput.addEventListener('input', filterTable); // Adiciona evento para o campo de busca

        // Recalcula o SLA quando a página é carregada e depois filtra a tabela
        calcularSLA();
        filterTable();

        // Atualizar total de chamados
        document.getElementById('total-chamados-count').textContent = rows.length;

        // Dados para o gráfico de Status
        const statusData = {
            labels: Object.keys(statusCounts), // Pega automaticamente os nomes dos status
            datasets: [{
                data: Object.values(statusCounts), // Pega os valores associados a cada status
                backgroundColor: ['#FF6384', '#36A2EB', '#4BC0C0', '#FFCE56', '#8E44AD'] // Cores para cada status
            }]
        };

        // Dados para o gráfico de SLA
        const slaData = {
            labels: Object.keys(slaCounts),
            datasets: [{
                data: Object.values(slaCounts),
                backgroundColor: ['#FFCE56', '#dc3545', '#28a745'] // Cores para No Prazo, Atrasado e Encerrado
            }]
        };

        // Função para calcular a porcentagem
        function calculatePercentage(value, total) {
            return ((value / total) * 100).toFixed(2) + '%';
        }

        // Inicializar o gráfico de Status com rótulos personalizados
        const ctxStatus = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(ctxStatus, {
            type: 'pie',
            data: statusData,
            options: {
                responsive: true,
                plugins: {
                    datalabels: {
                        formatter: (value, ctx) => {
                            const total = ctx.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                            const percentage = calculatePercentage(value, total);
                            return `${value}\n(${percentage}%)`; // Exibe número e % em linhas separadas
                        },
                        color: '#fff',
                        font: {
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: false // Esconde a legenda padrão
                    }
                }
            },
            plugins: [ChartDataLabels]
        });

        // Inicializar o gráfico de SLA com rótulos personalizados
        const ctxSla = document.getElementById('slaChart').getContext('2d');
        const slaChart = new Chart(ctxSla, {
            type: 'pie',
            data: slaData,
            options: {
                responsive: true,
                plugins: {
                    datalabels: {
                        formatter: (value, ctx) => {
                            const total = ctx.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                            const percentage = calculatePercentage(value, total);
                            return `${value}\n(${percentage}%)`; // Exibe número e % em linhas separadas
                        },
                        color: '#fff',
                        font: {
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: false // Esconde a legenda padrão
                    }
                }
            },
            plugins: [ChartDataLabels]
        });

        // Função para gerar legendas personalizadas
        function generateLegend(chart, legendContainerId) {
            const legendContainer = document.getElementById(legendContainerId);
            const labels = chart.data.labels;
            const colors = chart.data.datasets[0].backgroundColor;
            labels.forEach((label, index) => {
                const color = colors[index];
                const li = document.createElement('li');
                li.innerHTML = `<span style="background-color:${color}; display:inline-block; width:12px; height:12px; margin-right:10px;"></span>${label}`;
                legendContainer.appendChild(li);
            });
        }

        // Gerar legendas personalizadas para o gráfico de Status e SLA
        generateLegend(statusChart, 'statusChartLegend');
        generateLegend(slaChart, 'slaChartLegend');
    });
</script>
{% endblock %}
