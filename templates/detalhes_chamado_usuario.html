<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1.0/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .direct-chat-msg.pending .direct-chat-text {
            opacity: 0.7;
        }

        .bi-check-all.pending {
            color: #aaa !important;
        }

        .bi-check-all.visualizado {
            color: #2fcc00;
        }

        .bi-check-all.nao-visualizado {
            color: #777;
        }
    </style>
</head>

<body class="detalhesChamado">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <!-- Informações do Chamado -->
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">Detalhes do Chamado - {{ chamado.protocolo }}</h3>
                    </div>

                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs" id="chamadoTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="dados-basicos-tab" data-toggle="tab" href="#dados-basicos"
                                role="tab" aria-controls="dados-basicos" aria-selected="true">Dados Básicos</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="documentos-tab" data-toggle="tab" href="#documentos" role="tab"
                                aria-controls="documentos" aria-selected="false">Documentos</a>
                        </li>

                        {% if chamado.setor_id in [1, 9, 10, 12, 13] %}
                        <li class="nav-item">
                            <a class="nav-link" id="formulario-tab" data-toggle="tab" href="#formulario" role="tab"
                                aria-controls="formulario" aria-selected="false">Formulário</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="beneficiarios-tab" data-toggle="tab" href="#beneficiarios"
                                role="tab" aria-controls="beneficiarios" aria-selected="false">Beneficiários</a>
                        </li>
                        {% endif %}
                    </ul>

                    <!-- Tab panes -->
                    <div class="tab-content">
                        <!-- Dados Básicos -->
                        <div class="tab-pane fade show active" id="dados-basicos" role="tabpanel"
                            aria-labelledby="dados-basicos-tab">
                            <div class="card-body">
                                <dl class="row">
                                    {% if chamado.setor_id in [1, 9, 10, 12, 13] %}
                                    <dt class="col-sm-3">Proposta Cadastrada:</dt>
                                    <dd class="col-sm-9">
                                        <span id="statusProposta">{{ "Cadastrada" if chamado.prop_c == "Sim" else "Não
                                            Cadastrada" }}
                                        </span>
                                    </dd>
                                    {% endif %}

                                    <dt class="col-sm-3">Protocolo:</dt>
                                    <dd class="col-sm-9">{{ chamado.protocolo }}</dd>

                                    <dt class="col-sm-3">Assunto:</dt>
                                    <dd class="col-sm-9">{{ chamado.assunto }}</dd>

                                    <dt class="col-sm-3">Solicitante:</dt>
                                    <dd class="col-sm-9">{{ chamado.usuario_nome }}</dd>

                                    <dt class="col-sm-3">Setor:</dt>
                                    <dd class="col-sm-9">{{ chamado.setor_nome }}</dd>

                                    <dt class="col-sm-3">Status:</dt>
                                    <dd class="col-sm-9">{{ chamado.status }}</dd>

                                    <dt class="col-sm-3">Data de Criação:</dt>
                                    <dd class="col-sm-9">{{ chamado.created_at.strftime('%d/%m/%Y %H:%M') }}</dd>

                                    <dt class="col-sm-3">Data Limite Próxima Interação:</dt>
                                    <dd class="col-sm-9">{{ chamado.data_limite.strftime('%d/%m/%Y %H:%M') }}</dd>

                                    <dt class="col-sm-3">Descrição:</dt>
                                    <dd class="col-sm-9">{{ chamado.description }}</dd>

                                    <!-- Lógica de Status -->
                                    <div class="progress-container d-flex justify-content-between align-items-center"
                                        style="position: relative; height: 200px;">
                                        <!-- Progress sections -->
                                        <div class="progress-step" style="position: relative; flex: 1; height: 10px;">
                                            <img src="/static/images/envelope.png" alt="Solicitação Aberta"
                                                style="position: absolute; top: -70px; left: 50%; transform: translateX(-50%); width: 50px; height: 50px;">
                                            <div class="progress" style="background-color: #ccc; height: 10px;">
                                                <div class="progress-bar bg-success {{ 'w-100' if chamado.status != 'Aberto' else 'w-50' }}"
                                                    role="progressbar"></div>
                                            </div>
                                            <i class="bi {{ 'bi-check-circle-fill text-success' if chamado.status in ['Aberto', 'Em Tratativa', 'Aguardando Solicitante', 'Retorno Operadora', 'Resolvido'] else 'bi-circle-fill text-secondary' }}"
                                                style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                            <small
                                                style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Solicitação
                                                Aberta</small>
                                        </div>

                                        <div class="progress-step" style="position: relative; flex: 1; height: 10px;">
                                            <img src="/static/images/email.png" alt="Em Tratativa"
                                                style="width: 50px; height: 50px; position: absolute; top: -80px; left: 50%; transform: translateX(-50%); padding: 0;">
                                            <div class="progress" style="background-color: #ccc; height: 10px;">
                                                <div class="progress-bar bg-success {{ 'w-100' if chamado.status in ['Aguardando Solicitante', 'Retorno Operadora', 'Resolvido'] else 'w-50' if chamado.status == 'Em Tratativa' else 'w-0' }}"
                                                    role="progressbar"></div>
                                            </div>
                                            <i class="bi {{ 'bi-check-circle-fill text-success' if chamado.status in ['Em Tratativa', 'Aguardando Solicitante', 'Retorno Operadora', 'Resolvido'] else 'bi-circle-fill text-secondary' }}"
                                                style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                            <small
                                                style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Em
                                                Andamento</small>
                                        </div>

                                        <div class="progress-step" style="position: relative; flex: 1; height: 10px;">
                                            <img src="/static/images/userWaiting.png" alt="Aguardando Solicitante"
                                                style="width: 50px; height: 50px; position: absolute; top: -80px; left: 50%; transform: translateX(-50%); padding: 0;">
                                            <div class="progress" style="background-color: #ccc; height: 10px;">
                                                <div class="progress-bar bg-success {{ 'w-100' if chamado.status in ['Retorno Operadora', 'Resolvido'] else 'w-50' if chamado.status == 'Aguardando Solicitante' else 'w-0' }}"
                                                    role="progressbar"></div>
                                            </div>
                                            <i class="bi {{ 'bi-check-circle-fill text-success' if chamado.status in ['Aguardando Solicitante', 'Retorno Operadora', 'Resolvido'] else 'bi-circle-fill text-secondary' }}"
                                                style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                            <small
                                                style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Aguardando
                                                Resposta do Solicitante</small>
                                        </div>

                                        <div class="progress-step" style="position: relative; flex: 1; height: 10px;">
                                            <img src="/static/images/esperando.png" alt="Retorno Operadora"
                                                style="width: 50px; height: 50px; position: absolute; top: -75px; left: 50%; transform: translateX(-50%); padding: 0;">
                                            <div class="progress" style="background-color: #ccc; height: 10px;">
                                                <div class="progress-bar bg-success {{ 'w-100' if chamado.status == 'Resolvido' else 'w-50' if chamado.status == 'Retorno Operadora' else 'w-0' }}"
                                                    role="progressbar"></div>
                                            </div>
                                            <i class="bi {{ 'bi-check-circle-fill text-success' if chamado.status in ['Retorno Operadora', 'Resolvido'] else 'bi-circle-fill text-secondary' }}"
                                                style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                            <small
                                                style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Aguardando
                                                Retorno da Operadora</small>
                                        </div>

                                        <div class="progress-step" style="position: relative; flex: 1; height: 10px;">
                                            <img src="/static/images/resolvido.png" alt="Resolvido"
                                                style="width: 50px; height: 50px; position: absolute; top: -70px; left: 50%; transform: translateX(-50%); padding: 0;">
                                            <div class="progress" style="background-color: #ccc; height: 10px;">
                                                <div class="progress-bar bg-success {{ 'w-100' if chamado.status == 'Resolvido' else 'w-0' }}"
                                                    role="progressbar"></div>
                                            </div>
                                            <i class="bi {{ 'bi-check-circle-fill text-success' if chamado.status == 'Resolvido' else 'bi-circle-fill text-secondary' }}"
                                                style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                            <small
                                                style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Resolvido</small>
                                        </div>
                                    </div>

                                    {% if chamado.setor_id in [1, 9, 10, 12, 13] %}
                                    <!-- Lógica de Sub Status - Usina da Proposta -->
                                    <div class="sub-progress-container" style="margin-top: 20px;">
                                        {% if session.get('setor_id') in [1, 9, 10, 12, 13] or session.get('user_type')
                                        in
                                        [1,
                                        7] %}
                                        <!-- Sub-status para o status "Em Tratativa" -->
                                        {% if chamado.status == 'Em Tratativa' %}
                                        <div class="d-flex justify-content-between align-items-center"
                                            style="position: relative; height: 100px;">
                                            <!-- Conferência -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/analiseDocs.png" alt="Conferência"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 49%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status in ['Emissão', 'Emitida', 'Emitida com Pendência'] else 'w-50' if chamado.sub_status == 'Conferência' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Conferência', 'Emissão', 'Emitida', 'Emitida com Pendência'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Conferência</small>
                                            </div>

                                            <!-- Emissão -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/emitindo.png" alt="Emissão"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 50%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status in ['Emitida', 'Emitida com Pendência'] else 'w-50' if chamado.sub_status == 'Emissão' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Emissão', 'Emitida', 'Emitida com Pendência'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Emissão</small>
                                            </div>

                                            <!-- Emitida -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/emitida.png" alt="Emitida"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 51%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status == 'Emitida com Pendência' else 'w-50' if chamado.sub_status == 'Emitida' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Emitida', 'Emitida com Pendência'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Emitida</small>
                                            </div>

                                            <!-- Emitida com Pendência -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/emitidaPendencia.png"
                                                    alt="Emitida com Pendência"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 51%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status == 'Emitida com Pendência' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status == 'Emitida com Pendência' else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Emitida
                                                    com Pendência</small>
                                            </div>
                                        </div>
                                        {% endif %}

                                        <!-- Sub-status para o status "Aguardando Solicitante" -->
                                        {% if chamado.status == 'Aguardando Solicitante' %}
                                        <div class="d-flex justify-content-between align-items-center"
                                            style="position: relative; height: 100px;">
                                            <!-- Pendência de Documentos/Informações -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/pendenciaDocs.png"
                                                    alt="Pendência de Documentos"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 50%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status == 'Aguardando Assinatura' else 'w-50' if chamado.sub_status == 'Pendência de Documentos/Informações' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Pendência de Documentos/Informações', 'Aguardando Assinatura'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Pendência
                                                    de Documentos/Informações</small>
                                            </div>

                                            <!-- Aguardando Assinatura -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/aguardandoAssinatura.png"
                                                    alt="Aguardando Assinatura"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 50%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status == 'Aguardando Assinatura' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status == 'Aguardando Assinatura' else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Aguardando
                                                    Assinatura</small>
                                            </div>
                                        </div>
                                        {% endif %}

                                        <!-- Sub-status para o status "Aguardando Retorno da Operadora" -->
                                        {% if chamado.status == 'Retorno Operadora' %}
                                        <div class="d-flex justify-content-between align-items-center"
                                            style="position: relative; height: 100px;">

                                            <!-- Análise de Documentação -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/analiseDocs.png" alt="Análise de Documentação"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 49%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status in ['Análise Técnica', 'Análise Médica', 'Aguardando Liberação de Boleto', 'Aguardando Pagamento do Boleto', 'Implantada sem Pagamento', 'Implantada com Pagamento'] else 'w-50' if chamado.sub_status == 'Análise de Documentação' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Análise de Documentação', 'Análise Técnica', 'Análise Médica', 'Aguardando Liberação de Boleto', 'Aguardando Pagamento do Boleto', 'Implantada sem Pagamento', 'Implantada com Pagamento'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Análise
                                                    de Documentação</small>
                                            </div>

                                            <!-- Análise Técnica -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/analiseTec.png" alt="Análise Técnica"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 51%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status in ['Análise Médica', 'Aguardando Liberação de Boleto', 'Aguardando Pagamento do Boleto', 'Implantada sem Pagamento', 'Implantada com Pagamento'] else 'w-50' if chamado.sub_status == 'Análise Técnica' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Análise Técnica', 'Análise Médica', 'Aguardando Liberação de Boleto', 'Aguardando Pagamento do Boleto', 'Implantada sem Pagamento', 'Implantada com Pagamento'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Análise
                                                    Técnica</small>
                                            </div>

                                            <!-- Análise Médica -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/analiseMedic.png" alt="Análise Médica"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 51%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status in ['Aguardando Liberação de Boleto', 'Aguardando Pagamento do Boleto', 'Implantada sem Pagamento', 'Implantada com Pagamento'] else 'w-50' if chamado.sub_status == 'Análise Médica' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Análise Médica', 'Aguardando Liberação de Boleto', 'Aguardando Pagamento do Boleto', 'Implantada sem Pagamento', 'Implantada com Pagamento'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Análise
                                                    Médica</small>
                                            </div>

                                            <!-- Aguardando Liberação de Boleto -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/aguardandoBoleto.png"
                                                    alt="Aguardando Liberação de Boleto"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 51%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status in ['Aguardando Pagamento do Boleto', 'Implantada sem Pagamento', 'Implantada com Pagamento'] else 'w-50' if chamado.sub_status == 'Aguardando Liberação de Boleto' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Aguardando Liberação de Boleto', 'Aguardando Pagamento do Boleto', 'Implantada sem Pagamento', 'Implantada com Pagamento'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Aguardando
                                                    Liberação de Boleto</small>
                                            </div>

                                            <!-- Aguardando Pagamento do Boleto -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/aguardandoBoleto.png"
                                                    alt="Aguardando Pagamento do Boleto"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 51%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status in ['Implantada sem Pagamento', 'Implantada com Pagamento'] else 'w-50' if chamado.sub_status == 'Aguardando Pagamento do Boleto' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Aguardando Pagamento do Boleto', 'Implantada sem Pagamento', 'Implantada com Pagamento'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Aguardando
                                                    Pagamento do Boleto</small>
                                            </div>

                                            <!-- Implantada sem Pagamento -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/implantadaSPagamento.png"
                                                    alt="Implantada sem Pagamento"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 48%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status == 'Implantada com Pagamento' else 'w-50' if chamado.sub_status == 'Implantada sem Pagamento' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Implantada sem Pagamento', 'Implantada com Pagamento'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Implantada
                                                    sem Pagamento</small>
                                            </div>

                                            <!-- Implantada com Pagamento -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/implantadaPaga.png"
                                                    alt="Implantada com Pagamento"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 51%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status == 'Implantada com Pagamento' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status == 'Implantada com Pagamento' else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Implantada
                                                    com Pagamento</small>
                                            </div>
                                        </div>
                                        {% endif %}

                                        <!-- Sub-status para o status "Resolvido" -->
                                        {% if chamado.status == 'Resolvido' %}
                                        <div class="d-flex justify-content-between align-items-center"
                                            style="position: relative; height: 100px;">

                                            <!-- Proposta Emitida -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/emitida.png" alt="Proposta Emitida"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 51%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status in ['Proposta Emitida', 'Proposta Declinada', 'Cancelada pelo Solicitante', 'Proposta Expirada', 'Proposta Finalizada'] else 'w-100' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Proposta Emitida', 'Proposta Declinada', 'Cancelada pelo Solicitante', 'Proposta Expirada', 'Proposta Finalizada'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Proposta
                                                    Emitida
                                                </small>
                                            </div>

                                            <!-- Proposta Declinada -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/propostaDeclinada.png" alt="Proposta Declinada"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 51%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status in ['Cancelada pelo Solicitante', 'Proposta Expirada', 'Proposta Finalizada'] else 'w-100' if chamado.sub_status == 'Proposta Declinada' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Proposta Declinada', 'Cancelada pelo Solicitante', 'Proposta Expirada', 'Proposta Finalizada'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Proposta
                                                    Declinada</small>
                                            </div>

                                            <!-- Cancelada pelo Solicitante -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/propostaCancelada.png"
                                                    alt="Cancelada pelo Solicitante"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 51%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status in ['Proposta Expirada', 'Proposta Finalizada'] else 'w-100' if chamado.sub_status == 'Cancelada pelo Solicitante' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Cancelada pelo Solicitante', 'Proposta Expirada', 'Proposta Finalizada'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Cancelada
                                                    pelo Solicitante</small>
                                            </div>

                                            <!-- Proposta Expirada -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/propostaExpirada.png" alt="Proposta Expirada"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 50%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status == 'Proposta Finalizada' else 'w-100' if chamado.sub_status == 'Proposta Expirada' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status in ['Proposta Expirada', 'Proposta Finalizada'] else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Proposta
                                                    Expirada</small>
                                            </div>

                                            <!-- Proposta Finalizada -->
                                            <div class="progress-step"
                                                style="position: relative; flex: 1; height: 10px;">
                                                <img src="/static/images/erro_cadastral.png" alt="Proposta Expirada"
                                                    style="width: 50px; height: 50px; position: absolute; top: -75px; left: 50%; transform: translateX(-50%); padding: 0;">
                                                <div class="progress" style="background-color: #ccc; height: 10px;">
                                                    <div class="progress-bar bg-info {{ 'w-100' if chamado.sub_status == 'Proposta Finalizada' else 'w-0' }}"
                                                        role="progressbar"></div>
                                                </div>
                                                <i class="bi {{ 'bi-check-circle-fill text-info' if chamado.sub_status == 'Proposta Finalizada' else 'bi-circle-fill text-secondary' }}"
                                                    style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); font-size: 24px;"></i>
                                                <small
                                                    style="position: absolute; top: 30px; left: 50%; transform: translateX(-50%);">Proposta
                                                    Finalizada por Divergência Cadastral</small>
                                            </div>
                                        </div>
                                        {% endif %}
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                </dl>
                            </div>
                        </div>

                        <!-- Documentos -->
                        <div class="tab-pane fade" id="documentos" role="tabpanel" aria-labelledby="documentos-tab">
                            <div class="card-body">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th class="cName">Nome</th>
                                            <th class="cArquivo">Arquivo</th>
                                            <th class="cData">Data</th>
                                            <th class="cAction">Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if ticket_files %}
                                        {% for file in ticket_files %}
                                        <tr>
                                            <td class="cName">{{ file.nome_arquivo }}</td>
                                            <td class="cArquivo">{{ file.arquivo }}</td>
                                            <td class="cData">{{ file.data_upload }}</td>
                                            <td class="cAction">
                                                <a href="{{ file.url_arquivo }}" class="btn btn-sm btn-primary" download
                                                    style="color: #fff;">
                                                    <i class="fa-solid fa-cloud-arrow-down"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                        {% else %}
                                        <tr>
                                            <td colspan="4">Nenhum arquivo encontrado.</td>
                                        </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                            <!-- Botão para adicionar novo arquivo -->
                            <div class="form-group-btns">
                                <div class="form-group">
                                    <button type="button" class="btn btn-secondary" id="add-file-btn">Anexar
                                        Arquivos</button>
                                    <!-- Ícone de informação -->
                                    <div class="info-icon">
                                        ?
                                        <div class="info-tooltip">O arquivo deve ser adicionado separadamente.</div>
                                    </div>
                                </div>

                                <a id="download-all" href="{{ url_for('download_all_files', ticket_id=ticket_id) }}"
                                    class="btn btn-primary">
                                    <i class="fa-solid fa-file-zipper"></i> Baixar Todos
                                </a>
                            </div>



                            <!-- Lista de arquivos -->
                            <div id="file-list"></div>
                        </div>

                        <!-- Formulário -->
                        <div class="tab-pane fade" id="formulario" role="tabpanel" aria-labelledby="formulario-tab">
                            <div class="card-body">
                                {% if formulario %}
                                <h5>Dados do Formulário</h5>
                                <dl class="row">
                                    {% for key, value in formulario.items() %}
                                    <dt class="col-sm-3">{{ key }}:</dt>
                                    <dd class="col-sm-9">
                                        {% if key == 'Código da Proposta' and (value == "--" or not value) %}
                                        <span id="cod-proposta-display">{{ value }}</span>
                                        {% else %}
                                        {{ value }}
                                        {% endif %}
                                    </dd>
                                    {% endfor %}
                                </dl>
                                {% else %}
                                <p>Nenhum formulário encontrado para este chamado.</p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Beneficiários -->
                        <div class="tab-pane fade" id="beneficiarios" role="tabpanel"
                            aria-labelledby="beneficiarios-tab">
                            <div class="card-body">
                                {% if beneficiarios %}
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Tipo</th>
                                            <th>Plano</th>
                                            <th>Acomodação</th>
                                            <th>Faixa Etária</th>
                                            <th>Grau de Parentesco</th>
                                            <th>Estado Civil</th>
                                            <th>Data de Nascimento</th>
                                            <th>Idade</th>
                                            <th>Telefone</th>
                                            <th>Email</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for beneficiario in beneficiarios %}
                                        <tr>
                                            <td>{{ beneficiario.nome }}</td>
                                            <td>{{ beneficiario.tipo_beneficiario }}</td>
                                            <td>{{ beneficiario.plano }}</td>
                                            <td>{{ beneficiario.acomodacao }}</td>
                                            <td>{{ beneficiario.faixa_etaria }}</td>
                                            <td>{{ beneficiario.grau_parentesco }}</td>
                                            <td>{{ beneficiario.civil }}</td>
                                            <td>{{ beneficiario.dt_nasci.strftime('%d/%m/%Y') }}</td>
                                            <td>{{ beneficiario.idade }}</td>
                                            <td>{{ beneficiario.telefone }}</td>
                                            <td>{{ beneficiario.email }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                                {% else %}
                                <p>Nenhum beneficiário encontrado para este formulário.</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat do Chamado -->
                <div class="card card-primary direct-chat direct-chat-primary mt-4">
                    <div class="card-header-chat">
                        <h3 class="card-title">Chat do Chamado</h3>
                        <div class="card-tools">
                            <span data-toggle="tooltip" title="Mensagens" class="badge badge-light">
                                {{ mensagens|length }}
                            </span>
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>

                    <div class="card-body-chat">
                        <!-- Mensagens do Chat -->
                        <div class="direct-chat-messages">
                            {% for mensagem in mensagens %}
                            <div class="direct-chat-msg {% if mensagem.user_id == session.user_id %}right{% else %}left{% endif %}"
                                data-mensagem-id="{{ mensagem.id }}">
                                <div class="direct-chat-infos clearfix">
                                    <span
                                        class="direct-chat-name {% if mensagem.user_id == session.user_id %}float-right{% else %}float-left{% endif %}">{{
                                        mensagem.usuario_nome }}</span>
                                    <span
                                        class="direct-chat-timestamp {% if mensagem.user_id == session.user_id %}float-left{% else %}float-right{% endif %}">{{
                                        mensagem.created_at.strftime('%d/%m/%Y %H:%M') }}</span>
                                </div>
                                <img class="direct-chat-img" src="{{ mensagem.profile_image_url }}" alt="User Image">
                                <div class="direct-chat-text">
                                    {{ mensagem.mensagem|safe }}
                                    {% if mensagem.user_id == session.user_id %}
                                    <i class="bi bi-check-all check-icon"></i>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Caixa de Mensagem -->
                    <div class="card-footer">
                        <div class="input-group">
                            <textarea id="chat-message" placeholder="Digite sua mensagem..." class="form-control"
                                rows="1" style="resize: none;"></textarea>
                            <span class="input-group-append">
                                <img id="send-chat-message"
                                    src="https://github.com/BrazilHealth/images-brh/blob/main/sending.png?raw=true"
                                    alt="Enviar" class="send-button">
                            </span>
                        </div>

                        <!-- Modal de Formatação -->
                        <div id="text-format-modal"
                            style="display: none; position: absolute; background-color: #f4f6f9; color: #fff; padding: 5px; border-radius: 5px;">
                            <button id="bold-btn" class="btn btn-dark btn-sm"><i class="bi bi-type-bold"></i></button>
                            <button id="italic-btn" class="btn btn-dark btn-sm"><i
                                    class="bi bi-type-italic"></i></button>
                            <button id="strike-btn" class="btn btn-dark btn-sm"><i
                                    class="bi bi-type-strikethrough"></i></button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap e Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Select2 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

    <!-- Socket.io com integridade -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"
        integrity="sha512-q/dWJ3kcmjBLU4Qc47E4A9kTB4m3wuTY7vkFJDTZKjTs8jhyGQnaUrxa0Ytd0ssMZhbNua9hE+E7Qv1j+DyZwA=="
        crossorigin="anonymous"></script>

    <!-- Marked.js para formatação de markdown -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <!-- AdminLTE -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1.0/dist/js/adminlte.min.js"></script>

    <!-- AdminLTE -->
    <script>
        function enableEdit() {
            document.getElementById('cod-proposta-display').style.display = 'none';
            document.getElementById('edit-button').style.display = 'none';
            document.getElementById('edit-form').style.display = 'block';
        }

        document.addEventListener("DOMContentLoaded", function () {
            console.log("DOM carregado.");
            $('#propostaStatus').select2();

            var chatMessageInput = document.getElementById('chat-message');
            var textFormatModal = document.getElementById('text-format-modal');
            var chamadoStatus = "{{ chamado.status }}"; // Obtenha o status do chamado via template

            // Verificar se o status do chamado é "Resolvido" para tornar o campo de entrada somente leitura
            if (chamadoStatus === "Resolvido") {
                chatMessageInput.setAttribute("readonly", true);
            }

            let inactivityInterval; // Intervalo para monitorar inatividade
            const INACTIVITY_LIMIT = 30000; // 30 segundos
            let userActive = false; // Estado para verificar se o usuário está ativo
            let pendingMessages = []; // Para armazenar mensagens pendentes durante desconexões
            let reconnectAttempt = 0;
            const MAX_RECONNECT_ATTEMPTS = 5;

            const socket = io({
                transports: ['websocket'], // Usar WebSocket como transporte
                upgrade: false, // Desativar fallback para polling
                reconnection: false, // Gerenciaremos a reconexão manualmente
                timeout: 10000 // Tempo limite de conexão (10 segundos)
            });

            // Variáveis de controle para mensagens
            const chamadoId = "{{ chamado.id }}";
            const userId = "{{ session.user_id }}";
            let isConnected = false;
            let sentMessages = new Map(); // Para rastrear mensagens enviadas aguardando confirmação

            // Função para verificar se o socket está conectado, e se não, reconectar
            function verificarEreconectar() {
                if (!socket.connected) {
                    console.log("Socket desconectado, reconectando...");
                    reconnectAttempt = 0;
                    tentarReconectar();
                    return false;
                }
                return true;
            }

            // Função para tentar reconectar com número limitado de tentativas
            function tentarReconectar() {
                if (reconnectAttempt >= MAX_RECONNECT_ATTEMPTS) {
                    console.error("Número máximo de tentativas atingido. Não foi possível reconectar.");
                    return;
                }

                reconnectAttempt++;
                console.log(`Tentativa de reconexão #${reconnectAttempt}`);

                socket.connect();

                // Se não conseguir conectar em 3 segundos, tenta novamente
                setTimeout(() => {
                    if (!socket.connected && reconnectAttempt < MAX_RECONNECT_ATTEMPTS) {
                        tentarReconectar();
                    }
                }, 3000);
            }

            // Evento de conexão bem-sucedida
            socket.on("connect", () => {
                console.log("Conectado ao servidor com sucesso. Socket ID:", socket.id);
                isConnected = true;
                reconnectAttempt = 0;
                startInactivityMonitor(); // Inicia o monitoramento de inatividade

                // Entrar na sala específica deste chamado
                socket.emit("join_room", { chamado_id: chamadoId });

                // Reenviar mensagens pendentes, se houver
                if (pendingMessages.length > 0) {
                    console.log(`Reenviando ${pendingMessages.length} mensagens pendentes`);
                    pendingMessages.forEach(msg => {
                        sendMessageToServer(msg.message, msg.messageId);
                    });
                    pendingMessages = [];
                }
            });

            // Inicia o monitoramento de inatividade
            function startInactivityMonitor() {
                if (inactivityInterval) {
                    clearInterval(inactivityInterval);
                }

                inactivityInterval = setInterval(() => {
                    if (userActive) {
                        console.log("Enviando ping para o servidor...");
                        socket.emit("ping");
                        userActive = false; // Reseta o estado de atividade
                    } else {
                        console.log("Nenhuma atividade detectada. Desconectando por inatividade...");
                        socket.emit("disconnect_due_to_inactivity");
                        handleInactivityDisconnect(); // Lida com a desconexão por inatividade
                    }
                }, INACTIVITY_LIMIT);
            }

            // Lidar com desconexão devido à inatividade
            function handleInactivityDisconnect() {
                clearInterval(inactivityInterval); // Para o monitoramento
                console.log("Desconectado por inatividade. Reconectará quando necessário.");
                socket.disconnect(); // Desconecta o cliente explicitamente
                isConnected = false;
                // Não mostra alerta, apenas desconecta silenciosamente
            }

            // Enviar a mensagem para o servidor
            function sendMessageToServer(messageContent, messageId) {
                if (!isConnected) {
                    console.log("Não conectado. Armazenando mensagem para envio posterior.");
                    // Verificar se a mensagem já está na fila para evitar duplicatas
                    const jaExiste = pendingMessages.some(msg => msg.messageId === messageId);
                    if (!jaExiste) {
                        pendingMessages.push({
                            message: messageContent,
                            messageId: messageId
                        });
                    }
                    return false;
                }

                // Rastrear mensagem enviada para confirmação
                sentMessages.set(messageId, {
                    content: messageContent,
                    timestamp: new Date().getTime()
                });

                // Emitir a mensagem via socket
                socket.emit('message', {
                    chamado_id: chamadoId,
                    user_id: userId,
                    mensagem: messageContent,
                    message_id: messageId
                });

                return true;
            }

            // Detecta envio ou recebimento de mensagens como atividade
            socket.on("message", (data) => {
                userActive = true;

                // Verificar se esta mensagem é para este chamado
                if (data.chamado_id == chamadoId) {
                    // Se for uma confirmação de mensagem enviada por nós
                    if (data.user_id == userId && sentMessages.has(data.message_id)) {
                        sentMessages.delete(data.message_id);
                    }

                    // Marcar como não pendente
                    data.pending = false;
                    renderMessage(data);
                }
            });

            // Confirmação de recebimento do servidor
            socket.on("message_received", (data) => {
                if (sentMessages.has(data.message_id)) {
                    console.log("Mensagem confirmada pelo servidor:", data.message_id);
                    sentMessages.delete(data.message_id);
                }
            });

            // Evento de desconexão
            socket.on("disconnect", (reason) => {
                console.log("Desconectado do servidor:", reason);
                isConnected = false;

                if (reason === "io server disconnect") {
                    console.warn("Desconectado manualmente pelo servidor.");
                } else if (reason === "transport close") {
                    console.warn("Conexão perdida, será reconectado quando necessário.");
                } else {
                    console.error("Desconexão inesperada:", reason);
                }

                clearInterval(inactivityInterval); // Limpa o intervalo
            });

            var userName = "{{ session.user_name }}";
            var profileImageUrl = "{{ session.profile_image_url or '/static/images/default-profile.png' }}";

            document.getElementById('chat-message').addEventListener('input', function () {
                const textarea = this;

                // Redefinir a altura para "auto" para recalcular corretamente
                textarea.style.height = 'auto';

                // Se o conteúdo exceder a altura máxima, permitir rolagem interna
                if (textarea.scrollHeight > 120) {
                    textarea.style.overflowY = 'scroll'; // Habilitar rolagem interna
                    textarea.style.height = '120px'; // Fixa a altura no máximo definido
                } else {
                    textarea.style.overflowY = 'hidden'; // Ocultar rolagem se o conteúdo for menor
                    textarea.style.height = textarea.scrollHeight + 'px'; // Ajusta a altura dinamicamente
                }
            });

            // Função para rolar automaticamente para a última mensagem do chat
            function rolarParaUltimaMensagem() {
                const chatMessages = document.querySelector('.direct-chat-messages');
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Função para mostrar o modal de formatação
            function showTextFormatModal(x, y) {
                const textareaRect = chatMessageInput.getBoundingClientRect();
                textFormatModal.style.left = `${textareaRect.left}px`;
                textFormatModal.style.top = `${textareaRect.bottom + window.scrollY + 5}px`;
                textFormatModal.style.display = 'flex';
            }

            // Função para ocultar o modal de formatação
            function hideTextFormatModal() {
                textFormatModal.style.display = 'none';
            }

            // Detecta a seleção de texto dentro do textarea e mostra o modal de formatação
            chatMessageInput.addEventListener('mouseup', function () {
                setTimeout(function () {
                    const selectionStart = chatMessageInput.selectionStart;
                    const selectionEnd = chatMessageInput.selectionEnd;
                    const selectedText = chatMessageInput.value.substring(selectionStart, selectionEnd).trim();

                    // Somente mostrar o modal se houver seleção
                    if (selectedText.length > 0) {
                        showTextFormatModal(); // Exibe o modal na posição corrigida
                    } else {
                        hideTextFormatModal();
                    }
                }, 0); // Timeout reduzido para garantir a detecção rápida da seleção
            });

            // Função para inserir tags de formatação ao texto selecionado
            function insertTextAtSelection(startTag, endTag) {
                const startPos = chatMessageInput.selectionStart;
                const endPos = chatMessageInput.selectionEnd;
                const selectedText = chatMessageInput.value.substring(startPos, endPos);

                const newText = startTag + selectedText + endTag;
                chatMessageInput.value = chatMessageInput.value.substring(0, startPos) + newText + chatMessageInput.value.substring(endPos);
                chatMessageInput.focus();
                chatMessageInput.selectionEnd = startPos + newText.length; // Mantém o cursor após a inserção da formatação

                hideTextFormatModal();  // Esconde o modal após a formatação ser aplicada
            }

            // Funções de formatação (negrito, itálico, tachado)
            document.getElementById('bold-btn').addEventListener('click', function () {
                insertTextAtSelection('*', '*');
                hideTextFormatModal();
            });

            document.getElementById('italic-btn').addEventListener('click', function () {
                insertTextAtSelection('_', '_');
                hideTextFormatModal();
            });

            document.getElementById('strike-btn').addEventListener('click', function () {
                insertTextAtSelection('~', '~');
                hideTextFormatModal();
            });

            // Oculta o modal se o usuário clicar fora
            document.addEventListener('mousedown', function (e) {
                if (!textFormatModal.contains(e.target) && e.target !== chatMessageInput) {
                    hideTextFormatModal();
                }
            });

            // Formatação do texto com negrito, itálico e tachado
            function formatText(text) {
                return text
                    .replace(/\*(.*?)\*/g, '<strong>$1</strong>')
                    .replace(/_(.*?)_/g, '<em>$1</em>')
                    .replace(/~(.*?)~/g, '<del>$1</del>');
            }

            // Função para enviar a mensagem
            function sendMessage() {
                var message = chatMessageInput.value.trim();

                if (message) {
                    // Verificar e tentar reconectar se necessário
                    if (!verificarEreconectar()) {
                        console.log("Tentando reconectar antes de enviar mensagem...");
                        // A mensagem será enviada após a reconexão bem-sucedida
                    }

                    // Gerar ID único para esta mensagem
                    const messageId = Date.now().toString();

                    message = message.replace(/\n/g, '<br>');
                    message = formatText(message);

                    var formattedMessage = marked.parse(message, {
                        renderer: new marked.Renderer()
                    });

                    formattedMessage = formattedMessage.replace(/<p>/g, '<p class="chat-paragraph">');

                    // Mostrar mensagem localmente primeiro (otimismo UI)
                    const localMessageData = {
                        chamado_id: chamadoId,
                        user_id: userId,
                        mensagem: formattedMessage,
                        usuario_nome: userName,
                        created_at: new Date().toLocaleString(),
                        profile_image_url: profileImageUrl,
                        mensagem_id: messageId,
                        visualizado: false,
                        pending: true
                    };

                    renderMessage(localMessageData);

                    // Tentar enviar a mensagem
                    if (sendMessageToServer(formattedMessage, messageId)) {
                        console.log("Mensagem enviada ao servidor:", messageId);
                    } else {
                        console.log("Mensagem armazenada para envio posterior:", messageId);
                    }

                    chatMessageInput.value = '';
                    chatMessageInput.style.height = 'auto';
                }
            }

            // Função para renderizar uma mensagem no chat
            function renderMessage(data) {
                console.log('Renderizando mensagem:', data);

                // Verificamos se a mensagem já existe no DOM (para evitar duplicatas)
                const existingMessage = document.querySelector(`.direct-chat-msg[data-mensagem-id="${data.mensagem_id}"]`);
                if (existingMessage) {
                    // Se a mensagem já existe e não está mais pendente, atualizar o status
                    if (data.pending === false && existingMessage.classList.contains('pending')) {
                        existingMessage.classList.remove('pending');
                        const checkIcon = existingMessage.querySelector('.bi-check-all');
                        if (checkIcon) {
                            checkIcon.classList.remove('pending');
                        }
                    }
                    return;
                }

                var chatMessages = document.querySelector('.direct-chat-messages');

                // Apenas o balão à direita (mensagens enviadas pelo próprio usuário) deve ter o ícone de check
                var visualizadoClass = data.visualizado ? 'visualizado' : 'nao-visualizado';
                var pendingClass = data.pending ? 'pending' : '';

                var newMessageHtml = `
                    <div class="direct-chat-msg ${data.user_id == userId ? 'right' : 'left'} ${pendingClass}" data-mensagem-id="${data.mensagem_id}">
                        <div class="direct-chat-infos clearfix">
                            <span class="direct-chat-name ${data.user_id == userId ? 'float-right' : 'float-left'}">${data.usuario_nome}</span>
                            <span class="direct-chat-timestamp ${data.user_id == userId ? 'float-left' : 'float-right'}">${typeof data.created_at === 'string' ? data.created_at : new Date(data.created_at).toLocaleString()}</span>
                        </div>
                        <img class="direct-chat-img" src="${data.profile_image_url || '/static/images/default-profile.png'}" alt="User Image">
                        <div class="direct-chat-text">
                            ${data.mensagem}
                            ${data.user_id == userId ? `<i class="bi bi-check-all ${visualizadoClass} ${pendingClass}" style="font-size: 1.2rem; position: absolute; bottom: 5px; right: 5px;"></i>` : ''}
                        </div>
                    </div>
                `;
                chatMessages.insertAdjacentHTML('beforeend', newMessageHtml);

                rolarParaUltimaMensagem();
            }

            // Verificar conexão quando o usuário interage com a página
            document.addEventListener('click', function () {
                userActive = true;
            });

            document.addEventListener('keydown', function () {
                userActive = true;
            });

            // Tentar reconectar quando a página fica visível novamente
            document.addEventListener('visibilitychange', function () {
                if (document.visibilityState === 'visible') {
                    userActive = true;
                    if (!isConnected) {
                        verificarEreconectar();
                    }
                }
            });

            // Substituir o manipulador de eventos existente para o botão de envio
            document.getElementById('send-chat-message').addEventListener('click', function () {
                userActive = true;
                sendMessage();
            });

            // Captura Enter para envio e Shift+Enter para nova linha
            chatMessageInput.addEventListener('keydown', function (event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    userActive = true;
                    sendMessage();
                }
            });

            // Função para marcar mensagens como visualizadas em tempo real
            function marcarComoVisualizado(chamadoId) {
                console.log("Enviando evento 'mark_as_read' para o servidor, chamado:", chamadoId);
                socket.emit('mark_as_read', { chamado_id: chamadoId });

                // Atualiza as mensagens localmente para mudar o ícone para verde
                const mensagens = document.querySelectorAll('.direct-chat-msg.right'); // Somente as mensagens à direita
                mensagens.forEach(mensagem => {
                    const checkIcon = mensagem.querySelector('.bi-check-all');
                    if (checkIcon) {
                        checkIcon.classList.add('visualizado');
                        checkIcon.classList.remove('nao-visualizado');
                        console.log("Mensagem visualizada localmente:", mensagem.dataset.mensagemId);
                    }
                });
            }

            // Detecta quando o chat é rolado até o fim e marca como visualizado
            document.querySelector('.direct-chat-messages').addEventListener('scroll', function () {
                const chatContainer = this;
                const isAtBottom = chatContainer.scrollHeight - chatContainer.scrollTop === chatContainer.clientHeight;

                console.log("Scroll detectado. Está no final do chat:", isAtBottom);  // Log para verificar se o scroll está no final

                if (isAtBottom) {
                    console.log("Marcando como visualizado o chamado:", chamadoId);
                    marcarComoVisualizado(chamadoId);  // Marca as mensagens como visualizadas
                }
            });

            // Quando o usuário abre o chat pela primeira vez
            window.addEventListener('load', function () {
                marcarComoVisualizado(chamadoId);  // Marca todas as mensagens como lidas quando o chat é aberto
                rolarParaUltimaMensagem(); // Rola para a última mensagem ao carregar
            });

            // Receber o evento de mensagens lidas em tempo real
            socket.on("mensagens_lidas", function (data) {
                const { chamado_id, user_id } = data;

                console.log(`Mensagens visualizadas para o chamado ${chamado_id} pelo usuário ${user_id}`);

                // Atualiza o ícone de visualização para mensagens à direita
                const mensagens = document.querySelectorAll('.direct-chat-msg.right'); // Somente mensagens enviadas pelo próprio usuário
                mensagens.forEach(mensagem => {
                    const checkIcon = mensagem.querySelector('.bi-check-all');
                    if (checkIcon) {
                        checkIcon.classList.add('visualizado');
                        checkIcon.classList.remove('nao-visualizado');
                        checkIcon.classList.remove('pending');
                        console.log("Mensagem atualizada como visualizada:", mensagem.dataset.mensagemId);
                    }
                });
            });

            // Adicionar novo campo de arquivo
            document.getElementById('add-file-btn').addEventListener('click', function () {
                const fileEntry = document.createElement('div');
                fileEntry.classList.add('file-entry');

                const fileTitleInput = document.createElement('input');
                const fileInput = document.createElement('div');
                const uploadButton = document.createElement('button');

                fileTitleInput.type = 'text';
                fileTitleInput.placeholder = 'Nome do Arquivo';
                fileTitleInput.classList.add('form-control', 'mb-2');

                fileInput.classList.add('custom-file');
                fileInput.innerHTML = `
                <input type="file" class="custom-file-input" id="customFile">
                <label class="custom-file-label" for="customFile">Adicione o arquivo</label>
            `;

                uploadButton.textContent = 'Enviar Arquivo';
                uploadButton.classList.add('btn', 'btn-success', 'btn-upload');

                fileEntry.appendChild(fileTitleInput);
                fileEntry.appendChild(fileInput);
                fileEntry.appendChild(uploadButton);

                document.getElementById('file-list').appendChild(fileEntry);

                fileInput.querySelector('input[type="file"]').addEventListener('change', function (e) {
                    const fileName = e.target.files[0].name;
                    fileInput.querySelector('.custom-file-label').textContent = fileName;
                });

                uploadButton.addEventListener('click', function () {
                    const file = fileInput.querySelector('input[type="file"]').files[0];
                    const fileTitle = fileTitleInput.value;
                    const ticketId = chamadoId;

                    if (!file || !fileTitle) {
                        alert('Preencha o nome do arquivo e selecione um arquivo.');
                        return;
                    }

                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('title', fileTitle);
                    formData.append('ticket_id', ticketId);

                    fetch('/novo_documento_ticket', {
                        method: 'POST',
                        body: formData
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.message) {
                                alert('Arquivo enviado com sucesso!');
                                window.location.reload();
                            } else if (data.error) {
                                alert('Erro: ' + data.error);
                            }
                        })
                        .catch(error => {
                            console.error('Erro ao enviar arquivo:', error);
                            alert('Erro ao enviar o arquivo. Tente novamente.');
                        });
                });
            });

            rolarParaUltimaMensagem();
        });
    </script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>

</html>