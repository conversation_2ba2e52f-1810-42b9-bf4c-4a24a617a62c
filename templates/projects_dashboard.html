{% extends "projects_base.html" %}

{% block title %}Painel de Projetos{% endblock %}

{% block header %}
<div class="row mb-2">
    <div class="col-sm-6">
        <h1 class="m-0">Painel de Projetos</h1>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Mensagem de boas-vindas no Dashboard -->
<div class="alert alert-info" role="alert">
    <h4 class="alert-heading">Bem-vindo ao Painel de Projetos!</h4>
    <p>Este é o conteúdo principal do painel.</p>
</div>

<!-- Cartão com Tabela de Projetos -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Lista de Projetos</h3>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Nome do Projeto</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    {% for proj in projetos %}
                    <tr>
                        <td>{{ proj[0] }}</td>
                        <td>{{ proj[1] }}</td>
                        <td>
                            <!-- Botões padronizados: Detalhes (btn-info), Editar (btn-warning) e Excluir (btn-danger) -->
                            <button type="button" class="btn btn-info btn-sm">Detalhes</button>
                            <button type="button" class="btn btn-warning btn-sm">Editar</button>
                            <button type="button" class="btn btn-danger btn-sm">Excluir</button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>                
            </table>
        </div>
    </div>
</div>
{% endblock %}
