{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}tabelaPorGrade{% endblock %}

{% block content %}
<div class="container-fluid tabelaPorGrade">
    <!-- Filtros -->
    <div class="row mb-3">
        <div class="col-md-4">
            <label for="gradeFilter" class="form-label">Grade</label>
            <select id="gradeFilter" class="form-select">
                <option value="">Selecione uma Grade</option>
                {% for grade in grades %}
                <option value="{{ grade }}">{{ grade }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-4">
            <label for="operadoraFilter" class="form-label">Operadora</label>
            <select id="operadoraFilter" class="form-select">
                <option value="">Selecione uma Operadora</option>
                {% for operadora in operadoras %}
                <option value="{{ operadora }}">{{ operadora }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-4">
            <label for="modalidadeFilter" class="form-label">Modalidade</label>
            <select id="modalidadeFilter" class="form-select">
                <option value="">Selecione uma Modalidade</option>
                {% for modalidade in modalidades %}
                <option value="{{ modalidade }}">{{ modalidade }}</option>
                {% endfor %}
            </select>
        </div>
    </div>

    <!-- Botão de buscar -->
    <div class="row">
        <div class="col-md-12 text-end">
            <button id="buscarBtn" class="btn btn-primary">Buscar</button>
        </div>
    </div>

    <div class="export-buttons">
        {% if session.get('user_type') in [1, 7] %}
        <button id="exportExcel" class="btn btn-success">Exportar para Excel</button>
        {% endif %}
        <button id="exportPdfBtn" class="btn btn-danger">Exportar para PDF</button>
    </div>      
    <!-- Tabela -->
    <div class="table-responsive">
        <table class="table table-striped permissoes-table">
            <thead>
                <tr>
                    <th class="permissoes-th">Operadora</th>
                    <th class="permissoes-th">Modalidade</th>
                    <th class="permissoes-th">Grade</th>
                    <th class="permissoes-th">Totais</th>
                    <th class="permissoes-th">1ª Parcela</th>
                    <th class="permissoes-th">2ª Parcela</th>
                    <th class="permissoes-th">3ª Parcela</th>
                    <th class="permissoes-th">4ª Parcela</th>
                    <th class="permissoes-th">5ª Parcela</th>
                    <th class="permissoes-th">6ª Parcela</th>
                    <th class="permissoes-th">7ª Parcela</th>
                    <th class="permissoes-th">8ª Parcela</th>
                    <th class="permissoes-th">9ª Parcela</th>
                    <th class="permissoes-th">10ª Parcela</th>
                    <th class="permissoes-th">11ª Parcela</th>
                    <th class="permissoes-th">12ª Parcela</th>
                </tr>
            </thead>
            <tbody id="tabelaResultados">
                <!-- Os resultados da busca serão injetados aqui -->
            </tbody>
        </table>
    </div>
    <div id="loadingScreen" class="loading-screen" style="display: none;">
        <div class="spinner"></div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">

<script src="{{ url_for('static', filename='js/tabela_porGrade.js') }}"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.0/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.3/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
{% endblock %}