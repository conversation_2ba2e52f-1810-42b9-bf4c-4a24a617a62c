{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}seletivo{% endblock %}

{% block extra_styles %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container">
    <div id="loadingScreen" class="loading-screen" style="display: none;">
        <div class="spinner"></div>
    </div>

    <div class="header-seletivo text-center my-4">
        <h1>Processos Seletivos Interno - Vagas Abertas</h1>
    </div>

    <div class="row">
        {% for seletivo in processos_seletivos %}
        <div class="col-md-4 mb-4">
            <div class="card h-100" id="seletivo-{{ seletivo.id }}">
                <div class="card-body">
                    <h3 class="card-title">{{ seletivo.titulo }}</h3>
                    <p class="card-text"><strong>Data limite de inscrição:</strong> {{ seletivo.data_fim }}</p>

                    {% if seletivo.encerrado %}
                    <!-- Ribbon de Encerrada com efeito de borda -->
                    <div class="ribbon ribbon-encerrada">
                        <span>Encerrada</span>
                    </div>
                    <button class="btn btn-secondary mt-3 w-100" disabled>Encerrado</button>
                    {% else %}
                    <!-- Ribbon de Vaga Aberta com efeito de borda -->
                    <div class="ribbon ribbon-aberta">
                        <span>Vaga Aberta</span>
                    </div>
                    <button class="btn btn-primary mt-3 w-100" data-id="{{ seletivo.id }}" onclick="openModal(this)">Ver Detalhes</button>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Modal para Detalhes do Processo Seletivo -->
    <div class="modal fade" id="modal" tabindex="-1" aria-labelledby="modalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalLabel">Detalhes do Processo Seletivo</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p><strong>Vaga:</strong> <span id="modal-titulo"></span></p>
                    <p><strong>Requisitos:</strong> <span id="modal-requisitos"></span></p>
                    <p><strong>Descrição da Vaga:</strong> <span id="modal-descricao"></span></p>
                    <p><strong>Data Inicial de inscrição:</strong> <span id="modal-data-inicio"></span></p>
                    <p><strong>Data Limite de inscrição:</strong> <span id="modal-data-final"></span></p>
                    <form id="seletivo-form" method="post">
                        <input type="hidden" name="id_seletivo" id="id_seletivo">
                        <div class="mb-3">
                            <label for="nome" class="form-label">Nome:</label>
                            <input type="text" class="form-control" id="nome" name="nome" required>
                        </div>
                        <div class="mb-3">
                            <label for="dp_atual" class="form-label">Departamento Atual:</label>
                            <input type="text" class="form-control" id="dp_atual" name="dp_atual" required>
                        </div>
                        <div class="mb-3">
                            <label for="cargo_atual" class="form-label">Cargo Atual:</label>
                            <input type="text" class="form-control" id="cargo_atual" name="cargo_atual" required>
                        </div>
                        <div class="mb-3">
                            <label for="cnh" class="form-label">Possui CNH?:</label>
                            <select class="form-control" id="cnh" name="cnh" required>
                                <option value="" disabled selected>Selecione</option>
                                <option value="true">Sim</option>
                                <option value="false">Não</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="carro" class="form-label">Possui carro próprio?:</label>
                            <select class="form-control" id="carro" name="carro" required>
                                <option value="" disabled selected>Selecione</option>
                                <option value="true">Sim</option>
                                <option value="false">Não</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="formacao" class="form-label">Formação:</label>
                            <select class="form-control" id="formacao" name="formacao" required>
                                <option value="" disabled selected>Selecione sua formação</option>
                                <option value="ensino_medio_incompleto">Ensino médio incompleto</option>
                                <option value="ensino_medio_completo">Ensino médio completo</option>
                                <option value="ensino_tecnico_incompleto">Ensino técnico incompleto</option>
                                <option value="ensino_tecnico_completo">Ensino técnico completo</option>
                                <option value="ensino_superior_incompleto">Ensino superior incompleto</option>
                                <option value="ensino_superior_completo">Ensino superior completo</option>
                                <option value="pos_graduacao">Pós-Graduação</option>
                                <option value="mestrado">Mestrado</option>
                                <option value="doutorado">Doutorado</option>
                            </select>
                            <label for="formacao-detal" class="form-label">Qual curso?:</label>
                            <input type="text" class="form-control" id="formacao-detal" name="formacao-detal" required>
                        </div>
                        <div class="mb-3">
                            <label for="certificacao" class="form-label">Possui certificações, cursos extra curriculares ou similares. Se sim, quais?:</label>
                            <input type="text" class="form-control" id="certificacao" name="certificacao" required>
                        </div>
                        <div class="mb-3">
                            <label for="motivo" class="form-label">Conte-nos o que motiva você a se candidatar a esta vaga:</label>
                            <textarea class="form-control" id="motivo" name="motivo" rows="4" maxlength="5000" required></textarea>
                        </div>
                        <button type="button" onclick="submitForm()" class="btn btn-primary w-100">Candidatar-se Vaga</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js"></script>
<script src="{{ url_for('static', filename='js/processo_seletivo.js') }}"></script>
{% endblock %}
