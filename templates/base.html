<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <title>{% block title %}Intranet | BrazilHealth{% endblock %}</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

    <!-- Adicionando o favicon -->
    <link rel="icon" href="{{ url_for('static', filename='images/brh-logo.png') }}" type="image/png">
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/brh-logo.png') }}" type="image/png">

    {% block head_extra %}{% endblock %}
    {% block extra_styles %}{% endblock %}

    <script type="text/javascript">
        (function (c, l, a, r, i, t, y) {
            c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };
            t = l.createElement(r); t.async = 1; t.src = "https://www.clarity.ms/tag/" + i;
            y = l.getElementsByTagName(r)[0]; y.parentNode.insertBefore(t, y);
        })(window, document, "clarity", "script", "qnpnt6esuz");
    </script>
</head>

<body class="{% block body_class %}{% endblock %}">
    {% block header %}
    <header class="pn-header">
        <div class="pn-header-top">
            <div class="pn-container">
                <div class="header_left">
                    <figure class="pn-header-logo">
                        <div id="init-page" style="display: inline-block; left: -10%;"></div>
                        <a href="{{ url_for('index') }}">
                            <img src="https://brazilhealth.com.br/wp-content/uploads/2020/03/07_Prancheta-1-c%C3%B3pia-6-e1583518361332-1-2048x672.png"
                                alt="Logo" class="home-header-image">
                        </a>
                    </figure>
                    <div class="home-menu-dropdown" onclick="toggleDropdown_m()">
                        <div class="menu-icon">
                            <i class="bi bi-list"></i>
                        </div>
                        <div class="header-dropdown-content" id="menuDropdown">

                            <a class="dropdown-link" href="#Brazil" onclick="toggleSubmenu(event, 'submenu-brazil')">
                                Brazil <i class="bi bi-chevron-right"></i>
                            </a>
                            <div class="submenu" id="submenu-brazil">
                                <a href="/organizacao-brazil">
                                    <i class="bi bi-building"></i> Organização
                                </a>
                                {% if session.get('user_type') in [7] %}
                                <a href="javascript:void(0);" onclick="redirectToBrazilAndOpenOrganograma()">
                                    <i class="fas fa-users"></i> Equipes (Organograma)
                                </a>
                                {% endif %}
                                <a href="/unidades">
                                    <i class="fas fa-globe"></i> Unidades
                                </a>
                                <a href="/calendar">
                                    <i class="fas fa-door-open"></i> Sala de Reunião (Agendar)
                                </a>
                            </div>

                            <a class="dropdown-link" href="#RH" onclick="toggleSubmenu(event, 'submenu-rh')">
                                RH <i class="bi bi-chevron-right"></i>
                            </a>
                            <div class="submenu" id="submenu-rh">
                                <a href="/rh"><i class="fas fa-users"></i> Institucional</a>
                                <a href="/manual"><i class="fas fa-book"></i> Manual do Colaborador</a>
                                <a href="/faq"><i class="bi bi-question-circle-fill"></i> FAQ</a>
                                <a href="/enviar_sugestao"><i class="fa-solid fa-envelope-open-text"></i> Sugestões</a>
                                <a href="/rh/processos-seletivos-internos"><i class="fa-brands fa-wpforms"></i>
                                    Processos Seletivos</a>
                            </div>

                            {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] %}
                            <a class="dropdown-link" href="#Gestao" onclick="toggleSubmenu(event, 'submenu-gestao')">
                                Gestão <i class="bi bi-chevron-right"></i>
                            </a>
                            <div class="submenu" id="submenu-gestao">
                                {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] %}
                                <a href="/gestao-de-reembolso"><i class="fas fa-money-check-alt"></i> Gestão de
                                    Reembolso</a>
                                {% endif %}

                                {% if session.get('user_type') in [1, 3, 4, 7] %}
                                <a href="/gestao-fastmoney"><i class="fas fa-coins"></i> Gestão de Fast-Money</a>
                                {% endif %}

                                {% if session.get('user_type') in [1, 7] %}
                                <a href="/tabela-de-comissoes"><i class="fas fa-table"></i> Tabelas de Comissão</a>
                                {% endif %}

                                {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] and session.get('setor_id') not in
                                [9, 10]%}
                                <a href="/acessos"><i class="fa-solid fa-key"></i> Login e Senha</a>
                                {% endif %}

                                {% if session.get('user_type') in [1, 5, 7] or session.get('user_id') in [110, 149]%}
                                <a href="/gestao/chamados"><i class="fa-solid fa-person-circle-exclamation"></i> Gestão
                                    de Chamados</a>
                                {% endif %}
                            </div>
                            {% endif %}

                            {% if session.get('user_id') in [67] or session.get('user_type') in [1, 6, 7] %}
                            <a class="dropdown-link" href="#Franqueado"
                                onclick="toggleSubmenu(event, 'submenu-franqueado')">
                                Franqueado <i class="bi bi-chevron-right"></i>
                            </a>
                            <div class="submenu" id="submenu-franqueado">
                                <a href="javascript:void(0);" onclick="redirectToBrazilAndShowDocumentos()"><i
                                        class="fas fa-file-alt"></i> Documentos</a>
                                <a href="#"><i class="fas fa-chart-line"></i> Desempenho</a>
                                <a href="#"><i class="fas fa-headset"></i> Help Desk</a>
                                <a href="#"><i class="fas fa-calendar-alt"></i> Eventos</a>
                                <a href="#"><i class="fas fa-tasks"></i> Processos</a>
                                <a href="#"><i class="fas fa-cogs"></i> Sistemas</a>
                            </div>
                            {% endif %}

                            {% if session.get('user_type') in [1, 2, 3, 4, 7] %}
                            <a class="dropdown-link" href="#Gerenciais"
                                onclick="toggleSubmenu(event, 'submenu-gerenciais')">
                                Gerênciais <i class="bi bi-chevron-right"></i>
                            </a>
                            {% endif %}

                            <div class="submenu" id="submenu-gerenciais">
                                {% if session.get('user_type') in [1, 7] %}
                                <a class="link" href="/ranking">
                                    <i class="material-symbols-outlined">leaderboard</i> Ranking por Assistente
                                </a>
                                <a href="/ranking-empresarial">
                                    <i class="material-symbols-outlined">leaderboard</i> Ranking Empresarial
                                </a>
                                {% elif session.get('user_type') == 4 %}
                                <a class="link" href="/ranking-individual">
                                    <i class="material-symbols-outlined">leaderboard</i> Ranking por Assistente
                                </a>
                                <a href="/ranking-individual-empresarial">
                                    <i class="material-symbols-outlined">leaderboard</i> Ranking Empresarial
                                </a>
                                {% endif %}

                                {% if session.get('user_type') in [1, 7] %}
                                <a href="/ranking-operadora">
                                    <i class="material-symbols-outlined">leaderboard</i> Ranking por Operadora
                                </a>
                                {% endif %}

                                {% if session.get('user_type') in [1, 2, 7] %}
                                <a href="/ranking-unidades">
                                    <i class="material-symbols-outlined">leaderboard</i> Ranking Unidades
                                </a>
                                {% endif %}

                                {% if session.get('user_type') in [1, 7] %}
                                <a href="/ranking-comparativo-mes">
                                    <i class="material-symbols-outlined">stacked_line_chart</i> Ranking Comparativo -
                                    Mensal
                                </a>
                                <a href="/ranking-comparativo-anual">
                                    <i class="material-symbols-outlined">stacked_line_chart</i> Ranking Comparativo -
                                    Ano Anterior
                                </a>
                                {% elif session.get('user_type') == 4 %}
                                <a href="/ranking-comp-mes-individual">
                                    <i class="material-symbols-outlined">stacked_line_chart</i> Ranking Comparativo -
                                    Mensal
                                </a>
                                <a href="/ranking-comp-anual-individual">
                                    <i class="material-symbols-outlined">stacked_line_chart</i> Ranking Comparativo -
                                    Ano Anterior
                                </a>
                                {% endif %}

                                {% if session.get('user_type') in [1, 7] %}
                                <a href="/ranking-op-x-ass">
                                    <i class="material-symbols-outlined">leaderboard</i> Ranking Operadora x Assistente
                                </a>
                                <a href="/ranking-op-x-cor">
                                    <i class="material-symbols-outlined">leaderboard</i> Ranking Operadora x Corretor
                                </a>
                                <a href="/ases">
                                    <i class="fa-solid fa-jet-fighter"></i> Ranking ASES de Vendas
                                </a>
                                {% endif %}

                                {% if session.get('user_type') in [1, 7] %}
                                <a href="/metas-vidas">
                                    <i class="bi bi-speedometer2"></i> Metas por Vidas
                                </a>
                                {% elif session.get('user_type') == 4 %}
                                <a href="/metas-individuais">
                                    <i class="bi bi-speedometer2"></i> Metas
                                </a>
                                {% elif session.get('user_type') in [2, 3] %}
                                <a href="/metas-superintendente">
                                    <i class="bi bi-speedometer2"></i> Metas
                                </a>
                                {% endif %}
                            </div>

                        </div>
                    </div>
                    <div id="overlay" class="overlay" onclick="closeMenu()"></div>
                </div>

                <ul class="pn-top-menu">
                    <li class="item {{ 'item-selected' if active_page == 'home' else '' }} dropdown">
                        <div class="pn-menu-ruler"></div>
                        <a id="home" class="link" href="{{ url_for('index') }}">
                            Home
                        </a>
                    </li>

                    <li class="item {{ 'item-selected' if active_page == 'rh' else '' }} dropdown">
                        <div class="pn-menu-ruler"></div>
                        <a id="rh" class="link" href="/rh">
                            RH <i></i>
                        </a>
                        <div class="submenu-dropdown" id="submenu-rh">
                            <a class="submenu-letter" href="/rh">Institucional</a>
                            <a class="submenu-letter" href="/manual">Manual do Colaborador</a>
                            <a class="submenu-letter" href="/faq">FAQ</a>
                            <a class="submenu-letter" href="/enviar_sugestao">Sugestões</a>
                            <a class="submenu-letter" href="/rh/processos-seletivos-internos">Processos Seletivos</a>
                        </div>
                    </li>

                    {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] %}
                    <li class="item {{ 'item-selected' if active_page == 'gestao' else '' }} dropdown">
                        <div class="pn-menu-ruler"></div>
                        <a id="gestao" class="link" href="/gestao">
                            Gestão <i></i>
                        </a>
                        <div class="submenu-dropdown" id="submenu-gestao">
                            {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] and session.get('setor_id') not in [9,
                            10] %}
                            <a class="submenu-letter" href="/gestao-de-reembolso">Gestão de Reembolso</a>
                            {% endif %}

                            {% if session.get('user_type') in [1, 3, 4, 7] %}
                            <a class="submenu-letter" href="/gestao-fastmoney">Gestão de Fast-Money</a>
                            {% endif %}

                            {% if session.get('user_type') in [1, 7] %}
                            <a class="submenu-letter" href="/tabela-de-comissoes">Tabelas de Comissão</a>
                            {% endif %}

                            {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] and session.get('setor_id') not in [9,
                            10] %}
                            <a class="submenu-letter" href="/acessos">Login e Senha</a>
                            {% endif %}

                            {% if session.get('user_type') in [1, 5, 7] or session.get('user_id') in [110, 149] and
                            session.get('setor_id') != 9 %}
                            <a class="submenu-letter" href="/gestao/chamados">Gestão de Chamados</a>
                            {% endif %}
                        </div>
                    </li>
                    {% endif %}

                    {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] %}
                    <li class="item {{ 'item-selected' if active_page == 'colaborador' else '' }} dropdown">
                        <div class="pn-menu-ruler"></div>
                        <a id="colaborador" class="link" href="/colaborador">
                            Colaborador
                        </a>
                    </li>
                    {% endif %}

                    {% if session.get('user_id') in [67] or session.get('user_type') in [1, 6, 7] %}
                    <li class="item {{ 'item-selected' if active_page == 'franqueado' else '' }} dropdown">
                        <div class="pn-menu-ruler"></div>
                        <a id="franqueado" class="link" href="/franqueado">
                            Franqueado
                        </a>
                        <div class="submenu-dropdown" id="submenu-franqueado">
                            <a class="submenu-letter" href="/franqueado">Documentos</a>
                            <a class="submenu-letter" href="/franqueado">Desempenho</a>
                            <a class="submenu-letter" href="/franqueado">Help Desk</a>
                            <a class="submenu-letter" href="/franqueado">Eventos</a>
                            <a class="submenu-letter" href="/franqueado">Processos</a>
                            <a class="submenu-letter" href="/franqueado">Sistemas</a>
                        </div>
                    </li>
                    {% endif %}

                    {% if session.get('user_type') in [1, 2, 3, 4, 7] %}
                    <li class="item {{ 'item-selected' if active_page == 'relatorio-gerenciais' else '' }} dropdown">
                        <div class="pn-menu-ruler"></div>
                        <a id="relatorio-gerenciais" class="link" href="/gerenciais">
                            Gerênciais
                        </a>
                        <div class="submenu-dropdown" id="submenu-gerenciais">
                            {% if session.get('user_type') in [1, 7] %}
                            <a class="submenu-letter" class="link" href="/ranking">
                                Ranking por Assistente
                            </a>
                            {% elif session.get('user_type') == 3 %}
                            <a class="submenu-letter" class="link" href="/breve">
                                Ranking por Assistente
                            </a>
                            {% elif session.get('user_type') == 4 %}
                            <a class="submenu-letter" class="link" href="/ranking-individual">
                                Ranking por Assistente
                            </a>
                            {% endif %}

                            {% if session.get('user_type') in [1, 7] %}
                            <a class="submenu-letter" href="/ranking-empresarial">
                                Ranking Empresarial
                            </a>
                            {% elif session.get('user_type') == 3 %}
                            <a class="submenu-letter" href="/breve">
                                Ranking Empresarial
                            </a>
                            {% elif session.get('user_type') == 4 %}
                            <a class="submenu-letter" href="/ranking-individual-empresarial">
                                Ranking Empresarial
                            </a>
                            {% endif %}

                            {% if session.get('user_type') in [1, 7] %}
                            <a class="submenu-letter" href="/ranking-operadora">
                                Ranking por Operadora
                            </a>
                            {% endif %}

                            {% if session.get('user_type') in [1, 2, 7] %}
                            <a class="submenu-letter" href="/ranking-unidades">
                                Ranking Unidades
                            </a>
                            {% endif %}

                            {% if session.get('user_type') in [1, 7] %}
                            <a class="submenu-letter" href="/ranking-comparativo-mes">
                                Ranking Comparativo - Mensal
                            </a>
                            <a class="submenu-letter" href="/ranking-comparativo-anual">
                                Ranking Comparativo - Ano Anterior
                            </a>
                            {% elif session.get('user_type') == 4 %}
                            <a class="submenu-letter" href="/ranking-comp-mes-individual">
                                Ranking Comparativo - Mensal
                            </a>
                            <a class="submenu-letter" href="/ranking-comp-anual-individual">
                                Ranking Comparativo - Ano Anterior
                            </a>
                            {% endif %}

                            {% if session.get('user_type') in [1, 7] %}
                            <a class="submenu-letter" href="/ranking-op-x-ass">
                                Ranking Operadora x Assistente
                            </a>
                            <a class="submenu-letter" href="/ranking-op-x-cor">
                                Ranking Operadora x Corretor
                            </a>
                            <a class="submenu-letter" href="/ases">
                                Ranking ASES de Vendas
                            </a>
                            {% endif %}

                            {% if session.get('user_type') in [1, 7] %}
                            <a class="submenu-letter" href="/metas-vidas">
                                Metas por Vidas
                            </a>
                            {% elif session.get('user_type') in [2, 3] %}
                            <a class="submenu-letter" href="/metas-superintendente">
                                Metas
                            </a>
                            {% elif session.get('user_type') == 4 %}
                            <a class="submenu-letter" href="/metas-individuais">
                                Metas
                            </a>
                            {% endif %}
                        </div>
                    </li>
                    {% endif %}
                </ul>

                <div class="header_right">
                    <div class="home-user-dropdown">
                        <div class="home-user-icon-container" onclick="toggleDropdown_user()">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="home-user-dropdown-content" id="userDropdown">
                            <a href="{{ url_for('usuarios') }}">Meu Cadastro</a>
                            <a href="{{ url_for('change_password') }}"
                                class="{{ 'item-selected' if active_page == 'change_password' else '' }}">Alterar
                                Senha</a>
                            {% if session.get('user_type') in [1, 7] %}
                            <a href="{{ url_for('admin') }}">Alteração Master</a>
                            {% endif %}
                            {% if session.get('user_type') in [1, 7] %}
                            <a href="{{ url_for('permissoes') }}">Permissões</a>
                            <a href="{{ url_for('parametros') }}">Parâmetros</a>
                            {% endif %}
                            <a href="{{ url_for('logout') }}">Sair</a>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </header>
    {% endblock %}
    <main class="content-container">
        {% block content %}
        <!-- Conteúdo específico da página -->
        {% endblock %}
    </main>

    {% block footer %}
    <footer class="footer-container">
        <div id="footer-box">
            <div id="footer-images">
                <div class="footer-logo-container">
                    <div id="footer-logo">
                        <a>
                            <img src="{{ url_for('static', filename='images/logobrazil10-1.png') }}" alt="Logo-10"
                                class="footer-logo-image">
                        </a>
                    </div>
                    <div id="footer-social">
                        <div id="facebook">
                            <a class="bi bi-facebook" href="https://www.facebook.com/brazilhealth/" target="_blank"
                                id="social-footer"></a>
                        </div>
                        <div id="instagram">
                            <a class="bi bi-instagram" href="https://www.instagram.com/brazil.health.oficial/"
                                target="_blank" id="social-footer"></a>
                        </div>
                        <div id="youtube">
                            <a class="bi bi-youtube" href="https://www.youtube.com/channel/UCVbEEkgVhZrVZiW_D3VAvUg"
                                target="_blank" id="social-footer"></a>
                        </div>
                    </div>
                </div>
                <div class="footer-gptw-container">
                    <a href="https://connect.gptw.info/certified-company?s=a94f9ea6-a4d8-4797-97f0-256282185249"
                        target="_blank">
                        <img src="{{ url_for('static', filename='images/gptw.png') }}" alt="GPTW"
                            class="footer-gptw-image">
                    </a>
                </div>
            </div>
            <div class="footer-separator"></div>
            <p class="copyright">© 2024 BrazilHealth. Todos os direitos reservados.</p>
        </div>
    </footer>
    {% endblock %}
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script>
        function redirectToFranqueadoAndOpen() {
            sessionStorage.setItem('openOrganograma', 'true');
            window.location.href = '/organizacao-brazil';
        }

        function redirectToCadastroAndOpenEstudosForm() {
            window.location.href = '/cadastro-corretor';
            sessionStorage.setItem('openEstudosForm', 'true');
        }
    </script>
    {% block scripts %}{% endblock %}
</body>

</html>