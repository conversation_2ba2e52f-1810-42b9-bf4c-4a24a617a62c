<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <title>Intranet | BrazilHealth</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&amp;display=swap" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Adicionando o favicon -->
    <link rel="icon" href="/static/images/brh-logo.png" type="image/png">
    <link rel="shortcut icon" href="/static/images/brh-logo.png" type="image/png">
</head>

<body class="reembolso-colaborador">
    <div class="container">
        <!-- Mensagens de sucesso ou erro -->
        {% if success %}
        <div class="alert alert-success">
            {{ success }}
        </div>
        {% endif %}

        {% if error %}
        <div class="alert alert-danger">
            {{ error }}
        </div>
        {% endif %}

        <div class="sugestoes">
            <div id="sugestao-float-box">
                <h2>Solicitação de Reembolso</h2>
                <form id="form-reembolso" action="{{ url_for('reembolso_colaborador') }}" method="POST"
                    enctype="multipart/form-data">

                    <div class="form-group-basic">
                        <div class="form-group">
                            <label for="nome">Nome Completo</label>
                            <input type="text" id="nome" name="nome" value="{{ usuario.nome }}" required>
                        </div>
                        <div class="form-group">
                            <label for="departamento">Departamento</label>
                            <input type="text" id="departamento" name="departamento" value="{{ usuario.departamento }}"
                                required>
                        </div>
                    </div>

                    <div class="form-group-basic">
                        <div class="form-group">
                            <label for="email">E-mail</label>
                            <input type="email" id="email" name="email" value="{{ usuario.email }}" required>
                        </div>
                        <div class="form-group">
                            <label for="telefone">Telefone</label>
                            <input type="tel" id="telefone" name="telefone" value="{{ usuario.telefone }}" required>
                        </div>
                    </div>

                    <div class="form-group-basic">
                        <div class="form-group">
                            <label for="data-despesa">Data da Despesa</label>
                            <input type="date" id="data-despesa" name="data-despesa" required>
                        </div>
                        <div class="form-group">
                            <label for="valor">Valor da Despesa (R$)</label>
                            <input type="number" id="valor" name="valor" step="0.01" required>
                        </div>
                    </div>

                    <div class="form-group-basic">
                        <div class="form-group">
                            <label for="tipo-despesa">Tipo de Despesa</label>
                            <select id="tipo-despesa" name="tipo-despesa" required>
                                <option value="">Selecione o tipo de despesa</option>
                                <option value="Transporte">Transporte</option>
                                <option value="Alimentação">Alimentação</option>
                                <option value="Hospedagem">Hospedagem</option>
                                <option value="Outros">Outros</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="comprovante">Anexar Comprovante</label>
                            <input type="file" id="comprovante" name="comprovante" accept=".pdf, .jpg, .png" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="descricao">Descrição</label>
                        <textarea id="descricao" name="descricao" rows="4" required></textarea>
                    </div>

                    <button type="submit" class="btn-primary">Enviar Solicitação</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/script.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('form-reembolso');
            form.addEventListener('submit', function (event) {
                event.preventDefault(); // Previne o envio padrão do formulário
                const formData = new FormData(form);

                fetch(form.action, {
                    method: 'POST',
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Erro na requisição: ' + response.statusText);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.Success) {
                            alert(data.message); // Exibe o alerta com a mensagem de sucesso
                            window.location.href = "/reembolso-colaborador"; // Redireciona para a página desejada
                        } else {
                            alert("Erro: " + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Erro:', error);
                        alert("Ocorreu um erro ao processar sua solicitação.");
                    });
            });
        });
    </script>
</body>
</html>