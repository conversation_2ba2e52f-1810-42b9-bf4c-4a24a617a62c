{% extends "base.html" %}

{% block title %}Acessos - Intranet | BrazilHealth{% endblock %}

{% block body_class %}permissoes{% endblock %}

{% block content %}
<div id="sideBar-permissoes">
    <ul class="nav flex-column">
        <li class="nav-item">
            <a class="nav-link d-flex align-items-center" data-bs-toggle="collapse" href="#gestaoMenu" role="button"
                aria-expanded="false" aria-controls="gestaoMenu">
                <i class="bi bi-caret-right-fill me-2 rotate-icon"></i> Gestão
            </a>
            <div class="collapse" id="gestaoMenu">
                <ul class="list-unstyled ms-3">
                    <li><a href="#" data-id="3" class="load-info">Fast Money</a></li>
                    <li><a href="#" data-id="5" class="load-info">Gestão de Boletos</a></li>
                    <li><a href="#" data-id="1" class="load-info">Login e Senha</a></li>
                    <li><a href="#" data-id="2" class="load-info">Reembolso</a></li>
                    <li><a href="#" data-id="4" class="load-info">Tabela de Comissão</a></li>
                </ul>
            </div>
        </li>
    </ul>
</div>

<div id="painel-permissoes">

</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/permissoes.js') }}"></script>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}