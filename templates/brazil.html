{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}brazil{% endblock %}
{% block content %}
<div id="brazil-float-box">
    <div id="brazil-main-content">
        <div class="bloco-conteiner-um">
            <div id="sobre-container">
                <h2>QUEM SOMOS</h2>
                <div class="text-container">

                    Brazil Health é uma empresa de especialistas em corretora, consultoria e
                    assessoria no mercado de
                    benefícios e seguros em planos de saúde, odonto e vida. Nascendo da visão e empreendedorismo de
                    seus
                    sócios, executivos corporativos com vasta experiência em mais de 25 anos em grandes operadoras e
                    corretoras do setor, podendo desenvolver estratégias focadas em ampliar oportunidades de
                    melhoria na
                    área de benefícios para pessoas, empresas, RH’s e corretores de seguros. Desde 2014 a Brazil
                    Health
                    sempre buscou inovar no mercado de benefícios, tornando-se uma das referências no segmento e
                    sendo
                    amplamente reconhecida com serviços de credibilidade e qualidade, conquistando reconhecimentos
                    anuais
                    pela sua atuação em bom atendimento, performance e inovações, pautados sempre na sua missão,
                    visão e
                    sólidos valores.
                    <br><br>
                    Atuando com mais de 123 colaboradores dispostos na sua matriz em São Paulo
                    e 8 unidades estratégicas, além de 53 franqueados e mais de 2000 corretores parceiros em todo
                    país,
                    o grupo BRH se destaca com uma das maiores consultorias e corretora de benefícios, seguros
                    Saúde,
                    Odonto e Vida no país, operando com boas práticas e respeito a cada segurado, corretor e
                    colaborador, como também associada junto a renomadas entidades como Sincor, ABF – Associação
                    Brasileira de Franchising, Sebrae e outras mais voltadas para apoiar o empreendedorismo,
                    atendimento
                    e inovação, incluindo serviços de rohrreinigung wien para atender às necessidades de
                    nossos clientes
                </div>
            </div>
            <div id="missao-container">
                <h2>MISSÃO</h2>
                <div class="text-container">
                    Prestar soluções inteligentes em benefícios saúde, odonto e vida. Gerar
                    serviços otimizados a cada cliente e corretor, buscando suprir as necessidades com eficácia,
                    qualidade, gestão e prevenção. Além disso, oferecemos serviços de Rohrreinigung Frankfurt para
                    garantir a satisfação total de nossos clientes. Foco em atendimento, pós-vendas e
                    relacionamento, buscando conquistar satisfação e mercado com nossos serviços.
                </div>
            </div>
        </div>
        <div class="bloco-conteiner-dois">
            <div id="visao-container">
                <h2>VISÃO</h2>
                <div class="text-container">
                    Ser reconhecida como a melhor consultoria e corretora no segmento saúde,
                    odonto e vida no país para nossos parceiros e clientes, através de serviços diferenciados,
                    inteligentes e competentes. Buscando superar todas as expectativas.
                </div>
            </div>
            <div id="valores-container">
                <h2>VALORES</h2>
                <div class="text-container">
                    Inovação | Ética | Comprometimento | Relacionamento | Respeito | Transparência.
                </div>
            </div>
        </div>
    </div>
    <div class="session-container">
        <div class="estrutura-container">
            <h2>Nossa Estrutura</h2>
        </div>
        <div class="bloco-container">
            <div class="number-container">
                <span class="numbers">
                    185 mil
                </span>
                <div class="rotulos">
                    VIDAS ATENDIDAS
                </div>
            </div>
            <div class="number-container">
                <span class="numbers">
                    123
                </span>
                <div class="rotulos">
                    COLABORADORES
                </div>
            </div>
            <div class="number-container">
                <span class="numbers">
                    2.000
                </span>
                <div class="rotulos">
                    CORRETORES PARCEIROS
                </div>
            </div>
            <div class="number-container">
                <span class="numbers">
                    8
                </span>
                <div class="rotulos">
                    UNIDADES SP & INTERIOR
                </div>
            </div>
            <div class="number-container">
                <span class="numbers">
                    53
                </span>
                <div class="rotulos">
                    FRANQUEADOS
                </div>
            </div>
        </div>
    </div>
    <div id="carrosselOrganograma" class="carrossel-organograma">
        <div class="carrossel-slides">
        </div>
        <a class="prev" onclick="mudarSlide(-1)">❮</a>
        <a class="next" onclick="mudarSlide(1)">❯</a>
        <div class="indicadores-slide">
        </div>
    </div>
    <div id="mensagemModal" class="mensagem-modal" style="display: none;">
        <div class="mensagem-modal-content">
            <span class="mensagem-close" onclick="closeMensagemModal()">&times;</span>
            <p id="mensagemTexto"></p>
        </div>
    </div>
</div>
{% block extra_scripts %}
<script>
    if (sessionStorage.getItem('openOrganograma') === 'true') {
      setTimeout(function() {
        openOrganograma();
        sessionStorage.removeItem('openOrganograma');
      }, 300); // Ajuste o tempo conforme necessário
    }
    </script>
{% endblock %}
{% endblock %}