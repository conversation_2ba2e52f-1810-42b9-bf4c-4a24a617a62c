{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}
{% block body_class %}area-do-franqueado{% endblock %}
{% block extra_styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/franqueado.css') }}">
{% endblock %}

{% block content %}
<div class="container">
    <h2>Abertura de Código na Operadora</h2>
    <form action="/submit_form" method="post" enctype="multipart/form-data">
        <div class="form-group">
            <label for="operadora">Selecione a Operadora:</label>
            <select id="operadora" name="operadora" class="form-control" required>
                <option value="Amil">Amil</option>
                <option value="Hapvida (GNDI)">Hapvida (GNDI)</option>
                <option value="CNU">CNU</option>
                <option value="Omint">Omint</option>
                <option value="Kipp">Kipp</option>
                <option value="Corpore">Corpore</option>
                <option value="SulAmerica">SulAmerica</option>
                <option value="Seguros Unimed">Seguros Unimed</option>
                <option value="Golden Cross">Golden Cross</option>
            </select>
        </div>
        <div class="form-group">
            <label for="contrato_social">Contrato Social e/ou última alteração contratual consolidada:</label>
            <input type="file" id="contrato_social" name="contrato_social" class="form-control-file" required>
        </div>
        <div class="form-group">
            <label for="nota_fiscal">Nota Fiscal:</label>
            <input type="file" id="nota_fiscal" name="nota_fiscal" class="form-control-file" required>
        </div>
        <div class="form-group">
            <label for="cartao_cnpj">Cartão de CNPJ:</label>
            <input type="file" id="cartao_cnpj" name="cartao_cnpj" class="form-control-file" required>
        </div>
        <div class="form-group">
            <label for="folha_cheque">Folha de Cheque/Cópia Cartão Banco/Proposta Abertura de Conta:</label>
            <input type="file" id="folha_cheque" name="folha_cheque" class="form-control-file" required>
        </div>
        <div class="form-group">
            <label for="susep_pj">SUSEP Pessoa Jurídica (CASO TENHA):</label>
            <input type="file" id="susep_pj" name="susep_pj" class="form-control-file">
        </div>
        <div class="form-group">
            <label for="comprovante_endereco">Comprovante de endereço em nome da Empresa/Corretora:</label>
            <input type="file" id="comprovante_endereco" name="comprovante_endereco" class="form-control-file" required>
        </div>
        <div class="form-group">
            <label for="cpf_rg_socios">CPF e RG do(s) sócio(s):</label>
            <input type="file" id="cpf_rg_socios" name="cpf_rg_socios" class="form-control-file" required>
        </div>
        <div class="form-group">
            <label for="comprovante_residencia_socios">Comprovante de residência do(s) sócio(s):</label>
            <input type="file" id="comprovante_residencia_socios" name="comprovante_residencia_socios" class="form-control-file" required>
        </div>
        <div class="form-group">
            <label for="declaracao_simples">Declaração Opção pelo Simples Digitalizado:</label>
            <input type="file" id="declaracao_simples" name="declaracao_simples" class="form-control-file" required>
        </div>
        <div class="form-group">
            <label for="comprovante_cpf">Comprovante de situação cadastral no CPF:</label>
            <input type="file" id="comprovante_cpf" name="comprovante_cpf" class="form-control-file" required>
        </div>
        <div class="form-group">
            <label for="ccm">CCM (Cadastro de Contribuinte Mobiliário):</label>
            <input type="file" id="ccm" name="ccm" class="form-control-file" required>
        </div>
        <div class="form-group">
            <label for="pis">PIS (Programa de Integração Social):</label>
            <input type="file" id="pis" name="pis" class="form-control-file" required>
        </div>
        <button type="submit" class="btn btn-primary">Enviar</button>
    </form>
</div>
<script src="{{ url_for('static', filename='js/franqueado.js') }}"></script>
{% endblock %}
