<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1.0/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>

<body class="chamados_colaborador">
    <div class="container">
        <div class="col-md-6">
            <div class="input-group">
                <input type="text" id="search-input" class="form-control" placeholder="Buscar por Protocolo ou Assunto">
                <span class="input-icon"><i class="fa-solid fa-magnifying-glass"></i></span>
            </div>
        </div>
        <div class="row mt-4">
            <select id="status-filter" class="select2" multiple="multiple" style="width: 45%;">
                <option value="Aberto">Aberto</option>
                <option value="Em Tratativa">Em Tratativa</option>
                <option value="Retorno Operadora">Aguardando Retorno</option>
                <option value="Resolvido">Resolvido</option>
            </select>

            <select id="sla-filter" class="select2" multiple="multiple" style="width: 45%;">
                <option value="">Filtrar por SLA</option>
                <option value="Atrasado">Atrasado</option>
                <option value="Encerrado">Encerrado</option>
                <option value="No Prazo">No Prazo</option>
            </select>
        </div>

        <div class="row">
            <div class="col-md-12">
                <h3>Gestão de Chamados</h3>
                <table class="table table-striped" id="chamados-table">
                    <thead>
                        <tr>
                            <th class="protocolo">Protocolo</th>
                            <th class="assunto">Assunto</th>
                            <th class="tpChamado">Tipo Chamado</th>
                            <th class="setor">Setor</th>
                            <th class="status">Status</th>
                            <th class="dtAbertura">Data de Abertura</th>
                            <th class="andamento">Dias em Andamento</th>
                            <th class="user_responsavel">Usuário Responsável</th>
                            <th>SLA</th>
                            <th class="action">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for chamado in chamados %}
                        <tr>
                            <td class="protocolo">{{ chamado.protocolo }}</td>
                            <td class="assunto">{{ chamado.assunto }}</td>
                            <td class="tpChamado">{{ chamado.tipo_chamado }}</td>
                            <td class="setor">{{ chamado.setor_nome }}</td>
                            <td class="status">
                                <span
                                    class="badge {% if chamado.status == 'Aberto' %}badge-warning{% elif chamado.status == 'Em Tratativa' %}badge-info{% else %}badge-success{% endif %}">
                                    {{ chamado.status }}
                                </span>
                            </td>
                            <td class="dtAbertura">{{ chamado.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                            <td class="andamento">{{ chamado.dias_aberto }}</td>
                            <td class="user_responsavel">{{ chamado.user_responsavel }}</td>
                            <td class="sla"
                                data-data-limite="{{ chamado.data_limite.isoformat() if chamado.data_limite else '' }}">
                            </td>
                            <td class="action">
                                <a href="{{ url_for('detalhes_chamado_usuario', chamado_id=chamado.id) }}"
                                    class="btn btn-primary btn-sm">Ver Detalhado</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1.0/dist/js/adminlte.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    
    <script src="{{ url_for('static', filename='js/chamado_colaborador.js') }}" defer></script>
</body>

</html>