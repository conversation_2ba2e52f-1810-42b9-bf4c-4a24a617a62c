<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}Painel de Projetos{% endblock %}</title>
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <!-- AdminLTE CSS -->
  <link rel="stylesheet" href="https://adminlte.io/themes/v3/dist/css/adminlte.min.css">
  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
  <!-- FullCalendar CSS (versão global com tema Bootstrap 5) -->
  <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/main.min.css" rel="stylesheet">
  {% block head %}{% endblock %}
</head>

<body class="hold-transition sidebar-mini">
  <div class="wrapper">
    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" data-widget="pushmenu" href="#" role="button">
            <i class="fas fa-bars"></i>
          </a>
        </li>
        <li class="nav-item d-none d-sm-inline-block">
          <a href="{{ url_for('projects_dashboard') }}" class="nav-link">
            <i class="fas fa-home mr-2"></i>Início
          </a>
        </li>
      </ul>

      <form class="form-inline ml-auto">
        <div class="input-group input-group-sm">
          <input class="form-control form-control-navbar" type="search" placeholder="Buscar..." aria-label="Buscar">
          <div class="input-group-append">
            <button class="btn btn-navbar" type="submit">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
      </form>

      <ul class="navbar-nav">
        <li class="nav-item dropdown">
          <a class="nav-link" data-bs-toggle="dropdown" href="#">
            <i class="far fa-bell"></i>
            <span class="badge badge-warning navbar-badge">15</span>
          </a>
          <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
            <span class="dropdown-header">15 Notificações</span>
          </div>
        </li>
        <li class="nav-item dropdown">
          <a class="nav-link" data-bs-toggle="dropdown" href="#">
            <i class="far fa-envelope"></i>
            <span class="badge badge-danger navbar-badge">4</span>
          </a>
          <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
            <span class="dropdown-header">4 Mensagens</span>
          </div>
        </li>
      </ul>
    </nav>
    <!-- /.navbar -->

    <!-- Sidebar -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
      <!-- Brand Logo -->
      <a href="{{ url_for('projects_dashboard') }}" class="brand-link">
        <span class="brand-text font-weight-light">Brazil Health - Tech and BI</span>
      </a>

      <!-- Sidebar Menu -->
      <div class="sidebar">
        <nav class="mt-2">
          <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu">
            <!-- Dashboard Link -->
            <li class="nav-item">
              <a href="{{ url_for('projects_dashboard') }}"
                class="nav-link {% if active_page == 'dashboard' %}active{% endif %}">
                <i class="nav-icon fas fa-tachometer-alt"></i>
                <p>Painel</p>
              </a>
            </li>
            <!-- Projects Menu -->
            <li
              class="nav-item has-treeview {% if active_page in ['project_add', 'project_edit', 'project_details'] %}menu-open{% endif %}">
              <a href="#"
                class="nav-link {% if active_page in ['project_add', 'project_edit', 'project_details'] %}active{% endif %}">
                <i class="nav-icon fas fa-folder"></i>
                <p>
                  Projetos
                  <i class="right fas fa-angle-left"></i>
                </p>
              </a>
              <ul class="nav nav-treeview">
                <li class="nav-item">
                  <a href="{{ url_for('project_add') }}"
                    class="nav-link {% if active_page == 'project_add' %}active{% endif %}">
                    <i class="fas fa-plus nav-icon"></i>
                    <p>Adicionar Projeto</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="{{ url_for('project_details_all') }}"
                    class="nav-link {% if active_page == 'project_details' %}active{% endif %}">
                    <i class="fas fa-info-circle nav-icon"></i>
                    <p>Detalhes dos Projetos</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="{{ url_for('project_tasks') }}"
                    class="nav-link {% if active_page == 'project_tasks' %}active{% endif %}">
                    <i class="fas fa-tasks nav-icon"></i>
                    <p>Tarefas</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="{{ url_for('project_calendar') }}"
                    class="nav-link {% if active_page == 'project_calendar' %}active{% endif %}">
                    <i class="fas fa-calendar-alt nav-icon"></i>
                    <p>Calendário</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="{{ url_for('analytics_projects') }}"
                    class="nav-link {% if active_page == 'analytics_projects' %}active{% endif %}">
                    <i class="fas fa-chart-line nav-icon"></i>
                    <p>Analytics</p>
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </nav>
      </div>
      <!-- /.sidebar -->
    </aside>
    <!-- /.sidebar -->

    <!-- Content Wrapper -->
    <div class="content-wrapper">
      <!-- Content Header -->
      <div class="content-header">
        <div class="container-fluid">
          {% if header %}
          <div class="row mb-2">
            <div class="col-sm-6">
              <h1 class="m-0">{{ header }}</h1>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
      <!-- /.content-header -->

      <!-- Main content -->
      <div class="content">
        <div class="container-fluid">
          {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
          {% for category, message in messages %}
          <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
          </div>
          {% endfor %}
          {% endif %}
          {% endwith %}
          {% block content %}{% endblock %}
        </div>
      </div>
      <!-- /.content -->
    </div>
    <!-- /.content-wrapper -->

    <!-- Footer -->
    <footer class="main-footer">
      <strong>&copy; 2025 <a href="#">Sua Empresa</a>.</strong> Todos os direitos reservados.
    </footer>
  </div>
  <!-- ./wrapper -->

  <!-- Scripts padrão -->
  <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
  <script src="https://adminlte.io/themes/v3/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="https://adminlte.io/themes/v3/dist/js/adminlte.min.js"></script>
  {% block scripts %}{% endblock %}
</body>

</html>