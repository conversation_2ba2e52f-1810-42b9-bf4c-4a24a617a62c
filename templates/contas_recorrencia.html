<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contas Recorrentes</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        .container.mt-5 {
            min-width: 90% !important;
        }
    </style>
</head>

<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <!-- Page Content -->
        <div class="content-wrapper">
            <div class="container custom-container">
                <!-- Header -->
                <section class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1>Contas Recorrentes</h1>
                            </div>
                        </div>
                    </div>
                </section>
                <!-- Main Content -->
                <section class="content">
                    <div class="container mt-5">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h3 class="card-title">Lista de Contas Recorrentes</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nome da Conta</th>
                                            <th>Empresa Pagadora</th>
                                            <th>Fornecedor</th>
                                            <th>Classificação</th>
                                            <th>Valor</th>
                                            <th>Data Início</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for conta in recorrencias %}
                                        <tr>
                                            <td>{{ conta.nome_conta }}</td>
                                            <td>{{ conta.empresa_pagadora }}</td>
                                            <td>{{ conta.fornecedor }}</td>
                                            <td>{{ conta.tipo_conta }}</td>
                                            <td>{{ conta.valor }}</td>
                                            <td>{{ conta.criado_em.strftime('%d/%m/%Y') }}</td>
                                            <td>
                                                <button class="btn btn-info btn-details" data-id="{{ conta.id }}">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr class="details-row" id="details-{{ conta.id }}" style="display: none;">
                                            <td colspan="7">
                                                <!-- Aqui iremos exibir as parcelas via JavaScript -->
                                                <div class="parcelas-container"></div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/js/adminlte.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="/static/js/contas_home.js"></script>
</body>

</html>