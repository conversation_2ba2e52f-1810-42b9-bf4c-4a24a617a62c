<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Comissões</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">Gestão de Comissões</h1>

        <!-- Filtros -->
        <div class="row mb-4">
            <div class="col-md-3">
                <label for="DataTipo" class="form-label">Tipo de Data (Obrigatório)</label>
                <select id="DataTipo" class="form-select">
                    <option value="Producao">Produção</option>
                    <option value="Vencimento">Vencimento</option>
                    <option value="Vigência">Vigência</option>
                    <option value="Inadimplente">Inadimplente</option>
                    <option value="Antecipação">Antecipação</option>
                    <option value="Assinatura">Assinatura</option>
                    <option value="Cadastro">Cadastro</option>
                    <option value="Protocolo">Protocolo</option>
                    <option value="Implantação">Implantação</option>
                    <option value="Borderô">Borderô</option>
                </select>
            </div>

            <div class="col-md-3">
                <label for="Visualizacao" class="form-label">Visualização (Obrigatório)</label>
                <select id="Visualizacao" class="form-select">
                    <option value="Todos">Todos</option>
                    <option value="Em Aberto">Em Aberto</option>
                    <option value="Em Atraso">Em Atraso</option>
                    <option value="Antecipado em Aberto">Antecipado em Aberto</option>
                    <option value="Antecipado em Atraso">Antecipado em Atraso</option>
                    <option value="Inadimplente">Inadimplente</option>
                    <option value="Antecipado">Antecipado</option>
                </select>
            </div>

            <div class="col-md-3">
                <label for="TextoDeProcura" class="form-label">Buscar por proposta ou segurado</label>
                <input type="text" id="TextoDeProcura" class="form-control" placeholder="Buscar...">
            </div>

            <div class="col-md-2">
                <label for="DataInicio" class="form-label">Data Início (Obrigatório)</label>
                <input type="date" id="DataInicio" class="form-control" required>
            </div>
            
            <div class="col-md-2">
                <label for="DataFinal" class="form-label">Data Final (Obrigatório)</label>
                <input type="date" id="DataFinal" class="form-control" required>
            </div>
        </div>

        <!-- Botão para buscar -->
        <div class="row mb-4">
            <div class="col text-center">
                <button id="buscarComissoes" class="btn btn-primary">Buscar</button>
            </div>
        </div>

        <!-- Tabela de Resultados -->
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Corretor</th>
                        <th>Assistente</th>
                        <th>Assistente Original</th>
                        <th>ID Produção</th>
                        <th>Proposta</th>
                        <th>Período</th>
                        <th>Modalidade</th>
                        <th>Conferente</th>
                        <th>Segurado</th>
                        <th>Telefone Celular</th>
                        <th>Telefone Residencial</th>
                        <th>Telefone Comercial</th>
                        <th>Email</th>
                        <th>Data de Vencimento</th>
                        <th>Data de Vigência</th>
                        <th>Data de Produção</th>
                        <th>Data de Assinatura</th>
                        <th>Data de Cadastro</th>
                        <th>Loja</th>
                        <th>Documento</th>
                        <th>Valor Parcela</th>
                        <th>Comissão Total</th>
                        <th>Comissão Corretor</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="comissaoTableBody">
                    <!-- Os dados da API serão injetados aqui -->
                </tbody>
            </table>
        </div>

        <!-- Paginação -->
        <nav aria-label="Paginação">
            <ul class="pagination justify-content-center">
                <li class="page-item"><a class="page-link" href="#">Anterior</a></li>
                <li class="page-item"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item"><a class="page-link" href="#">Próximo</a></li>
            </ul>
        </nav>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="{{ url_for('static', filename='js/gestao-comissoes.js') }}"></script>
</body>
</html>
