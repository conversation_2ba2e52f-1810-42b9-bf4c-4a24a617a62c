{% extends "base.html" %}

{% block title %}Gestão de Boletos de Corretores | BrazilHealth{% endblock %}

{% block body_class %}gestao_boletos_corretores{% endblock %}

{% block content %}
<!-- Bootstrap CSS -->
<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
<!-- AdminLTE3 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3/dist/css/adminlte.min.css">
<!-- FontAwesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

<style>
    .sorted-column {
        background-color: #f0f0f0;
        /* Destaque simples para a coluna classificada */
        font-weight: bold;
    }

    .sort-icon {
        margin-left: 5px;
        color: #333;
        /* Cor do ícone */
    }
</style>

<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <!-- Conteúdo Principal -->
        <div class="content-wrapper">
            <div class="content-header">
                <div class="container-fluid">
                    <h1 class="m-0 text-dark">Gestão de Boletos de Corretores</h1>
                </div>
            </div>
            <!-- Conteúdo da Página -->
            <section class="content">
                <div class="container-fluid">
                    <!-- Dashboard de Insights -->
                    <div class="row">
                        <div class="col-lg-3 col-6">
                            <!-- Caixa Total de Boletos -->
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>{{ boletos|length }}</h3>
                                    <p style="color: white; font-weight: bold;">Total de Boletos Submetidos</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-file-invoice-dollar"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-6">
                            <!-- Caixa Valor Total dos Boletos -->
                            <div class="small-box bg-success">
                                <div class="inner">
                                    <h3>R$ {{ "%.2f"|format(boletos | map(attribute='vlboleto') | map('float') | sum) |
                                        replace('.', ',') }}</h3>
                                    <p style="color: white; font-weight: bold;">Valor Total de Pré-Produção</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Tabela com Boletos -->
                    <div class="card-header d-flex flex-wrap align-items-center">
                        <h3 class="card-title flex-grow-1">Boletos Submetidos</h3>
                        <div class="card-tools ml-auto d-flex flex-wrap align-items-center">
                            <select id="filterSupervisor" class="form-control form-control-sm mr-2 mb-2">
                                <option value="">Todos Supervisores</option>
                                {% for supervisor in boletos | map(attribute='supervisor_nome') | unique %}
                                <option value="{{ supervisor }}">{{ supervisor }}</option>
                                {% endfor %}
                            </select>
                            <select id="filterModalidade" class="form-control form-control-sm mr-2 mb-2">
                                <option value="">Todas Modalidades</option>
                                {% for modalidade in boletos | map(attribute='modalidade') | unique %}
                                <option value="{{ modalidade }}">{{ modalidade }}</option>
                                {% endfor %}
                            </select>
                            <select id="filterOperadora" class="form-control form-control-sm mr-2 mb-2">
                                <option value="">Todas Operadoras</option>
                                {% for operadora in boletos | map(attribute='nome_operadora') | unique %}
                                <option value="{{ operadora }}">{{ operadora }}</option>
                                {% endfor %}
                            </select>
                            <select id="filterUsuario" class="form-control form-control-sm mr-2 mb-2">
                                <option value="">Todos Usuários Solicitantes</option>
                                {% for usuario in boletos | map(attribute='nome_usuario') | unique %}
                                <option value="{{ usuario }}">{{ usuario }}</option>
                                {% endfor %}
                            </select>
                            <input type="month" id="filterMes" class="form-control form-control-sm mr-2 mb-2">
                            <label for="filterMes" class="sr-only">Filtrar por mês</label>
                            <select id="filterStatus" class="form-control form-control-sm mr-2 mb-2">
                                <option value="">Todos os Status</option>
                            </select>
                            <input type="text" id="searchBoletos" class="form-control form-control-sm mr-2 mb-2"
                                placeholder="Buscar por qualquer campo...">
                            <button id="resetFilters" class="btn btn-outline-secondary btn-sm mb-2">
                                Resetar Filtros
                            </button>
                        </div>
                    </div>

                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap" id="tabelaBoletos">
                            <thead>
                                <tr>
                                    <th id="colDataEnvio">Data de Envio <i class="fas fa-sort sort-icon"></i></th>
                                    <th id="colStatus">Status <i class="fas fa-sort sort-icon"></i></th>
                                    <th id="colVisualizarBoleto">Visualizar Boleto</th>
                                    <th id="colAcoes">Ações</th>
                                    <th id="colCodigoProposta">Código Proposta <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th id="colCorretor">Corretor <i class="fas fa-sort sort-icon"></i></th>
                                    <th id="colSupervisor">Supervisor <i class="fas fa-sort sort-icon"></i></th>
                                    <th id="colModalidade">Modalidade <i class="fas fa-sort sort-icon"></i></th>
                                    <th id="colOperadora">Operadora <i class="fas fa-sort sort-icon"></i></th>
                                    <th id="colSubProduto">Sub Produto <i class="fas fa-sort sort-icon"></i></th>
                                    <th id="colSegurado">Segurado <i class="fas fa-sort sort-icon"></i></th>
                                    <th id="colValorPreProducao">Valor Pré-Produção <i
                                            class="fas fa-sort sort-icon"></i></th>
                                    <th id="colAcordo">Acordo <i class="fas fa-sort sort-icon"></i></th>
                                    <th id="colTabelaPadrao">Tabela Padrão <i class="fas fa-sort sort-icon"></i></th>
                                    <th id="colCodigoBarras">Código de Barras <i class="fas fa-sort sort-icon"></i></th>
                                    <th id="colUsuarioSolicitante">Usuário Solicitante <i
                                            class="fas fa-sort sort-icon"></i></th>
                                    <th>Senha do Boleto</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for boleto in boletos %}
                                <tr id="boleto-{{ boleto.codigo_proposta }}">
                                    <td>{{ boleto.data_envio }}</td>
                                    <td class="status-col">
                                        {% if boleto.status == 'Rejeitado' %}
                                        <span class="badge badge-danger">Rejeitado</span>
                                        {% elif boleto.comprovante_upload %}
                                        <span class="badge badge-success">Pago</span>
                                        {% else %}
                                        <span class="badge badge-warning">Pendente</span>
                                        {% endif %}
                                    </td>
                                    <td>{% if boleto.boleto_upload %}
                                        <a href="{{ boleto.boleto_upload }}" target="_blank" class="btn btn-outline-info btn-sm" rel="noopener noreferrer">
                                            <i class="fas fa-eye"></i> Visualizar
                                        </a>
                                        {% if boleto.boleto_senha %}
                                        <button class="btn btn-outline-secondary btn-sm" onclick="mostrarSenha('{{ boleto.codigo_proposta }}')">
                                            <i class="fas fa-key"></i> Mostrar Senha
                                        </button>
                                        {% endif %}
                                        {% else %}
                                        <span class="text-muted">Não disponível</span>
                                        {% endif %}
                                    </td>
                                    <td class="acoes-col">
                                        {% if boleto.status == 'Rejeitado' %}
                                        <button class="btn btn-secondary btn-sm" disabled>Rejeitado</button>
                                        {% elif boleto.comprovante_upload %}
                                        <a href="{{ boleto.comprovante_upload }}" target="_blank"
                                            class="btn btn-outline-info btn-sm" rel="noopener noreferrer"><i
                                                class="fas fa-eye"></i> Visualizar Comprovante</a>
                                        {% else %}
                                        <button class="btn btn-outline-primary btn-sm"
                                            onclick="abrirUploadComprovante('{{ boleto.codigo_proposta }}')"><i
                                                class="fas fa-upload"></i> Upload Comprovante</button>
                                        <button class="btn btn-danger btn-sm"
                                            onclick="rejeitarBoleto('{{ boleto.codigo_proposta }}')">Rejeitar</button>
                                        {% endif %}
                                    </td>
                                    <td>{{ boleto.codigo_proposta }}</td>
                                    <td>{{ boleto.corretor_nome }}</td>
                                    <td>{{ boleto.supervisor_nome }}</td>
                                    <td>{{ boleto.modalidade }}</td>
                                    <td>{{ boleto.nome_operadora }}</td>
                                    <td>{{ boleto.sub_produto }}</td>
                                    <td>{{ boleto.segurado }}</td>
                                    <td>R$ {{ "%.2f"|format(boleto.vlboleto|float) | replace('.', ',') }}</td>
                                    <td>{{ boleto.comissionamento_grade_producao }}</td>
                                    <td>{{ boleto.comissionamento_grade_corretor }}</td>
                                    <td>{{ boleto.codigo_barras }}<button class="btn btn-outline-secondary btn-sm"
                                            onclick="copiarCodigoBarras('{{ boleto.codigo_barras }}')"><i
                                                class="fas fa-copy"></i> Copiar</button></td>
                                    <td>{{ boleto.nome_usuario }}</td>
                                    <td>
                                        {% if boleto.boleto_senha %}
                                        <button class="btn btn-outline-secondary btn-sm" onclick="mostrarSenha('{{ boleto.codigo_proposta }}')">
                                            <i class="fas fa-key"></i> Mostrar Senha
                                        </button>
                                        {% else %}
                                        <span class="text-muted">Não disponível</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="pagination-container d-flex justify-content-center mt-3">
                        <ul id="pagination" class="pagination">
                            <!-- Botões de paginação gerados dinamicamente aqui -->
                        </ul>
                    </div>
                </div>
                <!-- Valor Total por Operadora -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Valor Total dos Boletos por Operadora</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Operadora</th>
                                            <th>Valor Pré-Produção</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for operadora, boletos_operadora in boletos | groupby('nome_operadora') %}
                                        <tr>
                                            <td>{{ operadora }}</td>
                                            <td>R$ {{ "%.2f"|format(boletos_operadora | map(attribute='vlboleto') |
                                                map('float') | sum) | replace('.', ',') }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
        </section>
    </div>
    </div>

    <!-- Modal para inserir senha do boleto -->
    <div class="modal fade" id="modalSenhaBoleto" tabindex="-1" role="dialog" aria-labelledby="modalSenhaBoletoLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header"></div>
                    <h5 class="modal-title" id="modalSenhaBoletoLabel">Inserir Senha do Boleto</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="formSenhaBoleto">
                        <div class="form-group">
                            <label for="senhaBoleto">Senha</label>
                            <input type="password" class="form-control" id="senhaBoleto" required>
                        </div>
                        <input type="hidden" id="codigoPropostaSenha">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
                    <button type="button" class="btn btn-primary" onclick="salvarSenhaBoleto()">Salvar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery, Bootstrap, AdminLTE JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3/dist/js/adminlte.min.js"></script>

    <!-- JavaScript para as Ações -->
    <script>
        // Função para copiar o código de barras
        function copiarCodigoBarras(codigoBarras) {
            navigator.clipboard.writeText(codigoBarras).then(() => {
                alert('Código de barras copiado para a área de transferência.');
            }).catch(err => {
                console.error('Erro ao copiar código de barras: ', err);
            });
        }

        // Função para abrir o modal de upload de comprovante
        function abrirUploadComprovante(codigoProposta) {
            const comprovanteInput = document.createElement('input');
            comprovanteInput.type = 'file';
            comprovanteInput.accept = '.pdf,.png,.jpg,.jpeg';
            comprovanteInput.onchange = function () {
                const formData = new FormData();
                formData.append('file', comprovanteInput.files[0]);
                formData.append('codigo_proposta', codigoProposta);

                // Faz o upload do comprovante via AJAX para a rota correta
                fetch('/upload_comprovante_gestao', {
                    method: 'POST',
                    body: formData
                }).then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Comprovante enviado com sucesso!');
                            location.reload(); // Recarrega a página para atualizar o status
                        } else {
                            alert('Erro ao enviar comprovante.');
                        }
                    }).catch(error => {
                        console.error('Erro ao enviar comprovante:', error);
                        alert('Erro ao enviar comprovante.');
                    });
            };
            comprovanteInput.click(); // Abre o seletor de arquivo
        }

        // Função para rejeitar o boleto
        function rejeitarBoleto(codigoProposta) {
            if (confirm('Tem certeza que deseja rejeitar este boleto?')) {
                fetch('/atualizar_status_boleto', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        codigo_proposta: codigoProposta,
                        status: 'Rejeitado'
                    })
                }).then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Boleto rejeitado com sucesso!');
                            // Atualiza o status na tabela sem recarregar a página
                            const boletoRow = document.getElementById('boleto-' + codigoProposta);
                            boletoRow.querySelector('.status-col').innerHTML = '<span class="badge badge-danger">Rejeitado</span>';
                            boletoRow.querySelector('.acoes-col').innerHTML = '<button class="btn btn-secondary btn-sm" disabled>Rejeitado</button>';
                        } else {
                            alert('Erro ao rejeitar boleto.');
                        }
                    }).catch(error => {
                        console.error('Erro ao rejeitar boleto:', error);
                        alert('Erro ao rejeitar boleto.');
                    });
            }
        }
        let currentPage = 1;
        const rowsPerPage = 25; // Ajuste conforme necessário

        function filterTable() {
            const filters = {
                search: $('#searchBoletos').val().toLowerCase(),
                supervisor: $('#filterSupervisor').val(),
                modalidade: $('#filterModalidade').val(),
                operadora: $('#filterOperadora').val(),
                usuario: $('#filterUsuario').val(),
                mes: $('#filterMes').val(),
                status: $('#filterStatus').val()
            };

            let visibleRows = 0;
            $('#tabelaBoletos tbody tr').each(function () {
                const $row = $(this);
                const rowData = {
                    text: $row.text().toLowerCase(),
                    supervisor: $row.find('td:eq(6)').text(),
                    modalidade: $row.find('td:eq(7)').text(),
                    operadora: $row.find('td:eq(8)').text(),
                    usuario: $row.find('td:eq(15)').text(),
                    date: $row.find('td:eq(0)').text(), // Assumindo que a data está na primeira coluna
                    status: $row.find('td:eq(1)').text().trim() // Ajuste o índice se necessário
                };

                // Parse a string de data
                const [datePart] = rowData.date.split(' - ');
                const [day, month, year] = datePart.split('/');
                const rowDateFormatted = `${year}-${month.padStart(2, '0')}`;

                const matchesFilters =
                    rowData.text.includes(filters.search) &&
                    (filters.supervisor === '' || rowData.supervisor === filters.supervisor) &&
                    (filters.modalidade === '' || rowData.modalidade === filters.modalidade) &&
                    (filters.operadora === '' || rowData.operadora === filters.operadora) &&
                    (filters.usuario === '' || rowData.usuario === filters.usuario) &&
                    (filters.mes === '' || rowDateFormatted.startsWith(filters.mes)) &&
                    (filters.status === '' || rowData.status === filters.status);

                if (matchesFilters) {
                    visibleRows++;
                    const shouldBeVisible = (visibleRows > (currentPage - 1) * rowsPerPage) && (visibleRows <= currentPage * rowsPerPage);
                    $row.toggle(shouldBeVisible);
                } else {
                    $row.hide();
                }
            });

            updatePagination(visibleRows);
        }

        function populateStatusFilter() {
            const statusSet = new Set();
            const $statusFilter = $('#filterStatus');

            // Coleta todos os valores de status exclusivos da tabela
            $('#tabelaBoletos tbody tr').each(function () {
                const status = $(this).find('td:eq(1)').text().trim(); // Ajuste o índice se necessário
                if (status) {
                    statusSet.add(status);
                }
            });

            // Ordena os valores de status
            const sortedStatuses = Array.from(statusSet).sort();

            // Adiciona opções ao elemento select
            sortedStatuses.forEach(status => {
                $statusFilter.append($('<option>', {
                    value: status,
                    text: status
                }));
            });
        }

        function updatePagination(visibleRows) {
            const totalPages = Math.ceil(visibleRows / rowsPerPage);
            const paginationHtml = Array.from({ length: totalPages }, (_, i) =>
                `<li class="page-item ${i + 1 === currentPage ? 'active' : ''}">
            <a class="page-link" href="#" data-page="${i + 1}">${i + 1}</a>
        </li>`
            ).join('');

            $('#pagination').html(paginationHtml);
        }

        function sortTable(columnIndex, isAscending) {
            const rows = $('#tabelaBoletos tbody tr').get();

            rows.sort(function (a, b) {
                const cellA = $(a).children('td').eq(columnIndex).text().toUpperCase();
                const cellB = $(b).children('td').eq(columnIndex).text().toUpperCase();

                if ($.isNumeric(cellA) && $.isNumeric(cellB)) {
                    return isAscending ? cellA - cellB : cellB - cellA;
                } else {
                    return isAscending ? cellA.localeCompare(cellB) : cellB.localeCompare(cellA);
                }
            });

            $.each(rows, function (index, row) {
                $('#tabelaBoletos tbody').append(row);
            });

            currentPage = 1; // Resetar para a primeira página após a ordenação
            filterTable(); // Reaplica os filtros após a ordenação
        }

        function defaultSortTable() {
            const rows = $('#tabelaBoletos tbody tr').get();

            rows.sort(function (a, b) {
                const dateA = $(a).children('td').eq(0).text().split('/');
                const dateB = $(b).children('td').eq(0).text().split('/');

                const formattedDateA = new Date(dateA[2], dateA[1] - 1, dateA[0]);
                const formattedDateB = new Date(dateB[2], dateB[1] - 1, dateB[0]);

                return formattedDateB - formattedDateA; // Ordenar por data mais recente
            });

            $.each(rows, function (index, row) {
                $('#tabelaBoletos tbody').append(row);
            });

            currentPage = 1; // Resetar para a primeira página após a ordenação
            filterTable(); // Reaplica os filtros após a ordenação
        }

        $(document).ready(function () {
            let isAscending = true;
            let lastSortedColumn = null;

            // Adiciona eventos de clique para cada coluna que deve ser classificada
            $('#tabelaBoletos thead th').on('click', function () {
                const columnIndex = $(this).index();

                // Remove o estilo da coluna previamente classificada
                if (lastSortedColumn) {
                    $(lastSortedColumn).removeClass('sorted-column');
                    $(lastSortedColumn).find('.sort-icon').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');
                }

                // Define a nova coluna como a última classificada
                lastSortedColumn = this;

                // Adiciona a classe para destacar a coluna classificada
                $(this).addClass('sorted-column');

                // Altera o ícone de classificação
                const $sortIcon = $(this).find('.sort-icon');
                if (isAscending) {
                    $sortIcon.removeClass('fa-sort').addClass('fa-sort-up');
                } else {
                    $sortIcon.removeClass('fa-sort').addClass('fa-sort-down');
                }

                // Realiza a classificação
                sortTable(columnIndex, isAscending);

                // Alterna o estado da classificação
                isAscending = !isAscending;
            });

            populateStatusFilter();
            const filterInputs = '#searchBoletos, #filterSupervisor, #filterModalidade, #filterOperadora, #filterUsuario, #filterMes, #filterStatus';

            $(filterInputs).on('input change', function () {
                currentPage = 1;
                filterTable();
            });

            $('#resetFilters').on('click', function () {
                $(filterInputs).val('');
                currentPage = 1;
                filterTable();
            });

            $('#pagination').on('click', '.page-link', function (e) {
                e.preventDefault();
                currentPage = parseInt($(this).data('page'));
                filterTable();
            });

            // Configuração inicial da tabela
            defaultSortTable();
        });

        // Função para mostrar a senha do boleto
        function mostrarSenha(codigoProposta) {
            fetch(`/get_boleto_senha/${codigoProposta}`)
                .then(response => response.json())
                .then(data => {
                    if (data.senha) {
                        alert(`Senha do boleto: ${data.senha}`);
                    } else {
                        alert('Senha não encontrada.');
                    }
                })
                .catch(error => {
                    console.error('Erro ao obter a senha do boleto:', error);
                    alert('Erro ao obter a senha do boleto.');
                });
        }

        // Função para abrir o modal de senha do boleto
        function abrirModalSenhaBoleto(codigoProposta) {
            document.getElementById('codigoPropostaSenha').value = codigoProposta;
            $('#modalSenhaBoleto').modal('show');
        }

        // Função para salvar a senha do boleto
        function salvarSenhaBoleto() {
            const codigoProposta = document.getElementById('codigoPropostaSenha').value;
            const senha = document.getElementById('senhaBoleto').value;

            fetch('/upload_comprovante_gestao', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    codigo_proposta: codigoProposta,
                    boleto_senha: senha
                })
            }).then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Senha salva com sucesso!');
                        $('#modalSenhaBoleto').modal('hide');
                    } else {
                        alert('Erro ao salvar a senha.');
                    }
                }).catch(error => {
                    console.error('Erro ao salvar a senha:', error);
                    alert('Erro ao salvar a senha.');
                });
        }
    </script>
</body>

</html>
{% endblock %}