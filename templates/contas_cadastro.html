<!DOCTYPE html>
<html lang="pt">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contas Home</title>
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/css/adminlte.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.0/css/all.min.css">
    <!-- Select2 CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
</head>

<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <div class="content-wrapper">
            <div class="container">
                <!-- Header -->
                <section class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1>Contas Home</h1>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Main Content -->
                <section class="content">
                    <div class="container-fluid">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Gerenciar Contas</h3>
                            </div>
                            <div class="card-body">
                                <ul class="nav nav-tabs" id="tabContas" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" id="tab-cadastrar-conta" data-toggle="tab"
                                            href="#cadastrar-conta" role="tab" aria-controls="cadastrar-conta"
                                            aria-selected="true">
                                            <i class="fas fa-plus-circle"></i> Cadastrar Nova Conta
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="tab-listar-contas" data-toggle="tab"
                                            href="#listar-contas" role="tab" aria-controls="listar-contas"
                                            aria-selected="false">
                                            <i class="fas fa-list"></i> Contas Cadastradas
                                        </a>
                                    </li>
                                </ul>

                                <div class="tab-content" id="tabContentContas">
                                    <!-- Aba Cadastrar Conta -->
                                    <div class="tab-pane fade show active" id="cadastrar-conta" role="tabpanel"
                                        aria-labelledby="tab-cadastrar-conta">
                                        <div class="card mt-3">
                                            <div class="card-header bg-primary text-white">
                                                <h3 class="card-title">Cadastrar Conta</h3>
                                            </div>
                                            <form id="form-contas" enctype="multipart/form-data">
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <label for="nome-conta">Nome da Conta</label>
                                                            <input type="text" class="form-control" id="nome-conta"
                                                                name="nome_conta" placeholder="Digite o nome da conta"
                                                                required>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <label for="empresa-pagadora">Empresa Pagadora</label>
                                                            <select class="form-control select2" id="empresa-pagadora"
                                                                name="empresa_pagadora" required>
                                                                <option value="" disabled selected>Selecione a empresa
                                                                    pagadora</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <label for="tipoBeneficiario">Tipo de beneficiário</label>
                                                            <select id="tipoBeneficiario" class="form-control select2"
                                                                name="tipo_beneficiario" required>
                                                                <option value="" disabled selected>Selecione</option>
                                                                <option value="fornecedor">Fornecedor</option>
                                                                <option value="funcionario">Funcionário</option>
                                                            </select>
                                                        </div>

                                                        <div class="col-md-4" id="containerFornecedor"
                                                            style="display: none;">
                                                            <label for="fornecedorSelect">Fornecedor</label>
                                                            <select class="form-control select2" id="fornecedorSelect"
                                                                name="fornecedor_id">
                                                                <option value="" disabled selected>Selecione o
                                                                    fornecedor</option>
                                                            </select>
                                                        </div>

                                                        <div class="col-md-4" id="containerFuncionario"
                                                            style="display: none;">
                                                            <label for="funcionarioSelect">Funcionário</label>
                                                            <select class="form-control select2" id="funcionarioSelect"
                                                                name="funcionario_id">
                                                                <option value="" disabled selected>Selecione o
                                                                    funcionário</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <div class="row mt-3">
                                                        <div class="col-md-4">
                                                            <label for="valor-conta">Valor da Conta</label>
                                                            <input type="text" class="form-control money-mask"
                                                                id="valor-conta" name="valor" placeholder="R$ 0,00"
                                                                required>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <label for="data-vencimento">Data de Vencimento</label>
                                                            <input type="date" class="form-control date-picker"
                                                                id="data-vencimento" name="data_vencimento"
                                                                placeholder="DD/MM/AAAA" required>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <label for="codigo-barras">Código de Barras</label>
                                                            <input type="text" class="form-control" id="codigo-barras"
                                                                name="codigo_barras"
                                                                placeholder="Digite o código de barras">
                                                        </div>
                                                    </div>

                                                    <div class="row mt-3">
                                                        <div class="col-md-4">
                                                            <label for="tipo_conta">Classificação</label>
                                                            <select class="form-control select2" id="tipo_conta"
                                                                name="tipo_conta" required>
                                                                <option value="" disabled selected>Selecione a
                                                                    classificação da conta</option>
                                                                <option value="Automóvel">Automóvel</option>
                                                                <option value="Custo Funcionário">Custo Funcionário
                                                                </option>
                                                                <option value="Despesas Administrativas">Despesas
                                                                    Administrativas</option>
                                                                <option value="Impostos">Impostos</option>
                                                                <option value="Prestador de Serviços">Prestador de
                                                                    Serviços</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <label for="upload-boleto">Upload de Boleto</label>
                                                            <div class="custom-file">
                                                                <input type="file" class="custom-file-input"
                                                                    id="upload-boleto" name="upload_boleto">
                                                                <label class="custom-file-label"
                                                                    for="upload-boleto">Escolher arquivo</label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <label>Recorrência</label>
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="radio"
                                                                    name="recorrencia" id="recorrencia-unica"
                                                                    value="Única" checked required>
                                                                <label class="form-check-label"
                                                                    for="recorrencia-unica">Única</label>
                                                            </div>
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="radio"
                                                                    name="recorrencia" id="recorrencia-semanal"
                                                                    value="Semanal">
                                                                <label class="form-check-label"
                                                                    for="recorrencia-semanal">Semanal</label>
                                                            </div>
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="radio"
                                                                    name="recorrencia" id="recorrencia-mensal"
                                                                    value="Mensal">
                                                                <label class="form-check-label"
                                                                    for="recorrencia-mensal">Mensal</label>
                                                            </div>
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="radio"
                                                                    name="recorrencia" id="recorrencia-anual"
                                                                    value="Anual">
                                                                <label class="form-check-label"
                                                                    for="recorrencia-anual">Anual</label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row mt-3" id="recorrencia-quantidade-container"
                                                        style="display: none;">
                                                        <div class="col-md-6">
                                                            <label for="quantidade-recorrencia">Quantidade de
                                                                Recorrências</label>
                                                            <input type="number" class="form-control"
                                                                id="quantidade-recorrencia"
                                                                name="quantidade_recorrencia" min="1"
                                                                placeholder="Digite a quantidade de recorrências">
                                                        </div>
                                                    </div>

                                                    <div class="row mt-3">
                                                        <div class="col-md-12">
                                                            <label for="observacoes">Observações</label>
                                                            <textarea class="form-control" id="observacoes"
                                                                name="observacoes" rows="3"
                                                                placeholder="Adicione observações (opcional)"></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="card-footer text-right">
                                                    <button type="submit" class="btn btn-primary">Salvar</button>
                                                    <button type="reset" class="btn btn-secondary">Cancelar</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>

                                    <!-- Aba Listar Contas -->
                                    <div class="tab-pane fade" id="listar-contas" role="tabpanel"
                                        aria-labelledby="tab-listar-contas">
                                        <div class="card mt-3">
                                            <div class="card-header bg-secondary text-white">
                                                <h3 class="card-title">Contas Cadastradas</h3>
                                            </div>
                                            <div class="card-body">
                                                <div class="info-box bg-gradient-warning" style="width: 15%;">
                                                    <span class="info-box-icon"><i
                                                            class="fa-solid fa-money-bill"></i></span>
                                                    <div class="info-box-content">
                                                        <span class="info-box-text">Total à Pagar</span>
                                                        <span class="info-box-number" id="total-valor"></span>
                                                    </div>
                                                </div>
                                                <div class="row mb-4">
                                                    <div class="col-md-3">
                                                        <label for="filtro-periodo">Filtrar por Período</label>
                                                        <input type="text" class="form-control" id="filtro-periodo"
                                                            placeholder="Selecione o período"
                                                            style="background-color: #fff !important;">
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label for="filtro-empresa">Filtrar por Empresa Pagadora</label>
                                                        <select class="form-control" id="filtro-empresa">
                                                            <option value="">Todas Empresas</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label for="filtro-fornecedor">Filtrar por Fornecedor</label>
                                                        <select class="form-control" id="filtro-fornecedor">
                                                            <option value="">Todos Fornecedores</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label for="filtro-classificacao">Filtrar por
                                                            Classificação</label>
                                                        <select class="form-control" id="filtro-classificacao">
                                                            <option value="">Todas Classificações</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th>Nome da Conta</th>
                                                                <th>Empresa Pagadora</th>
                                                                <th>Fornecedor/Funcionário</th>
                                                                <th>Valor</th>
                                                                <th>Data de Vencimento</th>
                                                                <th>Código de Barras</th>
                                                                <th>Recorrência</th>
                                                                <th>Classificação</th>
                                                                <th>Observações</th>
                                                                <th>Boleto</th>
                                                                <th>Ações</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="contas-list">

                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Modal Editar Conta -->
                                <div class="modal fade" id="modalEditarConta" tabindex="-1" role="dialog"
                                    aria-labelledby="modalEditarContaLabel" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="modalEditarContaLabel">Editar Conta</h5>
                                                <button type="button" class="close" data-dismiss="modal"
                                                    aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <form id="form-editar-conta">
                                                    <input type="hidden" id="editar-conta-id">
                                                    <input type="hidden" id="editar-recorrencia-id">
                                                    <div class="form-group">
                                                        <label for="editar-valor-conta">Valor da Conta</label>
                                                        <input type="text" class="form-control" id="editar-valor-conta"
                                                            name="valor" required>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="editar-data-vencimento">Data de Vencimento</label>
                                                        <input type="text" class="form-control date-picker"
                                                            id="editar-data-vencimento" name="data_vencimento" required>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="editar-upload-boleto">Upload de Boleto</label>
                                                        <div class="custom-file">
                                                            <input type="file" class="custom-file-input"
                                                                id="editar-upload-boleto" name="editar_upload_boleto">
                                                            <label class="custom-file-label"
                                                                for="editar-upload-boleto">Escolher arquivo</label>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="editar-codigo-barras">Código de Barras</label>
                                                        <input type="text" class="form-control"
                                                            id="editar-codigo-barras" name="codigo_barras">
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="editar-tipo_conta">Classificação</label>
                                                        <select class="form-control select2" id="editar-tipo_conta"
                                                            name="tipo_conta" required>
                                                            <option value="" disabled selected>Selecione a
                                                                classificação da conta</option>
                                                            <option value="Automóvel">Automóvel</option>
                                                            <option value="Custo Funcionário">Custo Funcionário
                                                            </option>
                                                            <option value="Despesas Administrativas">Despesas
                                                                Administrativas</option>
                                                            <option value="Impostos">Impostos</option>
                                                            <option value="Prestador de Serviços">Prestador de
                                                                Serviços</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="editar-observacoes">Observações</label>
                                                        <textarea class="form-control" id="editar-observacoes"
                                                            name="observacoes" rows="3"></textarea>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary"
                                                    data-dismiss="modal">Cancelar</button>
                                                <button id="salvar-editar-conta" class="btn btn-primary">Salvar</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner-border" role="status">
            <span class="sr-only">Carregando...</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/js/adminlte.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/pt.js"></script>
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/pt.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            $('.select2').select2({
                placeholder: "Selecione uma opção",
                allowClear: true
            });

            // Inicializa Flatpickr
            flatpickr(".date-picker", {
                locale: "pt",
                dateFormat: "d/m/Y"
            });

            // Selecionar "Única" por padrão
            document.getElementById('recorrencia-unica').checked = true;
        });
    </script>
    <script src="/static/js/contas_home.js"></script>
</body>

</html>