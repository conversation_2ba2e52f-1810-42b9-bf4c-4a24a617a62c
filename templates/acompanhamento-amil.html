{% extends "base.html" %}

{% block title %}Intranet | Dashboard de Assistentes{% endblock %}

{% block body_class %}acompanhamentoAmil{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.1.0/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/css/adminlte.min.css">
<style>
    .ultima-atualizacao {
        text-align: right;
        font-size: 0.9em;
        color: #666;
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Filtro de Seleção do Assistente -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="assistente-select">Selecione o Assistente:</label>
                        <select id="assistente-select" class="form-control">
                            {% for assistente, dados in list_acompanhamentos.items() %}
                            <option value="{{ assistente }}">{{ assistente }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="ultima-atualizacao">
                        <span id="data-atualizacao">Última atualização: </span>
                    </div>
                </div>
            </div>

            <!-- Tabela Dinâmica -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Relatório de Status por Assistente</h3>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="assistente-table" class="table table-bordered table-hover">
                                    <thead>
                                        <!-- Nome do assistente vai aparecer aqui -->
                                        <tr>
                                            <th colspan="3" id="assistente-header">Assistente</th>
                                        </tr>
                                        <!-- Colunas de Status e Quantidade -->
                                        <tr>
                                            <th>Status</th>
                                            <th>Quantidade Propostas</th>
                                            <th>Quantidade Beneficiários</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Linhas da tabela vão ser preenchidas pelo JS -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
            </div>

            <!-- Modal -->
            <div id="infoModal" class="custom-modal">
                <div class="custom-modal-content">
                    <span class="close-btn">&times;</span>
                    <h5 class="modal-title">Informações Detalhadas</h5>
                    <div class="modal-body" id="modal-body">
                        <!-- Tabela com os detalhes -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="close-modal-btn" class="btn btn-secondary">Fechar</button>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>
{% endblock %}

{% block scripts %}
<script>
    window.assistentesData = JSON.parse(`{
        {% for assistente, dados in list_acompanhamentos.items() %}
        "{{ assistente }}": [
            {% for dado in dados %}
            {
                "situacao": "{{ dado['situacao'] }}",
                "qtd_beneficiarios": "{{ dado['qtd_beneficiarios'] }}",
                "soma_beneficiarios": "{{ dado['soma_beneficiarios'] }}",
                "ultima_atualizacao": "{{ dado['ultima_atualizacao'] }}"
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ]{% if not loop.last %},{% endif %}
        {% endfor %}
    }`);
</script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.1.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/js/adminlte.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/acompanhamento-amil.js') }}"></script>
{% endblock %}