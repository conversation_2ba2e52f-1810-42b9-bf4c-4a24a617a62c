{% extends "projects_base.html" %}

{% block title %}Edit Project{% endblock %}

{% block header %}
<div class="row mb-2">
    <div class="col-sm-6">
        <h1 class="m-0">Edit Project</h1>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Edit Project Form Card -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Edit Project Details</h3>
    </div>
    <div class="card-body">
        <form action="{{ url_for('project_edit') }}" method="POST" enctype="multipart/form-data">

            <!-- Row 1: Nome do Projeto / Responsável -->
            <div class="row">
                <!-- Nome do Projeto -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectName">Nome do Projeto</label>
                        <input type="text" class="form-control" id="projectName" name="projectName" placeholder="Digite o nome do projeto" required>
                    </div>
                </div>

                <!-- Responsável / Solicitante -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectOwner">Responsável / Solicitante</label>
                        <input type="text" class="form-control" id="projectOwner" name="projectOwner" placeholder="Quem solicitou o projeto?">
                    </div>
                </div>
            </div>

            <!-- Row 2: Descrição / Justificativa -->
            <div class="row">
                <!-- Descrição do Projeto -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectDescription">Descrição</label>
                        <textarea class="form-control" id="projectDescription" name="projectDescription" rows="3" placeholder="Digite a descrição do projeto" required></textarea>
                    </div>
                </div>

                <!-- Justificativa do Projeto -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectJustification">Justificativa / Objetivo</label>
                        <textarea class="form-control" id="projectJustification" name="projectJustification" rows="3" placeholder="Motivo ou objetivo do projeto"></textarea>
                    </div>
                </div>
            </div>

            <!-- Row 3: Prioridade / Complexidade -->
            <div class="row">
                <!-- Prioridade -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectPriority">Prioridade</label>
                        <select class="form-control" id="projectPriority" name="projectPriority">
                            <option value="alta">Alta</option>
                            <option value="media">Média</option>
                            <option value="baixa">Baixa</option>
                        </select>
                    </div>
                </div>

                <!-- Complexidade -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectComplexity">Complexidade</label>
                        <select class="form-control" id="projectComplexity" name="projectComplexity">
                            <option value="alta">Alta</option>
                            <option value="media">Média</option>
                            <option value="baixa">Baixa</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Row 4: Data Início / Data Entrega (Deadline) -->
            <div class="row">
                <!-- Data Início -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectStartDate">Data de Início</label>
                        <input type="date" class="form-control" id="projectStartDate" name="projectStartDate">
                    </div>
                </div>

                <!-- Data de Entrega (Deadline) -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectDeadline">Data de Entrega</label>
                        <input type="date" class="form-control" id="projectDeadline" name="projectDeadline">
                    </div>
                </div>
            </div>

            <!-- Row 5: Status / Upload -->
            <div class="row">
                <!-- Status -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectStatus">Status</label>
                        <select class="form-control" id="projectStatus" name="projectStatus" required>
                            <option value="backlog">Backlog</option>
                            <option value="planejamento">Planejamento</option>
                            <option value="execucao">Em Execução</option>
                            <option value="concluido">Concluído</option>
                        </select>
                    </div>
                </div>

                <!-- Upload de Arquivos -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectFiles">Upload de Arquivos</label>
                        <input type="file" class="form-control" id="projectFiles" name="projectFiles" multiple>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn btn-warning">Atualizar</button>
        </form>
    </div>
</div>
{% endblock %}
