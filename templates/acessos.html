{% extends "base.html" %}

{% block title %}Acessos - Intranet | BrazilHealth{% endblock %}

{% block body_class %}acessos{% endblock %}

{% block content %}
<div class="acessos-container">
    <div class="acessos-header">
        <h2>Acessos Operadoras</h2>
        <button id="clearFilterBtn">Limpar Filtro</button>
    </div>
    <div>
        <div class="search-acessos-container">
            <input type="text" id="searchInput" placeholder="Buscar por Nome, Login, Unidade ou Código"
                onkeyup="searchTable()">
        </div>
        <div id="table-container-acessos">
            <table class="acessos-table">
                <table class="acessos-table">
                    <thead>
                        <tr>
                            <th id="nome-col" class="column-nome">Nome <button class="filter-button"><i
                                        class="bi bi-filter"></i></button></th>
                            <th id="link-col" class="column-link">Link <button class="filter-button"><i
                                        class="bi bi-filter"></i></button></th>
                            <th id="login-col" class="column-login">Login <button class="filter-button"><i
                                        class="bi bi-filter"></i></button></th>
                            <th id="senha-col" class="column-senha">Senha <button class="filter-button"><i
                                        class="bi bi-filter"></i></button></th>
                            <th id="unidade-col" class="column-unidade">Unidade/Empresa <button class="filter-button"><i
                                        class="bi bi-filter"></i></button></th>
                            <th id="modalidade-col" class="column-modalidade">Modalidade <button
                                    class="filter-button"><i class="bi bi-filter"></i></button></th>
                            <th id="codigo-col" class="column-codigo">Código <button class="filter-button"><i
                                        class="bi bi-filter"></i></button></th>
                            <th id="outros-col" class="column-outros">Outros <button class="filter-button"><i
                                        class="bi bi-filter"></i></button></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for acessos in listar_acessos %}
                        <tr>
                            <td data-label="nome" class="column-nome">{{ acessos.nome }}</td>
                            <td data-label="link" class="column-link">
                                <a href="{{ acessos.link }}" class="link-acessos" target="_blank">{{ acessos.link }}</a>
                            </td>
                            <td data-label="login" class="column-login">{{ acessos.login }}</td>
                            <td data-label="senha" class="column-senha">
                                <span class="senha-text" data-password="{{ acessos.senha }}">***********</span>
                                <i class="fa-solid fa-copy" onclick="copyPassword(this)"></i>
                                <i class="fa-solid fa-eye" onclick="togglePasswordVisibility(this)"></i>
                                {% if has_permission or session.get('user_type') in [1, 7]%}
                                <i class="fa-solid fa-pen-to-square"
                                    onclick="openAcessModal('{{ acessos.id }}', '{{ acessos.portal_id }}', '{{ acessos.nome }}', '{{ acessos.link }}', '{{ acessos.login }}', '{{ acessos.senha }}', '{{ acessos.unidade }}', '{{ acessos.codigo }}', '{{ acessos.outros }}', '{{ acessos.modalidade }}')">
                                </i>

                                {% endif %}
                            </td>
                            <td data-label="unidade" class="column-unidade">{{ acessos.unidade }}</td>
                            <td data-label="modalidade" class="column-modalidade" title="{{ acessos.modalidade }}">{{
                                acessos.modalidade }}</td>
                            <td data-label="codigo" class="column-codigo" title="{{ acessos.codigo }}">{{ acessos.codigo
                                }}</td>
                            <td data-label="outros" class="column-outros" title="{{ acessos.outros }}">{{ acessos.outros
                                }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/acessos.js') }}"></script>
{% endblock %}