{% extends "base.html" %}

{% block title %}Gestão de Fast Money - Intranet{% endblock %}

{% block extra_styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1.0/dist/css/adminlte.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">

<style>
  /* Estilos gerais para os cartões */
  .card {
    position: flex;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
    padding: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
  }

  .card p {
    line-height: 1.6;
    margin-bottom: 10px;
    color: #555;
  }

  .card h4 {
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
  }

  .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }

  .card-title {
    font-size: 1.6em;
    font-weight: bold;
    color: #333;
  }

  /* Estilos específicos para as colunas */
  .column.pending {
    background-color: #d6e3ff;
    padding: 10px;
    border-radius: 10px;
  }

  .column.approved {
    background-color: #dfffdc;
    padding: 10px;
    border-radius: 10px;
  }

  .column.rejected {
    background-color: #ffdddd;
    padding: 10px;
    border-radius: 10px;
  }

  /* Estilos para os cabeçalhos das colunas */
  .card-header {
    background-color: white;
    color: #333;
    text-align: center;
    font-weight: bold;
    padding: 10px;
    border-radius: 10px 10px 0 0;
  }

  /* Botões de ação (Ver Detalhes) */
  .btn-info {
    background-color: #ff5722;
    color: white;
    border-radius: 5px;
    padding: 10px 20px;
    border: none;
    transition: background-color 0.3s ease;
  }

  .btn-info:hover {
    background-color: #e64a19;
  }

  /* Ajuste de layout para as colunas */
  .row {
    display: flex;
    justify-content: flex;
    gap: 0px;
    margin-top: 20px;
  }

  .column {
    flex: 1;
    margin: 0px;
  }

  /* Ajuste da altura mínima das colunas */
  .column {
    min-height: 400px;
  }

  /* Para o cartão de rejeitados */
  .card-danger.card-outline {
    border: 3px solid #dc3545;
  }

  /* Para o cartão de aprovados */
  .card-success.card-outline {
    border: 3px solid #28a745;
  }

  /* Para o cartão de pendentes */
  .card-primary.card-outline {
    border: 3px solid #007bff;
  }

  /* Container principal de conteúdo */
  main.content-container {
    flex: 1;
    padding: 20px;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    margin-left: 0;
    gap: 0.5rem;
  }

  /* Estilo exclusivo para o card de filtro de data */
  .cartao-data-filtro {
    background-color: #e8e8e8;
    border: 1px solid #bbbbbb;
    border-radius: 10px;
    padding: 10px;
    margin-top: 30px;
    margin-bottom: 20px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .cabecalho-data-filtro {
    color: black;
    padding: 10px;
    border-radius: 10px 10px 0 0;
    text-align: center;
  }

  .campo-data-filtro {
    width: 100%;
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 14px;
  }

  /* Estilos para a barra de rolagem nas colunas */
  .column {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 10px;
  }

  /* Estilização da barra de rolagem */
  .column::-webkit-scrollbar {
    width: 8px;
  }

  .column::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .column::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 10px;
  }

  .column::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  /* Estilos gerais para o modal */
  .modal-detalhes {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    font-family: 'Roboto', sans-serif;
  }

  .modal-detalhes .modal-content {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    padding: 20px;
    max-width: 600px;
    width: 100%;
    overflow-y: auto;
    max-height: 80%;
    position: relative;
    padding-top: 50px;
    /* Espaço para o ribbon */
  }

  /* Cabeçalho do modal */
  .modal-detalhes h2 {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
    text-align: center;
  }

  /* Estilo para os parágrafos dentro do modal */
  .modal-detalhes p {
    font-size: 1rem;
    color: #555;
    margin-bottom: 10px;
  }

  .modal-detalhes strong {
    color: #333;
  }

  /* Estilo para os links de comprovantes e boletos */
  .modal-detalhes a {
    color: #007bff;
    text-decoration: none;
  }

  .modal-detalhes a:hover {
    text-decoration: underline;
  }

  /* Botões de ação (aprovar/rejeitar) */
  .modal-detalhes button {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    color: #fff;
    margin-top: 10px;
    transition: background-color 0.3s ease;
  }

  /* Botão de aprovar */
  .modal-detalhes .approve-btn {
    background-color: #28a745;
  }

  .modal-detalhes .approve-btn:hover {
    background-color: #218838;
  }

  /* Botão de rejeitar */
  .modal-detalhes .reject-btn {
    background-color: #dc3545;
  }

  .modal-detalhes .reject-btn:hover {
    background-color: #c82333;
  }

  /* Botão de fechar o modal */
  .modal-detalhes .close-button {
    position: absolute;
    top: 15px;
    right: 15px;
    background: transparent;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #333;
  }

  .modal-detalhes .close-button:hover {
    color: #000;
  }

  /* Estilo específico para o ribbon no modal */
  .ribbon-wrapper-modal {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    overflow: hidden;
    width: 75px;
    /* Ajuste conforme necessário */
    height: 75px;
  }

  .ribbon-modal {
    position: absolute;
    top: 15px;
    left: -23px;
    transform: rotate(-45deg);
    width: 100px;
    /* Ajuste para evitar corte de texto */
    background-color: #007bff;
    /* Cor para status Rejeitado */
    color: white;
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    line-height: 25px;
    text-transform: uppercase;
    box-shadow: 0 3px 10px -5px rgba(0, 0, 0, 1);
  }

  .ribbon-modal.bg-success {
    background-color: #28a745;
    /* Cor para status Aprovada */
  }

  .ribbon-modal.bg-danger {
    background-color: #dc3545;
    /* Cor para status Rejeitada */
  }

  .ribbon-modal.bg-primary {
    background-color: #007bff;
    /* Cor para status Pendente */
  }

  select#motivoRejeicao {
    width: calc(100% - 20px);
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    outline: 0;
    padding: 0.5rem;
    margin-bottom: 0.8rem;
  }

  textarea#comentarioRejeicao {
    width: calc(100% - 20px);
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    outline: 0;
    padding: 0.5rem;
    margin-bottom: 0.8rem;
  }

  /* Responsividade */
  @media (max-width: 768px) {
    .modal-detalhes .modal-content {
      max-width: 90%;
    }

    .modal-detalhes h2 {
      font-size: 1.5rem;
    }
  }
</style>
{% endblock %}
{% block body_class %}gestao-fastmoney{% endblock %}
{% block content %}
<!-- Adiciona o JSON com os dados das solicitações de Fast-Money -->
<script id="fastmoney-data" type="application/json">
    {{ fastmoney | tojson }}
</script>

<!-- Filtro de Data -->
<div class="cartao cartao-data-filtro">
  <div class="cabecalho-cartao cabecalho-data-filtro">
    <h3 class="titulo-cartao titulo-data-filtro">Filtrar por Semana</h3>
  </div>
  <div class="corpo-cartao corpo-data-filtro">
    <input type="text" id="dateRangePicker" class="campo-data-filtro" placeholder="Selecione o período" />
  </div>
</div>

<!-- Board Kanban -->
<div class="row">
  <!-- Coluna de Solicitações Pendentes -->
  <div class="col-md-4">
    <div class="card card-primary card-outline">
      <div class="card-header">
        <h3 class="card-title">
          Solicitações Pendentes
          <span class="badge badge-primary">{{ fastmoney|selectattr('status', 'equalto', 'Pendente')|list|length
            }}</span>
        </h3>
      </div>
      <div class="column pending" ondrop="drop(event)" ondragover="allowDrop(event)">
        {% for solicitacao in fastmoney %}
        {% if solicitacao.status == 'Pendente' %}
        <div class="card" draggable="true" id="{{ solicitacao.id }}" ondragstart="drag(event)">
          <!-- Ribbon para Solicitação Pendente -->
          <div class="ribbon-wrapper ribbon-lg">
            <div class="ribbon bg-primary">
              Pendente
            </div>
          </div>
          <h4>Solicitação de Adiantamento</h4>
          <p><strong>Corretor:</strong> {{ solicitacao.nome_empresa }}</p>
          <p><strong>Supervisor:</strong> {{ solicitacao.supervisor }}</p>
          <p><strong>Valor:</strong> R$ {{ solicitacao.valor_proposta }}</p>
          <p><strong>Data de Solicitação:</strong> {{ solicitacao.data_criacao }}</p>
          <button class="btn btn-info btn-sm" onclick="openModal(this.parentElement)">Ver Detalhes</button>
        </div>
        {% endif %}
        {% endfor %}
      </div>
    </div>
  </div>

  <!-- Coluna de Solicitações Aprovadas -->
  <div class="col-md-4">
    <div class="card card-success card-outline">
      <div class="card-header">
        <h3 class="card-title">
          Solicitações Aprovadas
          <span class="badge badge-success">{{ fastmoney|selectattr('status', 'equalto', 'Aprovado')|list|length
            }}</span>
        </h3>
      </div>
      <div class="column approved" ondrop="drop(event)" ondragover="allowDrop(event)">
        {% for solicitacao in fastmoney %}
        {% if solicitacao.status == 'Aprovado' %}
        <div class="card" draggable="true" id="{{ solicitacao.id }}" ondragstart="drag(event)">
          <!-- Ribbon para Solicitação Aprovada -->
          <div class="ribbon-wrapper ribbon-lg">
            <div class="ribbon bg-success">
              Aprovada
            </div>
          </div>
          <h4>Solicitação de Adiantamento</h4>
          <p><strong>Corretor:</strong> {{ solicitacao.nome_empresa }}</p>
          <p><strong>Supervisor:</strong> {{ solicitacao.supervisor }}</p>
          <p><strong>Valor:</strong> R$ {{ solicitacao.valor_proposta }}</p>
          <p><strong>Data de Solicitação:</strong> {{ solicitacao.data_criacao }}</p>
          <button class="btn btn-info btn-sm" onclick="openModal(this.parentElement)">Ver Detalhes</button>
        </div>
        {% endif %}
        {% endfor %}
      </div>
    </div>
  </div>

  <!-- Coluna de Solicitações Rejeitadas -->
  <div class="col-md-4">
    <div class="card card-danger card-outline">
      <div class="card-header">
        <h3 class="card-title">
          Solicitações Rejeitadas
          <span class="badge badge-danger">{{ fastmoney|selectattr('status', 'equalto', 'Rejeitado')|list|length
            }}</span>
        </h3>
      </div>
      <div class="column rejected" ondrop="drop(event)" ondragover="allowDrop(event)">
        {% for solicitacao in fastmoney %}
        {% if solicitacao.status == 'Rejeitado' %}
        <div class="card" draggable="true" id="{{ solicitacao.id }}" ondragstart="drag(event)">
          <!-- Ribbon para Solicitação Rejeitada -->
          <div class="ribbon-wrapper ribbon-lg">
            <div class="ribbon bg-danger">
              Rejeitada
            </div>
          </div>
          <h4>Solicitação de Adiantamento</h4>
          <p><strong>Corretor:</strong> {{ solicitacao.nome_empresa }}</p>
          <p><strong>Supervisor:</strong> {{ solicitacao.supervisor }}</p>
          <p><strong>Valor:</strong> R$ {{ solicitacao.valor_proposta }}</p>
          <p><strong>Data de Solicitação:</strong> {{ solicitacao.data_criacao }}</p>
          <button class="btn btn-info btn-sm" onclick="openModal(this.parentElement)">Ver Detalhes</button>
        </div>
        {% endif %}
        {% endfor %}
      </div>
    </div>
  </div>

  <!-- Modal para detalhes da solicitação -->
  <div id="modal" class="modal-detalhes" style="display: none;">
    <div class="modal-content">
      <!-- Ribbon inserido corretamente dentro do modal, no canto superior esquerdo -->
      <div id="ribbon-container" class="ribbon-wrapper-modal">
        <div id="ribbon-text" class="ribbon-modal bg-primary">Aprovada</div>
      </div>
      <span class="close-button" onclick="closeModal()">&times;</span>
      <h2>Detalhes da Solicitação</h2>
      <p><strong>Corretor:</strong> <span id="modal-corretor"></span></p>
      <p><strong>Supervisor:</strong> <span id="modal-supervisor"></span></p>
      <p><strong>Código da Proposta:</strong> <span id="modal-cod_proposta"></span></p>
      <p><strong>Segurado:</strong> <span id="modal-segurado"></span></p>
      <p><strong>Operadora:</strong> <span id="modal-operadora"></span></p>
      <p><strong>Valor da Proposta:</strong> R$ <span id="modal-valor"></span></p>
      <p><strong>Modalidade:</strong> <span id="modal-modalidade"></span></p>
      <p><strong>Tipo de Adiantamento:</strong> <span id="modal-tipo_adiantamento"></span></p>
      <p><strong>Acordo:</strong> <span id="modal-acordo"></span></p>
      <p><strong>Tabela Padrão:</strong> <span id="modal-tabela"></span></p>
      <p><strong>Comprovante:</strong> <a href="#" id="modal-comprovante">Download</a></p>
      <p><strong>Boleto:</strong> <a href="#" id="modal-boleto">Download</a></p>
      <p><strong>Sub-Produto:</strong> <span id="modal-sub_produto"></span></p>
      <!-- Campos de rejeição no modal de detalhes -->
      <div id="rejection-section" style="display: none;">
        <p><strong>Motivo da Rejeição:</strong> <span id="motivoRejeicaoDetalhes"></span></p>
        <p><strong>Comentário da Rejeição:</strong> <span id="comentarioRejeicaoDetalhes"></span></p>
      </div>


      <!-- Campos de rejeição -->
      <div id="rejection-fields" style="display: none;">
        <label for="motivoRejeicao">Motivo da Rejeição:</label>
        <select id="motivoRejeicao">
          <option value="corretor_novo">Corretor Novo (Não permitido reanálise)</option>
          <option value="falta_comprovante">Falta de Comprovante e/ou Boleto (Permitido reanálise)</option>
          <option value="nao_implantada">Não implantada na Operadora (Não permitido reanálise)</option>
          <option value="solicitacao_repetida">Solicitação Repetida (Não permitido reanálise)</option>
          <option value="outros">Outros (Escreva o motivo da rejeição)</option>
        </select>

        <!-- Comentário obrigatório -->
        <textarea id="comentarioRejeicao" placeholder="Descreva o motivo da rejeição" required></textarea>
        <!-- Botão de submissão -->
        <button class="submit-rejection" onclick="submitRejection()">Enviar Rejeição</button>
      </div>

      <!-- Botões de aprovar e rejeitar -->
      {% if session.get('user_type') in [1, 7] %}
      <button class="approve-btn" onclick="approveRequest()">Aprovar</button>
      <button class="reject-btn" onclick="showRejectionFields()">Rejeitar</button>
      {% endif %}
    </div>
  </div>

  {% endblock %}

  {% block scripts %}
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.9/dist/l10n/pt.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1.0/dist/js/adminlte.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
  <script src="{{ url_for('static', filename='js/gestao-fastmoney.js') }}"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js"></script>
  {% endblock %}