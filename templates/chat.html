<div class="chatWrapper">
    <div class="chatContainer">
        <div class="messages" id="messages"></div>
        <div class="messageInputContainer" style="display: flex; align-items: center;">
            <input type="text" class="messageInput" id="messageInput" placeholder="Type a message...">
            <button class="sendButton" onclick="sendMessage()">Send</button>
        </div>
    </div>
    <div class="userListContainer">
        <h3>Selecione um Departamento e Usuário</h3>
        <div class="dropdownContainer">
            <select id="departmentDropdown" onchange="populateUsers()">
                <option value="">Selecione um Departamento</option>
                <option value="DeptA">Financeiro</option>
                <option value="DeptB">Pós Vendas</option>
            </select>
            <select id="userDropdown" onchange="selectUser(this.value)">
                <option value="">Selecione um Usuário</option>
                <option value="DeptA"><PERSON><PERSON><PERSON><PERSON></option>
                <option value="DeptB"><PERSON><PERSON></option>
            </select>
        </div>
        <div class="userList" id="userList">
            <!-- User list will be dynamically populated -->
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const users = {
        DeptA: ['User 1', 'User 2', 'User 3'],
        DeptB: ['User 4', 'User 5', 'User 6']
    };

    window.populateUsers = function() {
        const department = document.getElementById('departmentDropdown').value;
        const userDropdown = document.getElementById('userDropdown');
        userDropdown.innerHTML = '<option value="">Select User</option>'; // Reset user dropdown

        if (department && users[department]) {
            users[department].forEach(user => {
                const option = document.createElement('option');
                option.value = user;
                option.textContent = user;
                userDropdown.appendChild(option);
            });
        }
    };

    window.selectUser = function(user) {
        if (user) {
            document.getElementById('messages').innerHTML = `<div class="message them">Chat started with ${user}</div>`;
        }
    };

    window.sendMessage = function() {
        const message = document.getElementById('messageInput').value;
        if (message.trim() === '') return;

        const messagesDiv = document.getElementById('messages');
        const messageElement = document.createElement('div');
        messageElement.className = 'message you';
        messageElement.textContent = `You: ${message}`;
        messagesDiv.appendChild(messageElement);
        messagesDiv.scrollTop = messagesDiv.scrollHeight; // Scroll to bottom

        document.getElementById('messageInput').value = '';
    };
});
</script>
