<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intranet | BrazilHealth</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body class="inactive">
    <div class="header-image-inactive">
        <img src="https://brazilhealth.com.br/wp-content/uploads/2020/03/07_Prancheta-1-cópia-6-e1583518361332-1.png" alt="Logo BrazilHealth">
    </div>
    <div class="inactive-container">
        <h1 id="greeting"></h1>
        <br />
        <p id="text-inactive">
            Sua conta está inativa. Por favor, entre em contato com o administrador.
        </p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            function getGreeting() {
                const now = new Date();
                const hours = now.getHours();
                let greeting;

                if (hours < 12) {
                    greeting = "Bom dia";
                } else if (hours < 18) {
                    greeting = "Boa tarde";
                } else {
                    greeting = "Boa noite";
                }

                return greeting;
            }

            function setGreeting() {
                const greetingElement = document.getElementById('greeting');
                const userName = "{{ user_name }}"; // Obtenha o nome do usuário dinamicamente do backend
                const greeting = `${getGreeting()}, ${userName}!`;

                greetingElement.textContent = greeting;
            }

            setGreeting();
        });
    </script>
</body>
</html>
