{% extends "base.html" %}

{% block title %}Intranet | Gestão de Candidaturas{% endblock %}

{% block body_class %}gestao-candidatura{% endblock %}

{% block extra_styles %}
<link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/css/adminlte.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Processos Seletivos Abertos</h3>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                            <select id="seletivo-select" class="form-control mb-3">
                                <option value="" selected>Selecione o Processo Seletivo</option>
                                {% for seletivo in processos %}
                                <option value="{{ seletivo['id'] }}">{{ seletivo['titulo'] }}</option>
                                {% endfor %}
                            </select>                            

                            <table id="seletivo-table" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Nome</th>
                                        <th>Departamento Atual</th>
                                        <th>Cargo Atual</th>
                                        <th>Possui CNH?</th>
                                        <th>Possui Carro Próprio?</th>
                                        <th>Formação</th>
                                        <th>Qual Curso?</th>
                                        <th>Certificações</th>
                                        <th>Motivo</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Preenchido dinamicamente com JS -->
                                </tbody>
                            </table>

                            <!-- Botão de Exportar para Excel -->
                            <button id="export-excel" class="btn btn-success mt-3">Exportar para Excel</button>
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/js/adminlte.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/gestao_candidaturas.js') }}"></script>
{% endblock %}