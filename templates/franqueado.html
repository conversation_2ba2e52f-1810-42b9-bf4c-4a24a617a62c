{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}
{% block body_class %}area-do-franqueado{% endblock %}
{% block extra_styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
{% endblock %}

{% block content %}
<div class="franqueado-container">
    <div class="card-container">
        <div class="card" data-content="chamados" onclick="showChamados()">
            <i class="fa-solid fa-envelope-circle-check"></i>
            <h3>Chamados</h3>
            <p>Confira o andamento das suas solicitações ou abra um novo chamado.</p>
            <a href="#" class="btn-acessar">Acessar</a>
        </div>

        <div class="card" data-content="documentos" onclick="showDocumentos()">
            <i class="fas fa-file-alt"></i>
            <h3>Documentos</h3>
            <p>Acesse aqui documentos importantes sobre sua franquia.</p>
            <a href="#" class="btn-acessar">Acessar</a>
        </div>

        <div class="card" data-content="desempenho">
            <i class="fas fa-chart-line"></i>
            <h3>Desempenho</h3>
            <p>Acompanhe a performance da sua franquia.</p>
            <a href="/breve" class="btn-acessar">Acessar</a>
        </div>

        <div class="card" data-content="help-desk" onclick="showHelpDesk()">
            <i class="fas fa-headset"></i>
            <h3>Help Desk</h3>
            <p>Acesse o suporte técnico e resolva suas dúvidas rapidamente.</p>
            <a href="#" class="btn-acessar">Acessar</a>
        </div>

        <div class="card" data-content="eventos">
            <i class="fas fa-calendar-alt"></i>
            <h3>Eventos</h3>
            <p>Veja os próximos eventos e treinamentos.</p>
            <a href="{{ url_for('calendar') }}" class="btn-acessar">Acessar</a>
        </div>

        <div class="card" data-content="processos" onclick="showProcessos()">
            <i class="fas fa-tasks"></i>
            <h3>Processos</h3>
            <p>Acompanhe suas propostas, solicitações, status de processos e formulários.</p>
            <a href="#" class="btn-acessar">Acessar</a>
        </div>

        <div class="card" data-content="sistemas" onclick="showSystem()">
            <i class="fas fa-cogs"></i>
            <h3>Sistemas</h3>
            <p>Acesse os principais sistemas por aqui.</p>
            <a href="#" class="btn-acessar">Acessar</a>
        </div>

        <div class="card" data-content="sugestoes">
            <i class="fa-solid fa-envelope-open-text"></i>
            <h3>Sugestões</h3>
            <p>Envie suas sugestões por aqui.</p>
            <a href="/enviar_sugestao" class="btn-acessar">Acessar</a>
        </div>
    </div>
</div>

<div id="documentos-container" class="documentos-container" style="display:none;">
    <div class="sidebar">
        <h3>Documentos Importantes</h3>
        <ul>
            <li onclick="loadDocumentContent('circular-oferta')">Circular de Oferta de Franquia (COF)</li>
            <li onclick="loadDocumentContent('contrato-franquia')">Contrato de Franquia</li>
            <li onclick="loadDocumentContent('plano-negocios')">Plano de Negócios</li>
            <li onclick="loadDocumentContent('manual-marca')">Manual da Marca</li>
            <li onclick="loadDocumentContent('certificados')">Certificados</li>
            <li onclick="loadDocumentContent('tabela-comissao')">Tabela de Comissão</li>
            <li onclick="loadDocumentContent('comunicados')">Comunicados</li>
            <li onclick="loadDocumentContent('voltar')">
                <i class="fas fa-arrow-left"></i> Voltar
            </li>
        </ul>
    </div>
    <div id="document-content" class="main-content">
        <!-- O conteúdo será carregado aqui -->
    </div>
</div>

<!-- Novo Formulário de Plano de Ação -->
<div class="area-do-franqueado">
    <div class="franqueado-container">
        <div id="plano-negocios-container" class="plano-acao-container" style="display:none;">
            <h2>Plano de Ação Unidade Franqueada Brazil Health</h2>
            <form>
                <div class="plano-acao-container">
                    <label for="unidade">Unidade:</label>
                    <input type="text" id="unidade" name="unidade" class="form-control" placeholder="Digite a unidade">
                </div>
                <div class="plano-acao-container">
                    <label for="franqueado">Franqueado:</label>
                    <input type="text" id="franqueado" name="franqueado" class="form-control"
                        placeholder="Digite o nome do franqueado">
                </div>
                <div class="plano-acao-container">
                    <label for="preenchido-por">Preenchido por:</label>
                    <input type="text" id="preenchido-por" name="preenchido-por" class="form-control"
                        placeholder="Digite o nome">
                </div>
                <div class="plano-acao-container">
                    <label for="data">Data:</label>
                    <input type="date" id="data" name="data" class="form-control">
                </div>
                <h3>Tabela de Ações</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Prioridade</th>
                            <th>No</th>
                            <th>Ação (O que)</th>
                            <th>Execução (Como)</th>
                            <th>Prazo (Quando)</th>
                            <th>Responsável</th>
                            <th>Observações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <select class="form-control">
                                    <option>Alta</option>
                                    <option>Média</option>
                                    <option>Baixa</option>
                                </select>
                            </td>
                            <td><input type="text" class="form-control"></td>
                            <td><input type="text" class="form-control"></td>
                            <td><input type="text" class="form-control"></td>
                            <td><input type="date" class="form-control"></td>
                            <td><input type="text" class="form-control"></td>
                            <td><input type="text" class="form-control"></td>
                        </tr>
                    </tbody>
                </table>
                <div class="plano-acao-container">
                    <label for="anotacoes">Anotações:</label>
                    <textarea id="anotacoes" name="anotacoes" class="form-control"
                        placeholder="Digite as anotações"></textarea>
                </div>
                <div class="plano-acao-container">
                    <label for="data-final">Data:</label>
                    <input type="date" id="data-final" name="data-final" class="form-control">
                </div>
                <div class="plano-acao-container">
                    <label for="franqueado-final">Franqueado:</label>
                    <input type="text" id="franqueado-final" name="franqueado-final" class="form-control"
                        placeholder="Digite o nome do franqueado">
                </div>
                <div class="plano-acao-container">
                    <label for="supervisor">Supervisor Franquias:</label>
                    <input type="text" id="supervisor" name="supervisor" class="form-control"
                        placeholder="Digite o nome do supervisor">
                </div>
                <button type="submit" class="btn btn-primary">Enviar</button>
            </form>
        </div>
    </div>
</div>

<div id="help-desk-container" class="help-desk-container" style="display:none;">
    <div class="sidebar">
        <h3>Help Desk</h3>
        <ul>
            <li onclick="loadHelpDeskContent('problemas-login')">Primeiro Acesso - Painel do Corretor</li>
            <li onclick="loadHelpDeskContent('redefinir-senha')">Como redefinir sua senha?</li>
            <li onclick="loadHelpDeskContent('abrir-chamado')">Abra seu chamado</li>
            <li onclick="loadHelpDeskContent('status')">Status dos Sistemas</li>
            <li onclick="loadHelpDeskContent('ramais')">Ramais Internos</li>
            <li onclick="loadDocumentContent('voltar')">
                <i class="fas fa-arrow-left"></i> Voltar
            </li>
        </ul>
    </div>
    <div id="help-desk-content" class="main-content">
        <!-- O conteúdo será carregado aqui -->
    </div>
</div>

<div id="processos-container" class="processos-container" style="display:none;">
    <div class="sidebar">
        <h3>Processos e Acompanhamento</h3>
        <ul>
            <li onclick="loadProcessContent('proposta')">Acompanhe suas propostas</li>
            <li onclick="loadProcessContent('solicitacoes')">Acompanhe suas Solicitações</li>
            <li onclick="loadProcessContent('alteracao-conta')">Alteração de conta bancária</li>
            <li onclick="loadProcessContent('codigo-operadora')">Abertura de código na operadora</li>
            <li onclick="loadProcessContent('formularios')">Formulários</li>
            <li onclick="loadDocumentContent('voltar')">
                <i class="fas fa-arrow-left"></i> Voltar
            </li>
        </ul>
    </div>
    <div id="process-content" class="main-content">
        <!-- O conteúdo será carregado aqui -->
    </div>
</div>

<div id="sistemas-container" class="sistemas-container" style="display:none;">
    <div class="sidebar">
        <h3>Sistemas</h3>
        <ul>
            <li onclick="loadSystemContent('sistema1')">Painel do Corretor - PDC</li>
            <li onclick="loadSystemContent('sistema2')">Produtos</li>
            <li onclick="loadSystemContent('sistema3')">Comunica Brazil</li>
            <li onclick="loadSystemContent('sistema4')">Universidade BRH</li>
            <li onclick="loadDocumentContent('voltar')">
                <i class="fas fa-arrow-left"></i> Voltar
            </li>
        </ul>
    </div>
    <div id="sistemas-content" class="main-content">
        <!-- O conteúdo será carregado aqui -->
    </div>
</div>

<div id="chamados-container" class="chamados-container" style="display:none;">
    <div class="sidebar">
        <h3>Sistemas</h3>
        <ul>
            <li onclick="loadChamadosContent('sistema1')">Novo Chamado</li>
            <li onclick="loadChamadosContent('sistema2')">Acompanhar suas solicitações</li>
            <li onclick="loadDocumentContent('voltar')">
                <i class="fas fa-arrow-left"></i> Voltar
            </li>
        </ul>
    </div>
    <div id="chamados-content" class="main-content"></div>
</div>

<script src="{{ url_for('static', filename='js/franqueado.js') }}"></script>
{% endblock %}