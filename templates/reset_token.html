<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redefinir <PERSON></title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>

<body class="login">
    <div class="header-image">
        <img src="https://brazilhealth.com.br/wp-content/uploads/2020/03/07_Prancheta-1-cópia-6-e1583518361332-1.png"
            alt="Logo BrazilHealth" style="margin-top: 70%;">
    </div>
    <div class="login-container">
        <form class="login-form" id="reset-password-form" action="/reset-password/{{ token }}" method="post">
            <h2>Redefinir Senha</h2>
            <label for="new-password">Nova Senha:</label>
            <div class="input-group-register">
                <input type="password" id="new-password" name="new-password" required>
                <span id="togglePassword" class="fa fa-eye input-group-register-addon"></span>
            </div>
            <div id="senha-reqs" style="margin-top: 10px;">
                <p id="req-length" style="color: #555;">Deve conter 6 a 64 caracteres</p>
                <p id="req-letter" style="color: #555;">Deve conter ao menos uma letra</p>
                <p id="req-number" style="color: #555;">Deve conter ao menos um número</p>
                <p id="req-special" style="color: #555;">Deve conter ao menos um caracter especial (!@#$%&*)</p>
                <br>
            </div>
            <label for="confirm-password">Confirme a Senha:</label>
            <div class="input-group-register">
                <input type="password" id="confirm-password" name="confirm-password" required>
                <span id="toggleConfirmPassword" class="fa fa-eye input-group-register-addon"></span>
            </div>
            <div id="error-message" style="color: red; margin-top: 10px;"></div>
            <div class="submit-group-register">
                <button type="submit" class="change-password-btn">Redefinir Senha</button>
            </div>
        </form>
    </div>

    <p class="login-link">Lembrou sua senha? <a href="{{ url_for('index') }}">Faça login</a></p>

    <script src="{{ url_for('static', filename='js/reset_pswd.js') }}"></script>
</body>
</html>
