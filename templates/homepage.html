{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}homepage data-informativos-url="{{ url_for('informativos') }}"{% endblock %}
{% block extra_styles %}
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.11/core/main.min.css' rel='stylesheet' />
{% endblock %}
{% block content %}
<div class="aniversariantes-container">
    <h3>Aniversariantes do Mês</h3>
    <div id="calendar-aniversariantes"></div>
</div>

{% if session.get('setor_id') not in [9, 10] %}
<div class="informativos-container">
    <div class="inform_add">
        {% if session.get('user_type') in [1, 7] %}
        <a href="{{ url_for('novo_informativo') }}" class="btn btn-inform">
            <i class="fas fa-plus"></i>
        </a>
        {% endif %}
    </div>
    <div class="rotulo_inform">Informativos BRH:</div>
    <div class="informativos-carousel">
        <div class="informativos-carousel-inner">
            {% for post in data %}
            <div class="informativos-carousel-item">
                <div class="informativo-card">
                    {% if session.get('user_type') in [1, 7] %}
                    <div class="delete-btn" data-id="{{ post.id }}" onclick="confirmDelete(this);">
                        <i class="bi bi-x-circle-fill"></i>
                    </div>
                    {% endif %}
                    <img src="{{ post.url_imagem }}" alt="{{ post.titulo }}">
                    <div class="informativo-content">
                        <h2 class="informativo-title">{{ post.titulo }}</h2>
                        <p class="informativo-excerpt">{{ post.conteudo|truncate(150, True)|safe }}</p>
                    </div>
                    <div class="informativo-footer">
                        <a href="{{ url_for('detalhe_informativo', id=post.id) }}" class="saiba-mais-btn">Saiba mais</a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <button class="informativos-carousel-control-prev" onclick="moveInformativosCarousel(-1)">&#10094;</button>
        <button class="informativos-carousel-control-next" onclick="moveInformativosCarousel(1)">&#10095;</button>
    </div>
</div>
{% endif %}
<!--
<div class="insta-content">
    <div class="rotulo_insta"> Nosso Instagram tem o plano certo para você. Confira!</div>
    <div id="instagramCarousel" class="carousel slide" data-bs-ride="carousel">
        <div class="carousel-inner">
            {% for post in instagram_posts %}
            <div class="carousel-item{{ ' active' if loop.index == 1 else '' }}">
                <a href="{{ post.permalink }}" target="_blank">
                    {% if post.media_type == 'VIDEO' %}
                    <div class="video-container">
                        <video class="d-block w-100" autoplay muted loop>
                            <source src="{{ post.media_url }}" type="video/mp4">
                            Seu navegador não suporta vídeos.
                        </video>
                        <img src="/static/images/video.png" class="video-icon" style="width: 25px; top: 23%;">
                    </div>
                    {% else %}
                    <img src="{{ post.media_url }}" class="d-block w-100" alt="{{ post.caption }}">
                    {% endif %}
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
-->

{% if show_modal %}
<div id="modal-atualizacao-homepage" class="modal-atualizacao-homepage" style="display:block;">
    <div class="modal-content-atualizacao-homepage">
        <h2>Atualize seu setor</h2>
        <form id="form-setor" method="POST" action="{{ url_for('atualizar_setor_route') }}">
            <label for="setor">Selecione seu setor:</label>
            <select id="setor" name="setor_id" required>
                {% for setor in setores %}
                <option value="{{ setor.id }}">{{ setor.nome }}</option>
                {% endfor %}
            </select>
            <button type="submit" class="btn-atualizar-modal">Atualizar</button>
        </form>
    </div>
</div>
{% endif %}

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const modal = document.getElementById('modal-atualizacao-homepage');

        // Renderiza o calendário
        renderCalendar();

        // Verifica o estado do modal na sessão e exibe-o se necessário
        if (modal && sessionStorage.getItem('modalClosed') !== 'true') {
            modal.style.display = 'block';
            setupModalForm();
        }

        function renderCalendar() {
            const calendarEl = document.getElementById('calendar-aniversariantes');
            if (!calendarEl) {
                console.error('Elemento calendar-aniversariantes não encontrado.');
                return;
            }

            fetch('/api/aniversariantes')
                .then(response => response.json())
                .then(aniversariantes => {
                    console.log('Aniversariantes:', aniversariantes);

                    const events = aniversariantes.map(function (aniversariante) {
                        let date = new Date(aniversariante.data_nascimento);
                        let today = new Date();
                        date.setFullYear(today.getFullYear()); // Ajusta o ano para o ano atual

                        return {
                            title: aniversariante.nome,
                            start: date.toISOString().split('T')[0], // Formata a data como 'YYYY-MM-DD'
                            allDay: true,
                            extendedProps: { // Propriedades adicionais para usar no modal
                                nome: aniversariante.nome,
                                data_nascimento: aniversariante.data_nascimento,
                                profile_image_url: aniversariante.profile_image_url,
                                unidade_id: aniversariante.unidade_id
                            }
                        };
                    });

                    const calendar = new FullCalendar.Calendar(calendarEl, {
                        headerToolbar: {
                            left: 'prev,next today',
                            center: 'title',
                            right: 'dayGridMonth,timeGridWeek'
                        },
                        locale: 'pt-br',
                        navLinks: true,
                        selectable: true,
                        selectMirror: true,
                        editable: true,
                        dayMaxEvents: true,
                        events: events,
                        eventTimeFormat: {
                            hour: '2-digit',
                            minute: '2-digit',
                            meridiem: false
                        },
                        eventClick: function (info) {
                            openBirthdayModal(info.event.extendedProps);
                        }
                    });

                    calendar.render();
                })
                .catch(error => console.error('Erro ao carregar os aniversariantes:', error));
        }

        // Função para configurar o formulário no modal
        function setupModalForm() {
            const form = document.getElementById('form-setor');

            form.addEventListener('submit', function (event) {
                event.preventDefault(); // Impede o envio padrão do formulário

                const formData = new FormData(form);
                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                    .then(response => {
                        console.log('Response received:', response);
                        if (!response.ok) {
                            return response.json().then(data => {
                                console.error('Response not OK:', data);
                                throw new Error(data.message || 'Erro ao atualizar o setor');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Data received:', data);
                        if (data.success) {
                            // Atualiza o estado do modal para não exibi-lo novamente
                            sessionStorage.setItem('modalClosed', 'true');
                            closeModal();
                            alert(data.message); // Exibe a mensagem de sucesso
                            location.reload(); // Recarrega a página
                        } else {
                            throw new Error(data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Erro ao atualizar o setor:', error);
                        alert('Erro ao atualizar o setor: ' + error.message); // Exibe a mensagem de erro
                    });
            });

            function closeModal() {
                modal.style.display = 'none';
            }
        }

        // Função para abrir o modal com informações do aniversariante
        function openBirthdayModal(aniversariante) {
            // Cria o modal se ainda não existir
            if (!document.getElementById('birthdayModal')) {
                var modalHTML = `
                <div id="birthdayModal" class="modal">
                    <div class="modal-content">
                        <span class="close-button" onclick="closeBirthdayModal()">&times;</span>
                        <h2 id="modalNome"></h2>
                        <img id="modalImage" src="" alt="Foto do Aniversariante">
                        <p id="modalData"></p>
                        <p id="modalUnidade"></p>
                    </div>
                </div>
            `;
                document.body.insertAdjacentHTML('beforeend', modalHTML);
            }

            // Ajusta a data para exibir apenas dia e mês, com +1 dia
            let data = new Date(aniversariante.data_nascimento);
            data.setDate(data.getDate() + 1); // Adiciona um dia à data original
            let diaMes = ("0" + data.getDate()).slice(-2) + "/" + ("0" + (data.getMonth() + 1)).slice(-2);

            // Atualiza o conteúdo do modal
            document.getElementById('modalNome').innerText = aniversariante.nome;
            document.getElementById('modalData').innerText = diaMes; // Exibe apenas o dia e mês
            document.getElementById('modalUnidade').innerText = unidadeMap[aniversariante.unidade_id];
            document.getElementById('modalImage').src = aniversariante.profile_image_url || 'placeholder.jpg';

            // Exibe o modal
            var modal = document.getElementById('birthdayModal');
            modal.style.display = 'block';
        }

        // Função para fechar o modal
        window.closeBirthdayModal = function () {
            var modal = document.getElementById('birthdayModal');
            modal.style.display = 'none';
        }

        // Mapeamento das unidades
        const unidadeMap = {
            1: "Matriz",
            2: "Asche Saúde",
            3: "BRH Solution",
            4: "Confiance BRH",
            5: "Luanca BRH",
            6: "Yolo BRH",
            7: "Brazil Call",
            8: "BRH Corporate",
            9: "BRH Rio de Janeiro",
            10: "BRH Campinas"
        };
    });
    // Adicione o CSS para estilizar o modal
    var style = document.createElement('style');
    style.innerHTML = `
    .modal {
        display: none;
        position: fixed;
        z-index: 1;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgb(0,0,0);
        background-color: rgba(0,0,0,0.4);
        padding-top: 60px;
    }

    .modal-content {
        background-color: #fefefe;
        margin: 5% auto;
        padding: 20px;
        border: 1px solid #888;
        width: 80%;
        text-align: center;
        align-items: center;
        gap: 1rem;
    }

    img#modalImage {
    border: 1px solid #000;
    width: 150px;
    height: 200px;
    object-fit: cover;
    }

    .close-button {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
    }

    .close-button:hover,
    .close-button:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }
`;
    document.head.appendChild(style);
</script>
<script src="https://code.jquery.com/jquery-3.x.x.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/home.js') }}"></script>
<script src="{{ url_for('static', filename='js/index.global.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/core/locales-all.global.min.js') }}"></script>
{% endblock %}