<!DOCTYPE html>
<html lang="pt">

<head>
    <meta charset="UTF-8">
    <title>Gestão de Dados Bancários</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/css/adminlte.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <!-- Select2 CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
</head>

<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <div class="content-wrapper">
            <div class="container">
                <!-- Header -->
                <section class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1>Gestão de Dados Bancários</h1>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Main Content -->
                <section class="content">
                    <div class="container-fluid">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Gerenciar Dados Bancários</h3>
                            </div>
                            <div class="card-body">
                                <ul class="nav nav-tabs" id="tabDadosBancarios" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" id="tab-cadastrar-dados" data-toggle="tab"
                                            href="#cadastrar-dados" role="tab" aria-controls="cadastrar-dados"
                                            aria-selected="true">
                                            <i class="fas fa-plus-circle"></i> Adicionar Dados Bancários
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="tab-listar-dados" data-toggle="tab" href="#listar-dados"
                                            role="tab" aria-controls="listar-dados" aria-selected="false">
                                            <i class="fas fa-list"></i> Dados Bancários Cadastrados
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content" id="tabContentDadosBancarios">

                                    <!-- ABA CADASTRAR DADOS BANCÁRIOS -->
                                    <div class="tab-pane fade show active" id="cadastrar-dados" role="tabpanel"
                                        aria-labelledby="tab-cadastrar-dados">
                                        <div class="card mt-3">
                                            <div class="card-header bg-primary text-white">
                                                <h3 class="card-title">Cadastrar Dados Bancários</h3>
                                            </div>
                                            <form id="form-dados-bancarios">
                                                <div class="card-body">
                                                    <!-- ROW 1 -->
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <label for="nomeBanco">Nome do Banco</label>
                                                            <input type="text" class="form-control" id="nomeBanco"
                                                                name="nome_banco" placeholder="Digite o nome do banco"
                                                                required>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="numeroBanco">Número do Banco</label>
                                                            <input type="text" class="form-control" id="numeroBanco"
                                                                name="numero_banco"
                                                                placeholder="Digite o número do banco" required>
                                                        </div>
                                                    </div>

                                                    <!-- ROW 2 -->
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <label for="agenciaBanco">Agência</label>
                                                            <input type="text" class="form-control" id="agenciaBanco"
                                                                name="agencia_banco"
                                                                placeholder="Digite o número da agência" required>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="digitoAgencia">Dígito da Agência</label>
                                                            <input type="text" class="form-control" id="digitoAgencia"
                                                                name="digito_agencia"
                                                                placeholder="Digite o dígito da agência" required>
                                                        </div>
                                                    </div>

                                                    <!-- ROW 3 -->
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <label for="tipoConta">Tipo de Conta</label>
                                                            <select class="form-control select2" id="tipoConta"
                                                                name="tipo_conta" required>
                                                                <option value="" disabled selected>Selecione o tipo de
                                                                    conta</option>
                                                                <option value="corrente">Conta Corrente</option>
                                                                <option value="poupanca">Conta Poupança</option>
                                                                <option value="salario">Conta Salário</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="numeroConta">Número da Conta</label>
                                                            <input type="text" class="form-control" id="numeroConta"
                                                                name="numero_conta"
                                                                placeholder="Digite o número da conta" required>
                                                        </div>
                                                    </div>

                                                    <!-- ROW 4 -->
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <label for="digitoConta">Dígito da Conta</label>
                                                            <input type="text" class="form-control" id="digitoConta"
                                                                name="digito_conta"
                                                                placeholder="Digite o dígito da conta" required>
                                                        </div>

                                                        <!-- SELECIONAR O TIPO DE BENEFICIÁRIO (Fornec. ou Func.) -->
                                                        <div class="col-md-6">
                                                            <label for="tipoBeneficiario">Tipo de beneficiário</label>
                                                            <select id="tipoBeneficiario" class="form-control select2"
                                                                name="tipo_beneficiario" required>
                                                                <option value="" disabled selected>Selecione</option>
                                                                <option value="fornecedor">Fornecedor</option>
                                                                <option value="funcionario">Funcionário</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <!-- ROW 5: FORNECEDOR E FUNCIONÁRIO, EXIBIDOS COND. -->
                                                    <div class="row">
                                                        <div class="col-md-6" id="containerFornecedor"
                                                            style="display: none;">
                                                            <label for="fornecedorSelect">Fornecedor</label>
                                                            <select class="form-control select2" id="fornecedorSelect"
                                                                name="fornecedor_id">
                                                                <option value="" disabled selected>Selecione o
                                                                    fornecedor</option>
                                                            </select>
                                                        </div>

                                                        <!-- CONTAINER FUNCIONÁRIO (inicialmente oculto) -->
                                                        <div class="col-md-6" id="containerFuncionario"
                                                            style="display: none;">
                                                            <label for="funcionarioSelect">Funcionário</label>
                                                            <select class="form-control select2" id="funcionarioSelect"
                                                                name="funcionario_id">
                                                                <option value="" disabled selected>Selecione o
                                                                    funcionário</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div><!-- card-body -->

                                                <div class="card-footer text-right">
                                                    <button type="submit" class="btn btn-primary"
                                                        id="botaoSalvarDadosBancarios">Salvar</button>
                                                    <button type="reset" class="btn btn-secondary">Limpar</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div><!-- fim aba cadastrar -->

                                    <!-- ABA LISTAR DADOS BANCÁRIOS -->
                                    <div class="tab-pane fade" id="listar-dados" role="tabpanel"
                                        aria-labelledby="tab-listar-dados">
                                        <div class="card mt-3">
                                            <div class="card-header bg-secondary text-white">
                                                <h3 class="card-title">Dados Bancários Cadastrados</h3>
                                            </div>
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th>ID</th>
                                                                <th>Banco</th>
                                                                <th>Número do Banco</th>
                                                                <th>Agência</th>
                                                                <th>Dígito da Agência</th>
                                                                <th>Conta</th>
                                                                <th>Dígito da Conta</th>
                                                                <th>Tipo</th>
                                                                <th>Fornecedor/Funcionario</th>
                                                                <th>Ações</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="dados-bancarios-list">
                                                            <!-- Dados dinâmicos aqui -->
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div><!-- fim aba listar -->
                                </div><!-- tab-content -->
                            </div><!-- card-body -->
                        </div><!-- card -->
                    </div><!-- container-fluid -->
                </section>
            </div><!-- container -->
        </div><!-- content-wrapper -->
    </div><!-- wrapper -->

    <!-- JQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE JS -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/js/adminlte.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

    <!-- SEU ARQUIVO DE JS (ou inline) -->
    <script src="/static/js/contas_home.js"></script>

</body>

</html>