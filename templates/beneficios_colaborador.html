{% block content %}
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Políticas de Benefícios BRH</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body class="beneficios-page">
    <div class="beneficios-container">
        <h1 class="beneficios-header">Políticas de Benefícios BRH</h1>

        <h2 class="beneficios-section-title">Benefícios <PERSON>gais</h2>
        <div class="beneficios-cards">
            <!-- Benef<PERSON><PERSON><PERSON>gai<PERSON> -->
            <div class="beneficios-card">
                <div class="beneficios-card-icon">
                    <i class="fas fa-umbrella-beach fa-3x"></i>
                </div>
                <div class="beneficios-card-content">
                    <h3 class="beneficios-card-title">F<PERSON>rias</h3>
                    <p class="beneficios-card-description"><strong>Direito a 30 dias de descanso remunerado após 12 meses de trabalho.</strong></p>
                    <p class="beneficios-card-description"><strong>Divisão das Férias:</strong> Até três períodos (mínimo de 14 dias corridos para um dos períodos; os outros dois, no mínimo, 5 dias corridos).</p>
                    <p class="beneficios-card-description"><strong>Aviso de Férias:</strong> Comunicado com 30 dias de antecedência.</p>
                    <p class="beneficios-card-description"><strong>Pagamento:</strong> Até dois dias antes do início do período de gozo.</p>
                    <p class="beneficios-card-description"><strong>Início:</strong> Não pode ser na quinta ou sexta-feira e nem anteceder feriados.</p>

                    <h4 class="beneficios-calculo-title">Previsão de Cálculo de Férias</h4>
                    <form id="calculoFeriasForm" action="{{ url_for('meus_beneficios') }}" method="POST">
                        <div class="beneficios-calculo-form-group">
                            <label for="salario">Salário Base:</label>
                            <input type="text" class="beneficios-calculo-input" id="salario" name="salario" required>
                        </div>
                        <div class="beneficios-calculo-form-group">
                            <label for="dias_ferias">Dias de Férias:</label>
                            <input type="number" class="beneficios-calculo-input" id="dias_ferias" name="dias_ferias" required>
                        </div>
                        <div class="beneficios-calculo-form-group">
                            <label for="data_admissao">Data de Admissão:</label>
                            <input type="date" class="beneficios-calculo-input" id="data_admissao" name="data_admissao" required>
                        </div>
                        <div class="beneficios-calculo-form-group">
                            <input type="checkbox" id="receber_13" name="receber_13">
                            <label for="receber_13">Receber adiantamento do 13º salário junto com as férias</label>
                        </div>
                        <div class="beneficios-calculo-buttons">
                            <button type="submit" name="calcular" class="beneficios-calculo-btn">Calcular</button>
                            <button type="button" name="limpar" class="beneficios-calculo-btn-reset" onclick="limparResultados()">Limpar</button>
                        </div>
                    </form>

                    <div id="resultado" class="resultado-beneficios mt-4">
                        {% if valor_ferias is defined %}
                        <h3>Resultado dos Benefícios</h3>
                        <p><strong>Valor das Férias:</strong> R$ {{ valor_ferias }}</p>
                        <p><strong>Valor Proporcional:</strong> R$ {{ valor_proporcional }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="beneficios-card">
                <div class="beneficios-card-icon">
                    <i class="fas fa-calendar-alt fa-3x"></i>
                </div>
                <div class="beneficios-card-content">
                    <h3 class="beneficios-card-title">13º Salário</h3>
                    <p class="beneficios-card-description"><strong>Primeira Parcela:</strong> Até 30 de novembro de cada ano. Corresponde a 50% do valor total do décimo terceiro salário. É calculada com base no salário bruto do mês anterior ao pagamento. Não há descontos de INSS ou IR na primeira parcela.</p>
                    <p class="beneficios-card-description"><strong>Segunda Parcela:</strong> Deve ser paga até o dia 20 de dezembro. Corresponde ao valor restante do décimo terceiro salário, descontando-se os encargos legais, como INSS e Imposto de Renda (quando aplicável). É calculada com base no salário bruto de dezembro.</p>
                    <p class="beneficios-card-description"><strong>Cálculo do Décimo Terceiro Salário:</strong> O cálculo do décimo terceiro salário é feito da seguinte maneira:</p>
                    <p class="beneficios-card-description"><strong>Salário Base:</strong> O valor do décimo terceiro é baseado no salário bruto do trabalhador.</p>
                    <p class="beneficios-card-description"><strong>Proporcionalidade:</strong> Caso o empregado não tenha trabalhado o ano todo, o décimo terceiro será proporcional aos meses trabalhados. Cada mês completo de trabalho equivale a 1/12 do salário. Se o empregado trabalhou por 15 dias ou mais em um mês, esse mês é considerado completo para fins de cálculo.</p>
                    <p class="beneficios-card-description"><strong>Exemplo de Cálculo:</strong></p>
                    <ul class="beneficios-card-description">
                        <li>Se um empregado tem um salário bruto de R$ 3.000,00 e trabalhou o ano inteiro, o cálculo será:</li>
                        <ul>
                            <li>Primeira Parcela: 50% de R$ 3.000,00 = R$ 1.500,00</li>
                            <li>Segunda Parcela: R$ 3.000,00 - INSS - IR (se aplicável)</li>
                        </ul>
                        <li>Se o mesmo empregado trabalhou apenas 8 meses no ano:</li>
                        <ul>
                            <li>Proporcionalidade: 8/12 de R$ 3.000,00 = R$ 2.000,00</li>
                            <li>Primeira Parcela: 50% de R$ 2.000,00 = R$ 1.000,00</li>
                            <li>Segunda Parcela: R$ 2.000,00 - INSS - IR (se aplicável)</li>
                        </ul>
                    </ul>
                    <p class="beneficios-card-description"><strong>Descontos Aplicáveis:</strong> Na segunda parcela, são descontados:</p>
                    <ul class="beneficios-card-description">
                        <li>INSS: Conforme a tabela de alíquotas vigente.</li>
                        <li>Imposto de Renda: Conforme a tabela progressiva do IRRF, se o valor da segunda parcela estiver dentro da faixa de tributação.</li>
                    </ul>
                    <p class="beneficios-card-description"><strong>Solicitação da Primeira Parcela:</strong> O empregado pode solicitar o adiantamento da primeira parcela do décimo terceiro salário por ocasião das férias. Para isso, deve fazer a solicitação por escrito ao empregador até o mês de janeiro do respectivo ano.</p>
                    <p class="beneficios-card-description">Essas são as regras gerais para a divisão do décimo terceiro salário conforme a CLT.</p>
                </div>
            </div>
        </div>

        <h2 class="premium-section-title">Benefícios Exclusivos BRH</h2>
        <div class="beneficios-cards">
            <div class="beneficios-card">
                <div class="beneficios-card-icon">
                    <i class="fas fa-hospital fa-3x"></i>
                </div>
                <div class="beneficios-card-content">
                    <div class="operadora-icon">
                        <img src="{{ url_for('static', filename='images/amil.png') }}" alt="Amil">
                        <h3 class="beneficios-card-title">Plano de Saúde</h3>
                    </div>
                    <p class="beneficios-card-description"><strong>Categoria:</strong> S60+ / Com Coparticipação.</p>
                    <p class="beneficios-card-description"><strong>Inclusão de dependente:</strong> Cônjuge e filhos, custos assumidos pelo colaborador.</p>
                    <p class="beneficios-card-description"><strong>Prazo para inclusão:</strong> Até 30 dias da contratação.</p>
                    <button id="toggleTableBtn" class="toggle-button">Ver detalhes da coparticipação</button>
                    <div id="coparticipation-table" style="display: none;">
                        <table class="coparticipation-table">
                            <thead>
                                <tr>
                                    <th>Serviço</th>
                                    <th>Percentual</th>
                                    <th>Valor Limite</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Consultas Eletivas</td>
                                    <td>30%</td>
                                    <td>R$ 15,00</td>
                                </tr>
                                <tr>
                                    <td>Consultas Pronto-Socorro</td>
                                    <td>30%</td>
                                    <td>R$ 27,00</td>
                                </tr>
                                <tr>
                                    <td>Exames Básicos</td>
                                    <td>30%</td>
                                    <td>R$ 2,40</td>
                                </tr>
                                <tr>
                                    <td>Exames Especiais</td>
                                    <td>30%</td>
                                    <td>R$ 45,00</td>
                                </tr>
                                <tr>
                                    <td>Procedimentos Básicos</td>
                                    <td>30%</td>
                                    <td>R$ 2,40</td>
                                </tr>
                                <tr>
                                    <td>Procedimentos Especiais</td>
                                    <td>30%</td>
                                    <td>R$ 45,00</td>
                                </tr>
                                <tr>
                                    <td>Nutrição</td>
                                    <td>30%</td>
                                    <td>R$ 15,00</td>
                                </tr>
                                <tr>
                                    <td>Fisioterapia</td>
                                    <td>40%</td>
                                    <td>--</td>
                                </tr>
                                <tr>
                                    <td>Psicoterapia</td>
                                    <td>40%</td>
                                    <td>--</td>
                                </tr>
                                <tr>
                                    <td>Fonoaudiologia</td>
                                    <td>40%</td>
                                    <td>--</td>
                                </tr>
                                <tr>
                                    <td>Radioterapia</td>
                                    <td>0%</td>
                                    <td>--</td>
                                </tr>
                                <tr>
                                    <td>Quimioterapia</td>
                                    <td>0%</td>
                                    <td>--</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="beneficios-card">
                <div class="beneficios-card-icon">
                    <i class="fas fa-tooth fa-3x"></i>
                </div>
                <div class="beneficios-card-content">
                    <div class="operadora-icon">
                        <img src="{{ url_for('static', filename='images/bradesco.png') }}" alt="Bradesco">
                        <h3 class="beneficios-card-title">Plano Odontológico</h3>
                    </div>
                    <p class="beneficios-card-description">Oferecemos o plano de saúde da operadora Bradesco, categoria <strong>TNDP</strong>, o custo da mensalidade é da empresa.</p>
                    <p class="beneficios-card-description"><strong>Inclusão de dependente:</strong> Podem ser inclusos cônjuge e filhos, sendo assim a mensalidade assumida integralmente pelo colaborador.</p>
                </div>
            </div>

            <div class="beneficios-card">
                <div class="beneficios-card-icon">
                    <i class="fas fa-utensils fa-3x"></i>
                </div>
                <div class="beneficios-card-content">
                    <div class="operadora-icon">
                        <img src="{{ url_for('static', filename='images/vr_multi_card.png') }}" alt="VR Multi">
                        <h3 class="beneficios-card-title">Vale-Refeição</h3>
                    </div>
                    <p class="beneficios-card-description">Auxílio refeição/alimentação da "VR Multi", o valor do benefício é de <strong>R$ 34,60</strong> por dia. Podendo ser usado como refeição e alimentação, creditado todo último dia útil do mês.</p>
                </div>
            </div>

            <div class="beneficios-card">
                <div class="beneficios-card-icon">
                    <i class="fas fa-bus fa-3x"></i>
                </div>
                <div class="beneficios-card-content">
                    <h3 class="beneficios-card-title">Vale-Transporte</h3>
                    <p class="beneficios-card-description">Auxílio para deslocamento entre a residência e o local de trabalho, valor conforme a necessidade, creditado nos bilhetes e cartões todo último dia útil do mês.</p>
                </div>
            </div>

            <div class="beneficios-card">
                <div class="beneficios-card-icon">
                    <i class="fas fa-life-ring fa-3x"></i>
                </div>
                <div class="beneficios-card-content">
                    <h3 class="beneficios-card-title">Seguro de Vida</h3>
                    <p class="beneficios-card-description">Cobertura financeira em caso de falecimento ou invalidez do funcionário, operadora Porto Seguro.</p>
                </div>
            </div>

            <div class="beneficios-card">
                <div class="beneficios-card-icon">
                    <i class="fas fa-box fa-3x"></i>
                </div>
                <div class="beneficios-card-content">
                    <h3 class="beneficios-card-title">Cesta Básica</h3>
                    <p class="beneficios-card-description">A cesta básica é um benefício incentivo, válido para colaboradores que registram o ponto. Cumprindo o horário estipulado em seu contrato de trabalho, sem atrasos no mês, incentivamos com o benefício da cesta básica física ou o valor de R$ 150,00.</p>
                </div>
            </div>

            <div class="beneficios-card">
                <div class="beneficios-card-icon">
                    <i class="fas fa-dumbbell fa-3x"></i>
                </div>
                <div class="beneficios-card-content">
                    <h3 class="beneficios-card-title">Total Pass</h3>
                    <p class="beneficios-card-description">É o benefício corporativo descomplicado que oferece academias incríveis para você. O benefício das academias e estúdios é válido após 30 dias de admissão. O cadastro é feito no link <a href="https://totalpass.com/cadastro/brhbeneficios" target="_blank">Total Pass</a>, onde você pode escolher o plano desejado.</p>
                </div>
            </div>

            <div class="beneficios-card">
                <div class="beneficios-card-icon">
                    <i class="fas fa-gift fa-3x"></i>
                </div>
                <div class="beneficios-card-content">
                    <h3 class="beneficios-card-title">Voucher Aniversário</h3>
                    <p class="beneficios-card-description">No seu aniversário, você ganha um voucher no valor de R$ 150,00, para usar como quiser!</p>
                </div>
            </div>

            <div class="beneficios-card">
                <div class="beneficios-card-icon">
                    <i class="fas fa-gift fa-3x"></i>
                </div>
                <div class="beneficios-card-content">
                    <h3 class="beneficios-card-title">Voucher Aniversário de Empresa</h3>
                    <p class="beneficios-card-description">No seu aniversário de empresa, você ganha um voucher no valor de R$ 150,00, para usar como quiser!</p>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/beneficios_colaborador.js') }}"></script>
</body>
</html>
{% endblock %}