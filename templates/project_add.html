{% extends "projects_base.html" %}

{% block title %}Adicionar Projeto{% endblock %}

{% block header %}
<div class="row mb-2">
    <div class="col-sm-6">
        <h1 class="m-0">Adicionar Projeto</h1>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Formulário de Adicionar Projeto -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Detalhes do Projeto</h3>
    </div>
    <div class="card-body">
        <form action="{{ url_for('project_add') }}" method="POST" enctype="multipart/form-data">
            <div class="row">
                <!-- Nome do Projeto -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectName">Nome do Projeto</label>
                        <input type="text" class="form-control" id="projectName" name="projectName"
                            placeholder="Insira o nome do projeto" required>
                    </div>
                </div>

                <!-- Descrição do Projeto -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectDescription">Descrição</label>
                        <textarea class="form-control" id="projectDescription" name="projectDescription" rows="3"
                            placeholder="Insira a descrição do projeto" required></textarea>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Prioridade -->
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="projectPriority">Prioridade</label>
                        <select class="form-control" id="projectPriority" name="projectPriority" required>
                            <option value="">Selecione a prioridade</option>
                            <option value="alta">Alta</option>
                            <option value="media">Média</option>
                            <option value="baixa">Baixa</option>
                        </select>
                    </div>
                </div>

                <!-- Complexidade -->
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="projectComplexity">Complexidade</label>
                        <select class="form-control" id="projectComplexity" name="projectComplexity" required>
                            <option value="">Selecione a complexidade</option>
                            <option value="alta">Alta</option>
                            <option value="media">Média</option>
                            <option value="baixa">Baixa</option>
                        </select>
                    </div>
                </div>

                <!-- Responsável / Solicitante -->
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="responsavel" class="form-label">Responsável / Solicitante</label>
                        <input type="text" class="form-control" id="responsavel" name="responsavel"
                            value="{{ session.get('user_name', 'Usuário não identificado') }}" readonly>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Justificativa / Objetivo -->
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="projectJustification">Justificativa</label>
                        <textarea class="form-control" id="projectJustification" name="projectJustification" rows="2"
                            placeholder="Insira a justificativa ou objetivo"></textarea>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Data de Início -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectStartDate">Data de Início</label>
                        <input type="date" class="form-control" id="projectStartDate" name="projectStartDate" required>
                    </div>
                </div>

                <!-- Data de Entrega -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectDeadline">Data de Entrega</label>
                        <input type="date" class="form-control" id="projectDeadline" name="projectDeadline" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Status Inicial -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectStatus">Status</label>
                        <select class="form-control" id="projectStatus" name="projectStatus" required>
                            <option value="">Selecione o status</option>
                            <option value="planejamento">Planejamento</option>
                            <option value="backlog">Backlog/Pendências</option>
                            <option value="execucao">Em Execução</option>
                            <option value="concluido">Concluído</option>
                        </select>
                    </div>
                </div>

                <!-- Upload de Arquivos -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="projectFiles">Anexos (opcional)</label>
                        <input type="file" class="form-control" id="projectFiles" name="projectFiles" multiple>
                    </div>
                </div>
            </div>

            <!-- Botão de Envio -->
            <button type="submit" class="btn btn-primary">Criar Projeto</button>
        </form>
    </div>
</div>
{% endblock %}