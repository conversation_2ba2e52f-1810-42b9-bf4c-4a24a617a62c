{% extends 'base.html' %}

{% block title %}Gestão de Ramais | BrazilHealth{% endblock %}

{% block content %}
<div class="gestao-ramais">
    <div class="header-gestao-ramais">
        <h1>Gestão de Ramais</h1>
    </div>
    <div class="gestao-ramais-container">
        <!-- Contatos Internos Brazil Health -->
        <div class="card-ramais" onclick="openPopup('brasil-health-popup')">
            <h2>Brazil Health - Matriz</h2>
        </div>
        <div id="brasil-health-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('brasil-health-popup')">&times;</span>
                <h2>Brazil Health - Matriz</h2>
                <table class="gestao-ramais-table">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Cargo</th>
                            <th>Email</th>
                            <th>Ramal</th>
                            <th>Ativo</th>
                            <th>Contato Corporativo</th>
                            <th>Andar</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><PERSON><PERSON></td>
                            <td>CEO</td>
                            <td><EMAIL></td>
                            <td>1016</td>
                            <td>SIM</td>
                            <td>(11)99242-0080</td>
                            <td>8º andar</td>
                        </tr>
                        <!-- Adicione todos os contatos aqui... -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Luanca BRH Consultoria e Beneficios e Corretora de Seguros LTDA -->
        <div class="card-ramais" onclick="openPopup('luanca-brh-popup')">
            <h2>Luanca BRH</h2>
        </div>
        <div id="luanca-brh-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('luanca-brh-popup')">&times;</span>
                <h2>Luanca BRH Consultoria e Beneficios e Corretora de Seguros LTDA</h2>
                <table class="gestao-ramais-table">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Cargo</th>
                            <th>Email</th>
                            <th>Telefone</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Luiz Tenerelli Junior</td>
                            <td>Sócio</td>
                            <td><EMAIL></td>
                            <td>2337-2666 / 99811-6073</td>
                        </tr>
                        <!-- Mais contatos aqui -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Confiance BRH -->
        <div class="card-ramais" onclick="openPopup('confiance-brh-popup')">
            <h2>Confiance BRH</h2>
        </div>
        <div id="confiance-brh-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('confiance-brh-popup')">&times;</span>
                <h2>Confiance BRH</h2>
                <table class="gestao-ramais-table">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Cargo</th>
                            <th>Email</th>
                            <th>Telefone</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Marcio Florencio Rocha</td>
                            <td>Sócio</td>
                            <td><EMAIL></td>
                            <td>11-2337-2666 / 97254-2757</td>
                        </tr>
                        <!-- Mais contatos aqui -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Brazil Call Business Intelligence LTDA -->
        <div class="card-ramais" onclick="openPopup('brazil-call-popup')">
            <h2>Brazil Call </h2>
        </div>
        <div id="brazil-call-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('brazil-call-popup')">&times;</span>
                <h2>Brazil Call Business Intelligence LTDA</h2>
                <table class="gestao-ramais-table">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Cargo</th>
                            <th>Email</th>
                            <th>Telefone</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Marcia Vivian Magalhães</td>
                            <td>Gerente Comercial / Sócia</td>
                            <td><EMAIL></td>
                            <td>11 96060-1076</td>
                        </tr>
                        <!-- Mais contatos aqui -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- BRH Solution Saúde Assessoria e Corretora de Seguros LTDA -->
        <div class="card-ramais" onclick="openPopup('brh-solution-popup')">
            <h2>BRH Solution</h2>
        </div>
        <div id="brh-solution-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('brh-solution-popup')">&times;</span>
                <h2>BRH Solution Saúde Assessoria e Corretora de Seguros LTDA</h2>
                <table class="gestao-ramais-table">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Cargo</th>
                            <th>Email</th>
                            <th>Telefone</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Evaristo Moura</td>
                            <td>Sócio</td>
                            <td><EMAIL></td>
                            <td>97190-3837</td>
                        </tr>
                        <!-- Mais contatos aqui -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Asche BRH Santos -->
        <div class="card-ramais" onclick="openPopup('asche-brh-santos-popup')">
            <h2>Asche BRH - Santos</h2>
        </div>
        <div id="asche-brh-santos-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('asche-brh-santos-popup')">&times;</span>
                <h2>Asche BRH Santos</h2>
                <table class="gestao-ramais-table">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Cargo</th>
                            <th>Email</th>
                            <th>Telefone</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Sylmara Cavalheiro Andre</td>
                            <td>Sócia</td>
                            <td><EMAIL></td>
                            <td>(13) 3202-2772 / (13) 99760-4720</td>
                        </tr>
                        <!-- Mais contatos aqui -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Asche BRH Praia Grande -->
        <div class="card-ramais" onclick="openPopup('asche-brh-praia-grande-popup')">
            <h2>Asche BRH - Praia Grande</h2>
        </div>
        <div id="asche-brh-praia-grande-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('asche-brh-praia-grande-popup')">&times;</span>
                <h2>Asche BRH Praia Grande</h2>
                <table class="gestao-ramais-table">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Cargo</th>
                            <th>Email</th>
                            <th>Telefone</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Marcello Ignacio Puerta</td>
                            <td></td>
                            <td><EMAIL></td>
                            <td></td>
                        </tr>
                        <!-- Mais contatos aqui -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Asche BRH Guaruja -->
        <div class="card-ramais" onclick="openPopup('asche-brh-guaruja-popup')">
            <h2>Asche BRH - Guaruja</h2>
        </div>
        <div id="asche-brh-guaruja-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('asche-brh-guaruja-popup')">&times;</span>
                <h2>Asche BRH Guaruja</h2>
                <table class="gestao-ramais-table">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Cargo</th>
                            <th>Email</th>
                            <th>Telefone</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Larissa Feitoza dos Santos</td>
                            <td>ADM</td>
                            <td><EMAIL></td>
                            <td>13 3202-2789</td>
                        </tr>
                        <!-- Mais contatos aqui -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function openPopup(popupId) {
        document.getElementById(popupId).style.display = 'block';
    }

    function closePopup(popupId) {
        document.getElementById(popupId).style.display = 'none';
    }
</script>
{% endblock %}
