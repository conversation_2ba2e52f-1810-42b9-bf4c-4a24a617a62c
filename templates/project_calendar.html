{% extends "projects_base.html" %}

{% block title %}Calendario de Tarefas{% endblock %}

{% block content %}
<div class="container my-4">
  <div class="row">
    <div class="col-md-12">
      <h1>Calendario de Tarefas</h1>
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Calendário</h3>
        </div>
        <div class="card-body">
          <div id="calendar"></div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal de exemplo para novo evento -->
<div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form id="eventForm">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="eventModalLabel">Novo Evento</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label for="eventTitle" class="form-label">Título</label>
            <input type="text" class="form-control" id="eventTitle" required>
          </div>
          <div class="mb-3">
            <label for="eventStart" class="form-label">Início</label>
            <input type="text" class="form-control" id="eventStart" readonly>
          </div>
          <div class="mb-3">
            <label for="eventEnd" class="form-label">Fim</label>
            <input type="text" class="form-control" id="eventEnd" readonly>
          </div>
          <div class="form-check mb-3">
            <input type="checkbox" class="form-check-input" id="eventAllDay">
            <label class="form-check-label" for="eventAllDay">Dia Inteiro</label>
          </div>
          <div class="mb-3">
            <label for="eventDescription" class="form-label">Descrição</label>
            <textarea class="form-control" id="eventDescription"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-primary">Salvar</button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
        </div>
      </div>
    </form>
  </div>
</div>
{% endblock %}

{% block scripts %}
  <!-- FullCalendar JS (versão global com suporte a módulos) -->
  <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
  <!-- Nosso script customizado para o calendário -->
  <script src="{{ url_for('static', filename='js/project_calendar.js') }}"></script>
{% endblock %}
