{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}rh{% endblock %}
{% block content %}
<div class="brazil-container">
    <div id="videoOverlay" style="display: none;">
        <div id="videoContainer">
            <span id="closeButton">&times;</span>
            <iframe id="videoIframe" width="960" height="540" src="https://www.youtube.com/embed/Jk-oxjhgSfs"
                title="YouTube video player" frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
        </div>
    </div>
    <div id="rh-float-box">
        <div id="brazil-main-content">
            <div class="bloco-conteiner-dois">
                <div id="missao-container">
                    <h2>MISSÃO</h2>
                    <div class="text-container">
                        Prestar soluções inteligentes em benefícios saúde, odonto, pet, alimentação e vida. 
                        Gerar serviços otimizados a cada cliente e corretor, buscando suprir as necessidades com eficácia, qualidade, gestão e prevenção. 
                        Além disso, oferecemos serviços para garantir a satisfação total de nossos clientes. 
                        Foco em atendimento, pós-vendas e relacionamento, buscando conquistar satisfação e mercado com nossos serviços.
                    </div>
                </div>
                <div id="visao-container">
                    <h2>VISÃO</h2>
                    <div class="text-container">
                        Ser reconhecida como a melhor consultoria e corretora no segmento saúde,
                        odonto e vida no país para nossos parceiros e clientes, através de serviços diferenciados,
                        inteligentes e competentes. Buscando superar todas as expectativas.
                    </div>
                </div>
                <div id="valores-container">
                    <h2>VALORES</h2>
                    <div class="text-container">
                        Inovação | Ética | Comprometimento | Relacionamento | Respeito | Transparência.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% block extra_scripts %}
<script>
    let closeTimer;

    // Função para abrir o vídeo após 2 segundos
    function openVideoAfterDelay() {
        setTimeout(function () {
            document.getElementById('videoOverlay').style.display = 'flex';
            const videoIframe = document.getElementById('videoIframe');
            const videoSrc = videoIframe.src;
            videoIframe.src = videoSrc + "?autoplay=1";

            // Fechará o vídeo após 2 minutos e 21 segundos (141 segundos)
            closeTimer = setTimeout(function () {
                closeVideo();
            }, 141000);
        }, 2000);
    }

    // Verifica se a página é /rh e abre o vídeo após o delay
    if (window.location.pathname === '/rh') {
        openVideoAfterDelay();
    }

    document.getElementById('closeButton').addEventListener('click', function () {
        closeVideo();
    });

    function closeVideo() {
        const videoOverlay = document.getElementById('videoOverlay');
        videoOverlay.style.display = 'none';
        const videoIframe = document.getElementById('videoIframe');
        const videoSrc = videoIframe.src;
        videoIframe.src = "";
        videoIframe.src = videoSrc.replace("?autoplay=1", "");

        clearTimeout(closeTimer);
    }
</script>
{% endblock %}
{% endblock %}
