{% extends "base.html" %}

{% block title %}Parâmetros | Intranet{% endblock %}

{% block body_class %}parametros{% endblock %}

{% block head_extra %}
<style>
    .parametros {
        font-family: 'Montserrat', sans-serif;
        background: #ffffff;
        color: #000;
        display: flex;
        min-height: 100vh;
    }

    .sidebar {
        width: 280px;
        background: #ffffff;
        border-right: 1px solid #e5e7eb;
        height: 100vh;
        position: relative;
        left: 0;
        top: 0;
        overflow-y: auto;
    }

    .main-content {
        flex: 1;
        background: #f9fafb;
        min-height: 100vh;
        padding: 1.5rem;
        overflow-y: auto;
    }

    .nav-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: #4f46e5;
        background: #eef2ff;
        text-decoration: none;
        border-radius: 0.5rem;
        transition: all 0.2s;
        margin-bottom: 0.5rem;
    }

    .nav-item:hover {
        background: #4f46e5;
        color: #ffffff;
    }

    .nav-item i {
        font-size: 1.25rem;
        margin-right: 0.75rem;
    }

    .nav-item.active {
        background: #4f46e5;
        color: #ffffff;
    }

    .content-wrapper {
        background: #ffffff;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
        padding: 1.5rem;
    }

    .message-center {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 400px;
        text-align: center;
        color: #6b7280;
    }
</style>
{% endblock %}

{% block content %}
<div class="parametros">
    <!-- Sidebar fixa -->
    <aside class="sidebar">
        <div class="px-6 py-4">
            <h3 class="text-lg font-bold text-gray-800 mb-6">Parâmetros do Sistema</h3>
            <nav>
                <button id="btnMetas" class="nav-item w-full text-left">
                    <i class="fa-solid fa-bullseye"></i>
                    <span class="font-medium">Metas</span>
                </button>
                <button id="btnVidasOperadoras" class="nav-item w-full text-left">
                    <i class="fa-solid fa-building"></i>
                    <span class="font-medium">Vidas Operadoras</span>
                </button>
                <button id="btnVidasAssistentes" class="nav-item w-full text-left">
                    <i class="fa-solid fa-users"></i>
                    <span class="font-medium">Vidas Assistentes</span>
                </button>
                <button id="btnDataGlobal" class="nav-item w-full text-left">
                    <i class="fa-solid fa-calendar-days"></i>
                    <span class="font-medium">Data Global</span>
                </button>
            </nav>
        </div>
    </aside>

    <!-- Conteúdo principal -->
    <main class="main-content">
        <div id="content-area">
            <div class="message-center">
                <div>
                    <i class="fa-solid fa-compass text-6xl text-gray-300 mb-4 block"></i>
                    <h3 class="text-xl font-medium">Selecione uma opção no menu</h3>
                    <p class="text-gray-400 mt-2">Escolha uma seção no menu lateral para começar</p>
                </div>
            </div>
        </div>
    </main>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/parametros.js') }}"></script>
{% endblock %}