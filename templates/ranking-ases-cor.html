{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}ranking{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
<style>
    .ranking-header th {
        position: relative;
    }

    .ranking-body td {
        border-right: 1px solid #fff;
    }

    .ranking-body td:last-child {
        border-right: none;
    }

    input[type="search"] {
        width: 400px;
        padding: 6px 10px;
        margin-bottom: 10px;
        border: 1px solid #ccc;
        /* Borda cinza clara */
        border-radius: 4px;
        /* <PERSON><PERSON><PERSON> arredondadas */
        box-shadow: none;
        /* Remove sombra */
        outline: none;
        /* Remove o contorno */
        transition: border-color 0.3s, box-shadow 0.3s;
        /* Transição suave para foco */
        margin-left: 1rem;
    }

    input[type="search"]:focus {
        border-color: #007bff;
        /* Azul claro no foco */
        box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
        /* Sombra azul clara */
    }

    select#filter-canal {
        padding: 6px 10px;
        margin-bottom: 10px;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-shadow: none;
        outline: none;
        transition: border-color 0.3s, box-shadow 0.3s;
        margin-left: 1rem;
    }

    select#filter-canal:focus {
        border-color: #007bff;
        box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
    }

    .filter-container {
        display: flex;
        align-items: center;
    }

    .filter-container {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .filter-checkbox {
        margin-left: 10px;
    }

    .filter-dropdown {
        position: absolute;
        background-color: white;
        border: 1px solid #ccc;
        padding: 10px;
        z-index: 1000;
        top: 100%;
        right: 0;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .filter-dropdown label {
        display: block;
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="ranking-container">
    <h1 id="ranking-name" style="margin-bottom: 20px; margin-top: 20px;">Ranking ASES de Vendas - Corretores</h1>
    <div id="last-update" class="text-muted mb-3 font-weight-bold"></div>
    <div id="tables-container" class="d-flex flex-wrap justify-content-between"
        style="justify-content: center !important">
        <!-- As tabelas serão inseridas aqui dinamicamente -->
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.1.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.1.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.1.0/js/buttons.print.min.js"></script>
<script
    src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.15/js/bootstrap-multiselect.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/ranking-ases-cor.js') }}"></script>
{% endblock %}