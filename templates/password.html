{% extends "base.html" %}

{% block title %}Alterar Senha - Intranet | BrazilHealth{% endblock %}

{% block body_class %}pswd{% endblock %}

{% block content %}
    <div class="pswd-container">
        <div class="pswd-subcontainer">
            <div class="icone-alerta">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="mensagem-alerta">
                <p>Preencha os campos ao lado com a senha atual, nova senha e confirmação da nova senha, para atualizar
                    agora mesmo.</p>
                <p>Opte por uma senha forte, com letras, números e caracteres especiais.</p>
            </div>
        </div>
        <div class="form-troca-senha">
            <form id="form-change-pswd">
                <div class="grupo-form">
                    <label for="current-password">Digite a senha atual</label>
                    <input type="password" id="current-password" name="current-password" required>
                </div>
                <div class="grupo-form">
                    <label for="new-password">Digite a nova senha</label>
                    <input type="password" id="new-password" name="new-password" required>
                </div>
                <div id="senha-reqs" class="requisitos-senha">
                    <p id="req-length">Deve conter 6 a 64 caracteres</p>
                    <p id="req-letter">Deve conter ao menos uma letra</p>
                    <p id="req-number">Deve conter ao menos um número</p>
                    <p id="req-special">Deve conter ao menos um caracter especial (!@#$%&*)</p>
                </div>                
                <div class="grupo-form">
                    <label for="confirm-password">Confirme a nova senha</label>
                    <input type="password" id="confirm-password" name="confirm-password" required>
                </div>
                <div class="submit-pswd">
                    <button type="button" class="change-password-btn">TROCAR SENHA</button>
                </div>
            </form>
        </div>
    </div>
{% endblock %}
