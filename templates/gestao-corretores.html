{% extends "base.html" %}

{% block title %}Intranet | Gestão de Corretores{% endblock %}

{% block body_class %}gestaoCorretores{% endblock %}

{% block content %}

<div class="board">
    <div class="limiter-board">
        <!-- Indicadores -->
        <div class="dashboard">
            <div class="kpi">
                <h3>Total de Corretores</h3>
                <p id="total-corretores">0</p> <!-- Valor será atualizado via JS -->
            </div>
            <div class="kpi">
                <h3>Total de Contratos LGPD</h3>
                <p id="lgpd-assinados">Assinados: 0</p> <!-- Atualizado via JS -->
                <p id="lgpd-nao-assinados">Não Assinados: 0</p> <!-- Atualizado via JS -->
            </div>
        </div>

        <!-- Indicador de Carregamento -->
        <div id="loading" style="display: none;">
            <p>Carregando corretores...</p>
        </div>

        <div>
            <input type="text" id="filter-nome" placeholder="Nome do corretor">
            <input type="text" id="filter-email" placeholder="Email">
            <input type="text" id="filter-status" placeholder="Status do Contrato">
            <button id="search-button">Buscar</button>
        </div>        

        <!-- Tabela de Corretores -->
        <div class="corretores-table">
            <table>
                <thead>
                    <tr>
                        <th id="nome-col" class="column-nome">Nome <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="email-col" class="column-email">Email <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="status-contrato-col" class="column-status-contrato">Status Contrato <button
                                class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th id="data-inclusao-col" class="column-data-inclusao">Data Inclusão <button
                                class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th id="termo-lgpd-col" class="column-termo-lgpd">Termo LGPD <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="ultima-producao-col" class="column-ultima-producao">Última Produção <button
                                class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th id="supervisor-col" class="column-supervisor">Supervisor <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="producao-col" class="column-producao">Produção <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="comissao-col" class="column-comissao">Comissão <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="pessoa-col" class="column-pessoa">Pessoa <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="ativo-col" class="column-ativo">Ativo <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <!-- A coluna "Ações" não precisa de filtro -->
                        <th id="acoes-col" class="column-acoes">Ações</th>
                    </tr>
                </thead>

                <tbody id="corretores-list">
                    <!-- A tabela será preenchida dinamicamente via JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- Paginação -->
        <div class="pagination">
            <button id="previous-page" onclick="navigatePage(pagina_atual - 1)" disabled>Página Anterior</button>
            <span id="pagination-info">Página 1 de 1</span>
            <button id="next-page" onclick="navigatePage(pagina_atual + 1)" disabled>Próxima Página</button>
        </div>

        <!-- Modal para detalhes do corretor -->
        <div id="modal" class="modal" style="display: none;">
            <div class="modal-content">
                <span class="close-button" onclick="closeModal()">&times;</span>
                <h2>Detalhes do Corretor</h2>
                <div id="error-message" style="display:none; color:red;">
                    Ocorreu um erro ao carregar os dados. Por favor, tente novamente mais tarde.
                </div>

                <div class="tab">
                    <button class="tablinks" onclick="openTab(event, 'Geral')">Geral</button>
                    <button class="tablinks" onclick="openTab(event, 'Enderecos')">Endereços</button>
                    <button class="tablinks" onclick="openTab(event, 'Telefones')">Telefones</button>
                    <button class="tablinks" onclick="openTab(event, 'Documentos')">Documentos</button>
                    <button class="tablinks" onclick="openTab(event, 'DadosBancarios')">Dados Bancários</button>
                </div>

                <div id="Geral" class="tabcontent">
                    <h3>Geral</h3>
                    <p><strong>Nome:</strong> <span id="modal-nome">Sem Informação Disponível</span></p>
                    <p><strong>Email:</strong> <span id="modal-email">Sem Informação Disponível</span></p>
                    <p><strong>Status Contrato:</strong> <span id="modal-status-contrato">Sem Informação Disponível</span></p>
                    <p><strong>Data Inclusão:</strong> <span id="modal-data-inclusao">Sem Informação Disponível</span></p>
                    <p><strong>LGPD:</strong> <span id="modal-lgpd">Situação Irregular</span></p>
                    <p><strong>Última Produção:</strong> <span id="modal-ultima-producao">Sem Informação Disponível</span></p>
                </div>                                

                <div id="Enderecos" class="tabcontent">
                    <h3>Endereços</h3>
                    <div id="enderecos-content"></div>
                </div>

                <div id="Telefones" class="tabcontent">
                    <h3>Telefones</h3>
                    <div id="telefones-content"></div>
                </div>

                <div id="Documentos" class="tabcontent">
                    <h3>Documentos</h3>
                    <div id="documentos-content"></div>
                </div>

                <div id="DadosBancarios" class="tabcontent">
                    <h3>Dados Bancários</h3>
                    <div id="contas-content"></div>
                </div>
            </div>
        </div>
        {% endblock %}

        {% block scripts %}
        <script src="{{ url_for('static', filename='js/gestao-corretores.js') }}"></script>
        {% endblock %}