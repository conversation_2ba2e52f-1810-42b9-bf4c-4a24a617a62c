{% extends "base.html" %}
{% block title %}Intranet | Sugestões Colaborador{% endblock %}
{% block body_class %}sugestoes-colaborador{% endblock %}
{% block content %}
<div id="sugestao-float-box" class="container mt-5">
    <h2>Envie sua sugestão de melhoria</h2>
    <form id="form-sugestao" method="POST" action="{{ url_for('submit_sugestao_colaborador') }}">
        <div class="form-group-basic">
            <label for="nome">Nome:</label>
            <input type="text" id="nome" name="nome" class="form-control" value="{{ usuario.nome }}" readonly placeholder="Digite seu nome">
            <label for="setor">Setor:</label>
            <input type="text" id="setor" name="setor" class="form-control" required placeholder="Digite seu setor">
        </div>
        <div class="form-group">
            <label for="email">E-mail:</label>
            <input type="email" id="email" name="email" class="form-control" value="{{ usuario.email }}" readonly placeholder="Digite seu e-mail">
        </div>
        <div class="form-group">
            <label for="assunto">Assunto:</label>
            <input type="text" id="assunto" name="assunto" class="form-control" required placeholder="Digite o assunto">
        </div>
        <div class="form-group">
            <label for="descricao">Descrição:</label>
            <textarea id="descricao" name="descricao" class="form-control" rows="5" required placeholder="Descreva sua sugestão"></textarea>
        </div>
        <button type="submit" class="btn btn-primary">Enviar Sugestão</button>
    </form>
</div>
{% block extra_styles %}
<style>
    #mainContent.sugestoes-page .pn-header,
    #mainContent.sugestoes-page .footer-container {
        display: none;
    }
</style>
{% endblock %}
{% block extra_scripts %}
<script src="{{ url_for('static', filename='js/sugestoes_colaborador.js') }}"></script>
{% endblock %}
{% endblock %}
