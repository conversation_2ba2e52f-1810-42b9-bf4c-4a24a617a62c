<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Abertura de Chamado</title>
    <!-- Importando CSS do AdminLTE e Bootstrap -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1.0/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.1/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>

<body class="ticket">
    <div class="container">
        <div class="row justify-content-center">
            <div class="container mt-5">
                <form id="main-form">
                    <!-- Setor -->
                    <div class="form-group">
                        <label for="setor_id">Setor</label>
                        <select class="form-control" name="setor_id" id="setor_id" required>
                            <option value="" disabled selected>Selecione o Setor</option>
                            {% for setor in setores %}
                            <option value="{{ setor.id }}">{{ setor.nome }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Assunto -->
                    <div class="form-group">
                        <label for="assunto">Assunto</label>
                        <input type="text" class="form-control" id="assunto" name="assunto"
                            placeholder="Digite o assunto" required>
                    </div>

                    <!-- Campos Condicionais -->
                    <div id="conditional-fields" style="display: none;">
                        <div class="form-group">
                            <label for="modalidade">Modalidade</label>
                            <select class="form-control" id="modalidade" name="modalidade">
                                <option value="" disabled selected>Selecione a Modalidade</option>
                                <option value="Adesão">Adesão</option>
                                <option value="PF">PF</option>
                                <option value="PF - Odontológico">PF - Odontológico</option>
                                <option value="PME">PME</option>
                                <option value="PME - Odontológico">PME - Odontológico</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="operadora">Operadora</label>
                            <select class="form-control" id="operadora" name="operadora">
                                <option value="" disabled selected>Selecione a Operadora</option>
                                <option value="Aacl">AACL</option>
                                <option value="Affix">Affix</option>
                                <option value="Alice">Alice</option>
                                <option value="Allcare">Allcare</option>
                                <option value="Ameplan">Ameplan</option>
                                <option value="Ampla">Ampla</option>
                                <option value="Ana Costa">Ana Costa</option>
                                <option value="Assim Saúde">Assim Saúde</option>
                                <option value="Ativia">Ativia</option>
                                <option value="Benevix">Benevix</option>
                                <option value="Biovida">Biovida</option>
                                <option value="Blue Med">Blue Med</option>
                                <option value="Blue">Blue</option>
                                <option value="Bradesco">Bradesco</option>
                                <option value="Care Plus">Care Plus</option>
                                <option value="Cemeru">Cemeru</option>
                                <option value="Cnu">CNU</option>
                                <option value="Corpe">Corpe</option>
                                <option value="Cruz Azul">Cruz Azul</option>
                                <option value="Dentalpar">Dentalpar</option>
                                <option value="Garantia Saúde">Garantia Saúde</option>
                                <option value="Go Care">Go Care</option>
                                <option value="Grupo Amil">Grupo Amil</option>
                                <option value="Grupo Notre Dame">Grupo Notre Dame</option>
                                <option value="Hbc">HBC</option>
                                <option value="Health Santaris">Health Santaris</option>
                                <option value="Hebrom">Hebrom</option>
                                <option value="Klini">Klini</option>
                                <option value="Leve Saude">Leve Saude</option>
                                <option value="Livri">Livri</option>
                                <option value="Mais Dental">Mais Dental</option>
                                <option value="Med Tour">Med Tour</option>
                                <option value="Medical Health">Medical Health</option>
                                <option value="Medsenior">Medsenior</option>
                                <option value="Metlife">Metlife</option>
                                <option value="Monthermonn">Monthermonn</option>
                                <option value="New Leader">New Leader</option>
                                <option value="Odontoprev">Odontoprev</option>
                                <option value="Omint / Kipp">Omint / Kipp</option>
                                <option value="Plansaude">Plansaúde</option>
                                <option value="Plena">Plena</option>
                                <option value="Plural Saúde">Plural Saúde</option>
                                <option value="Porto">Porto</option>
                                <option value="Porto Pet">Porto Pet</option>
                                <option value="Prevent Senior">Prevent Senior</option>
                                <option value="Qualicorp">Qualicorp</option>
                                <option value="Safe Life">Safe Life</option>
                                <option value="Sagrada Família">Sagrada Família</option>
                                <option value="Sami">Sami</option>
                                <option value="Santa Casa">Santa Casa</option>
                                <option value="Santa Helena">Santa Helena</option>
                                <option value="São Camilo">São Camilo</option>
                                <option value="São Cristóvão">São Cristóvão</option>
                                <option value="São Francisco">São Francisco</option>
                                <option value="São Miguel">São Miguel</option>
                                <option value="Saúde Beneficência">Saúde Beneficência</option>
                                <option value="Seguros Unimed">Seguros Unimed</option>
                                <option value="Sobam">Sobam</option>
                                <option value="Sul América">Sul América</option>
                                <option value="Supermed">Supermed</option>
                                <option value="Tecgroup">Tecgroup</option>
                                <option value="Total Medcare">Total Medcare</option>
                                <option value="Trasmontano">Trasmontano</option>
                                <option value="Única">Única</option>
                                <option value="Uniconsult">Uniconsult</option>
                                <option value="Unihosp">Unihosp</option>
                                <option value="Unimed Ferj">Unimed Ferj</option>
                                <option value="Unimeds Regionais">Unimeds - Regionais</option>
                                <option value="Uniodonto">Uniodonto</option>
                                <option value="Vera Cruz">Vera Cruz</option>
                                <option value="Você Clube">Você Clube</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="tipo-chamado">Tipo de Chamado</label>
                            <select class="form-control" id="tipo-chamado" name="tipo-chamado">
                                <option value="" disabled selected>Informe o Tipo do Chamado</option>
                                <option value="Emissão">Emissão</option>
                                <option value="Cadastro no SisWeb">Cadastro no SisWeb</option>
                                <option value="Outros">Outros</option>
                            </select>
                        </div>
                    </div>

                    <!-- Descrição -->
                    <div class="form-group">
                        <label for="descricao">Descrição</label>
                        <textarea class="form-control" id="descricao" name="descricao" rows="3"
                            placeholder="Digite a descrição"></textarea>
                    </div>

                    <!-- Botão de adicionar arquivos -->
                    <div class="form-group">
                        <button type="button" class="btn btn-secondary" id="add-file-btn">Anexar Arquivos</button>
                        <div class="info-icon">
                            ?
                            <div class="info-tooltip">O arquivo deve ser adicionado separadamente.</div>
                        </div>
                    </div>
                    <div class="file-list" id="file-list"></div>
                    <input type="hidden" id="uploaded_files" name="uploaded_files">

                    <!-- Botão -->
                    <div class="form-group" style="
                        padding: .75rem 1.25rem;
                        background-color: rgba(0, 0, 0, .03);
                        border-top: 1px solid rgba(0, 0, 0, .125);
                        margin-bottom: -9px;
                    ">
                        <button type="submit" class="btn btn-primary" id="main-button">Abrir Chamado</button>
                    </div>
                </form>

                <div id="next-form" style="display: none;">
                    <div id="pf-forms">
                        <div class="form-group">
                            <label>Possui Multinotas?</label>
                            <select class="form-control" id="multinotas-pf" required>
                                <option value="false">Não</option>
                                <option value="true">Sim</option>
                            </select>

                            <input for="login_multinotas_pf" type="text" class="form-control" id="login_multinotas_pf"
                                placeholder="Login do Corretor">
                            <input for="senha_multinotas_pf" type="password" class="form-control"
                                id="senha_multinotas_pf" placeholder="Senha do Corretor">
                        </div>
                        <div class="form-group">
                            <label for="codigo_corretor_pf">Código do Corretor</label>
                            <input type="number" class="form-control" id="codigo_corretor_pf" required>
                        </div>
                        <div class="form-group">
                            <label for="modalidade_pf">Modalidade</label>
                            <input type="text" class="form-control" id="modalidade_pf" required>
                        </div>
                        <div class="form-group">
                            <label for="operadora_pf">Operadora</label>
                            <input type="text" class="form-control" id="operadora_pf" required>
                        </div>
                        <div class="form-group">
                            <label for="admin_pf">Administradora</label>
                            <input type="text" class="form-control" id="admin_pf">
                        </div>
                        <div class="form-group">
                            <label for="entidade_pf">Entidade</label>
                            <input type="text" class="form-control" id="entidade_pf">
                        </div>
                        <div class="form-group">
                            <label for="vigencia_pf">Vigência</label>
                            <input type="date" class="form-control" id="vigencia_pf" required>
                        </div>
                        <div class="form-group">
                            <label>Coparticipação</label>
                            <select class="form-control" id="coparticipacao_pf" required>
                                <option value="" disabled selected>Selecione</option>
                                <option value="true">Sim</option>
                                <option value="false">Não</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="tipo_copart_pf">Tipo de Coparticipação</label>
                            <input type="text" class="form-control" id="tipo_copart_pf">
                        </div>
                        <div class="form-group">
                            <label>Aproveitamento de Carência?</label>
                            <select class="form-control" id="aproveitamento_pf" required>
                                <option value="" disabled selected>Selecione</option>
                                <option value="true">Sim</option>
                                <option value="false">Não</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Odontológico Incluso?</label>
                            <select class="form-control" id="odontologico_pf" required>
                                <option value="" disabled selected>Selecione</option>
                                <option value="true">Sim</option>
                                <option value="false">Não</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="contato_pf">Contato do Responsável</label>
                            <input type="text" class="form-control" id="contato_pf" required>
                        </div>
                        <div class="form-group">
                            <label for="email_pf">E-mail</label>
                            <input type="email" class="form-control" id="email_pf" required>
                        </div>
                        <div class="form-group">
                            <label for="telefone_pf">Telefone</label>
                            <input type="tel" class="form-control" id="telefone_pf" required>
                        </div>
                        <div class="form-group">
                            <label for="valor_cotacao_pf">Valor da Cotação</label>
                            <input type="text" class="form-control" id="valor_cotacao_pf" inputmode="decimal"
                                placeholder="R$ 0,00" required>
                        </div>
                        <div class="form-group">
                            <label for="info_complementares_pf">Informação Complementares</label>
                            <textarea class="form-control" name="info_complementares_pf" id="info_complementares_pf"
                                rows="4"></textarea>
                        </div>
                        <div class="form-group">
                            <button type="button" class="btn btn-primary" id="pf-next-button">Próximo</button>
                        </div>
                    </div>
                    <div id="pme-forms">
                        <div class="form-group">
                            <label>Possui Multinotas?</label>
                            <select class="form-control" id="multinotas-pme" required>
                                <option value="false">Não</option>
                                <option value="true">Sim</option>
                            </select>

                            <input for="login_multinotas_pme" type="text" class="form-control" id="login_multinotas_pme"
                                placeholder="Login do Corretor">
                            <input for="senha_multinotas_pme" type="password" class="form-control"
                                id="senha_multinotas_pme" placeholder="Senha do Corretor">
                        </div>
                        <div class="form-group">
                            <label for="codigo_corretor_pme">Código do Corretor</label>
                            <input type="number" class="form-control" id="codigo_corretor_pme" required>
                        </div>
                        <div class="form-group">
                            <label for="modalidade_pme">Modalidade</label>
                            <input type="text" class="form-control" id="modalidade_pme" required>
                        </div>
                        <div class="form-group">
                            <label for="operadora_pme">Operadora</label>
                            <input type="text" class="form-control" id="operadora_pme" required>
                        </div>
                        <div class="form-group">
                            <label for="razao_social_pme">Razão Social</label>
                            <input type="text" class="form-control" id="razao_social_pme" required>
                        </div>
                        <div class="form-group">
                            <label for="cnpj_pme">CNPJ</label>
                            <input type="text" class="form-control" id="cnpj_pme" required>
                        </div>
                        <div class="form-group">
                            <label for="vigencia_pme">Vigência</label>
                            <input type="date" class="form-control" id="vigencia_pme" required>
                        </div>
                        <div class="form-group">
                            <label for="vencimento_pme">Vencimento</label>
                            <input type="date" class="form-control" id="vencimento_pme">
                        </div>
                        <div class="form-group">
                            <label>Coparticipação</label>
                            <select class="form-control" id="coparticipacao_pme" required>
                                <option value="" disabled selected>Selecione</option>
                                <option value="true">Sim</option>
                                <option value="false">Não</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="tipo_copart_pme">Tipo de Coparticipação</label>
                            <input type="text" class="form-control" id="tipo_copart_pme">
                        </div>
                        <div class="form-group">
                            <label>Plano Compulsório?</label>
                            <select class="form-control" id="plano_compulsorio_pme" required>
                                <option value="" disabled selected>Selecione</option>
                                <option value="true">Sim</option>
                                <option value="false">Não</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Aproveitamento de Carência?</label>
                            <select class="form-control" id="aproveitamento_pme" required>
                                <option value="" disabled selected>Selecione</option>
                                <option value="true">Sim</option>
                                <option value="false">Não</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Odontológico Incluso?</label>
                            <select class="form-control" id="odontologico_pme" required>
                                <option value="" disabled selected>Selecione</option>
                                <option value="true">Sim</option>
                                <option value="false">Não</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="tabela_regiao_pme">Tabela/Região</label>
                            <input type="text" class="form-control" id="tabela_regiao_pme" required>
                        </div>
                        <div class="form-group">
                            <label for="contato_pme">Contato do Responsável</label>
                            <input type="text" class="form-control" id="contato_pme" required>
                        </div>
                        <div class="form-group">
                            <label for="email_pme">E-mail</label>
                            <input type="email" class="form-control" id="email_pme" required>
                        </div>
                        <div class="form-group">
                            <label for="telefone_pme">Telefone</label>
                            <input type="tel" class="form-control" id="telefone_pme" required>
                        </div>
                        <div class="form-group">
                            <label for="valor_cotacao_pme">Valor da Cotação</label>
                            <input type="text" class="form-control" id="valor_cotacao_pme" inputmode="decimal"
                                placeholder="R$ 0,00" required>
                        </div>
                        <div class="form-group">
                            <label for="info_complementares_pme">Informação Complementares</label>
                            <textarea class="form-control" name="info_complementares_pme" id="info_complementares_pme"
                                rows="4"></textarea>
                        </div>
                        <div class="form-group">
                            <button type="button" class="btn btn-primary" id="pme-next-button">Próximo</button>
                        </div>
                    </div>
                </div>

                <div class="card-body" id="beneficiarios-form" style="display: none;">
                    <!-- Formulário Beneficiários -->
                    <div id="benef-form">
                        <div class="form-group">
                            <label>Tipo de Beneficiário</label>
                            <select class="form-control" id="tipo_benef_pme" required>
                                <option value="Titular - Sócio">Titular - Sócio</option>
                                <option value="Titular - Prestador">Titular - Prestador</option>
                                <option value="Titular - Funcionário">Titular - Funcionário</option>
                                <option value="Dependente">Dependente</option>
                                <option value="Agregado">Agregado</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="nome_benef">Nome</label>
                            <input type="text" class="form-control" id="nome_benef" required>
                        </div>
                        <div class="form-group">
                            <label for="plano_benef">Plano</label>
                            <input type="text" class="form-control" id="plano_benef" required>
                        </div>
                        <div class="form-group">
                            <label>Acomodação</label>
                            <select class="form-control" id="acomod_benef" required>
                                <option value="Enfermaria">Enfermaria</option>
                                <option value="Apartamento">Apartamento</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Faixa</label>
                            <select class="form-control" id="faixa_benef" required>
                                <option value="00 a 18">00 a 18</option>
                                <option value="19 a 23">19 a 23</option>
                                <option value="24 a 28">24 a 28</option>
                                <option value="29 a 33">29 a 33</option>
                                <option value="34 a 38">34 a 38</option>
                                <option value="39 a 43">39 a 43</option>
                                <option value="44 a 48">44 a 48</option>
                                <option value="49 a 53">49 a 53</option>
                                <option value="54 a 58">54 a 58</option>
                                <option value="59 ou +">59 ou +</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Grau de Parentesco</label>
                            <select class="form-control" id="grau_benef" required>
                                <option value="Titular">Titular</option>
                                <option value="Cônjuge">Cônjuge</option>
                                <option value="Filho [a]">Filho [a]</option>
                                <option value="Enteado">Enteado</option>
                                <option value="Pai">Pai</option>
                                <option value="Mãe">Mãe</option>
                                <option value="Neto [a]">Neto [a]</option>
                                <option value="Irmão">Irmão</option>
                                <option value="Genro">Genro</option>
                                <option value="Nora">Nora</option>
                                <option value="Cunhado [a]">Cunhado [a]</option>
                                <option value="Outros">Outros</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Estado Civil</label>
                            <select class="form-control" id="civil_benef" required>
                                <option value="Solteiro">Solteiro</option>
                                <option value="Casado/ União Estável">Casado/ União Estável</option>
                                <option value="Divorciado">Divorciado</option>
                                <option value="Viúvo [a]">Viúvo [a]</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="dtNasci_benef">Data de Nascimento</label>
                            <input type="date" class="form-control" id="dtNasci_benef" required>
                        </div>
                        <div class="form-group">
                            <label for="idade_benef">Idade</label>
                            <input type="number" class="form-control" id="idade_benef" required readonly>
                        </div>
                        <div class="form-group">
                            <label for="telefone_benef">Telefone</label>
                            <input type="tel" class="form-control" id="telefone_benef" required>
                        </div>
                        <div class="form-group">
                            <label for="email_benef">E-mail</label>
                            <input type="email" class="form-control" id="email_benef" required>
                        </div>
                    </div>

                    <button type="button" id="add-beneficiary-btn" class="btn btn-success mb-3">
                        <i class="fa-solid fa-person-circle-plus"></i> Adicionar Beneficiário
                    </button>

                    <!-- Tabela para exibir os beneficiários adicionados -->
                    <table class="table table-striped" id="beneficiaries-table">
                        <thead>
                            <tr>
                                <th>Tipo de Beneficiário</th>
                                <th>Nome</th>
                                <th>Plano</th>
                                <th>Acomodação</th>
                                <th>Faixa Etária</th>
                                <th>Parentesco</th>
                                <th>Data de Nascimento</th>
                                <th>Idade</th>
                                <th>Telefone</th>
                                <th>Email</th>
                                <th>Estado Civil</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <button type="button" id="solicitar-emissao-btn" class="btn btn-primary">
                        Solicitar Emissão
                    </button>
                </div>

                <!-- Tela de carregamento -->
                <div id="loadingScreen" class="loading-screen" style="display: none;">
                    <div class="spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Carregando...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Importando JS do AdminLTE e Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1.0/dist/js/adminlte.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/ticket_colaborador.js') }}" defer></script>
</body>

</html>