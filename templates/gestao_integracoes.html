{% extends 'base.html' %}

{% block title %}Integrações | BrazilHealth{% endblock %}

{% block content %}
<div class="gestao-integracoes">
    <div class="header-gestao-integracoes">
        <h1>Integrações</h1>
    </div>
    <div class="gestao-integracoes-container">
        <div class="card-integracoes" onclick="openPopup('usina-proposta-popup')">
            <h2>Usina da Proposta</h2>
            <p>Acesse os sistemas e sites da Usina da Proposta.</p>
        </div>
        <div id="usina-proposta-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('usina-proposta-popup')">&times;</span>
                <h2>Usina da Proposta</h2>
                <!-- Conteúdo adicional para Usina da Proposta -->
            </div>
        </div>

        <div class="card-integracoes" onclick="openPopup('financeiro-popup')">
            <h2>Financeiro</h2>
            <p>Acesse os sistemas e sites do Financeiro.</p>
        </div>
        <div id="financeiro-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('financeiro-popup')">&times;</span>
                <h2>Financeiro</h2>
                <!-- Conteúdo adicional para Financeiro -->
            </div>
        </div>

        <div class="card-integracoes" onclick="openPopup('contas-pagar-popup')">
            <h2>Contas a Pagar</h2>
            <p>Acesse os sistemas e sites de Contas a Pagar.</p>
        </div>
        <div id="contas-pagar-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('contas-pagar-popup')">&times;</span>
                <h2>Contas a Pagar</h2>
                <!-- Conteúdo adicional para Contas a Pagar -->
            </div>
        </div>

        <div class="card-integracoes" onclick="openPopup('franquia-popup')">
            <h2>Franquia</h2>
            <p>Acesse os sistemas e sites da Franquia.</p>
        </div>
        <div id="franquia-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('franquia-popup')">&times;</span>
                <h2>Franquia</h2>
                <!-- Conteúdo adicional para Franquia -->
            </div>
        </div>

        <div class="card-integracoes" onclick="openPopup('pos-vendas-popup')">
            <h2>Pós Vendas</h2>
            <p>Acesse os sistemas e sites de Pós Vendas.</p>
        </div>
        <div id="pos-vendas-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('pos-vendas-popup')">&times;</span>
                <h2>Pós Vendas</h2>
                <!-- Conteúdo adicional para Pós Vendas -->
            </div>
        </div>

        <div class="card-integracoes" onclick="openPopup('pj-corporate-popup')">
            <h2>PJ/Corporate</h2>
            <p>Acesse os sistemas e sites de PJ/Corporate.</p>
        </div>
        <div id="pj-corporate-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('pj-corporate-popup')">&times;</span>
                <h2>PJ/Corporate</h2>
                <!-- Conteúdo adicional para PJ/Corporate -->
            </div>
        </div>

        <div class="card-integracoes" onclick="openPopup('marketing-popup')">
            <h2>Marketing</h2>
            <p>Acesse os sistemas e sites de Marketing.</p>
        </div>
        <div id="marketing-popup" class="popup">
            <div class="popup-content">
                <span class="close" onclick="closePopup('marketing-popup')">&times;</span>
                <h2>Marketing</h2>
                <!-- Conteúdo adicional para Marketing -->
            </div>
        </div>

        <!-- Adicione mais departamentos aqui se necessário -->
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function openPopup(popupId) {
        document.getElementById(popupId).style.display = 'block';
    }

    function closePopup(popupId) {
        document.getElementById(popupId).style.display = 'none';
    }
</script>
{% endblock %}
