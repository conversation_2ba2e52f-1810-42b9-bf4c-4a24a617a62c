{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}criar-informativo{% endblock %}
{% block content %}

<div class="container mt-4">
    <h2>Criar Novo Informativo</h2>
    <form action="{{ url_for('novo_informativo') }}" method="post" enctype="multipart/form-data">
        <div class="form-group-inform">
            <label for="titulo">Título:</label>
            <input type="text" class="form-control" id="titulo" name="titulo" required>
        </div>
        <div class="form-group-inform">
            <label for="conteudo">Conteúdo:</label>
            <textarea class="form-control" id="conteudo" name="conteudo" rows="10" maxlength="5000" required></textarea>
            <small id="conteudo-contador" class="form-text text-muted">0/5000</small>
        </div>
        <div class="form-group-inform">
            <label for="imagem">Imagem:</label>
            <input type="file" class="form-control" id="imagem" name="imagem" accept="image/*">
        </div>
        <button id="publicar-inform" type="submit" class="btn btn-primary">Publicar Informativo</button>
    </form>
</div>

<script src="https://code.jquery.com/jquery-3.x.x.min.js"></script>
<script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>
<script src="{{ url_for('static', filename='js/home.js') }}"></script>
{% endblock %}
