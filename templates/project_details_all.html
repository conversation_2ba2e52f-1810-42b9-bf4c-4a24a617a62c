{# project_details_all.html #}
{% extends "projects_base.html" %}

{% block title %}Detalhes dos Projetos{% endblock %}

{% block header %}
<div class="row mb-2">
  <div class="col-sm-6">
    <h1 class="m-0">Detalhes dos Projetos</h1>
  </div>
</div>
{% endblock %}

{% block content %}
<div class="container-fluid py-3">
  {% if tarefas_dict is not defined %}
    {% set tarefas_dict = {} %}
  {% endif %}

  {% for projeto in projetos %}
  <div class="card mb-4">
    <div class="card-header">
      <div class="row align-items-center">
        <div class="col-md-8">
          <h3 class="card-title mb-0">{{ projeto[1] }}</h3>
        </div>
        <div class="col-md-4 text-right">
          <button type="button" class="btn btn-tool" data-toggle="collapse" data-target="#cardContent{{ projeto[0] }}" aria-expanded="true" aria-controls="cardContent{{ projeto[0] }}">
            <i class="fas fa-minus"></i>
          </button>
        </div>
      </div>
    </div>

    <div id="cardContent{{ projeto[0] }}" class="collapse show">
      <ul class="nav nav-tabs" id="tabsProject{{ loop.index }}" role="tablist">
        <li class="nav-item">
          <a class="nav-link active" id="resumo-tab{{ loop.index }}" data-toggle="tab" href="#resumo{{ loop.index }}" role="tab" aria-controls="resumo{{ loop.index }}" aria-selected="true">
            Resumo Geral
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="tasks-tab{{ loop.index }}" data-toggle="tab" href="#tasks{{ loop.index }}" role="tab" aria-controls="tasks{{ loop.index }}" aria-selected="false">
            Tarefas
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="team-tab{{ loop.index }}" data-toggle="tab" href="#team{{ loop.index }}" role="tab" aria-controls="team{{ loop.index }}" aria-selected="false">
            Equipe
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="timeline-tab{{ loop.index }}" data-toggle="tab" href="#timeline{{ loop.index }}" role="tab" aria-controls="timeline{{ loop.index }}" aria-selected="false">
            Linha do Tempo
          </a>
        </li>
      </ul>

      <div class="tab-content p-3" id="projectTapsContent{{ loop.index }}">

        <!-- ABA: Resumo Geral -->
        <div class="tab-pane fade show active" id="resumo{{ loop.index }}" role="tabpanel" aria-labelledby="resumo-tab{{ loop.index }}">
          <div class="callout callout-warning">
            <h5>Atenção!</h5>
            <p>Você precisa interagir no projeto até <strong>01/04/2025</strong>. Verifique suas tarefas.</p>
          </div>

          <div class="row">
            <div class="col-md-6 col-sm-12 mb-3">
              <p><strong>O que é?</strong> {{ projeto[1] }}</p>
              <p><strong>O que precisa ser feito?</strong> {{ projeto[2] }}</p>
              <p><strong>Por que isso é importante?</strong><br>{{ projeto[3] }}</p>
            </div>
            <div class="col-md-6 col-sm-12 mb-3">
              <div class="row">
                <div class="col-6 mb-3">
                  <p>
                    <strong>Data de Solicitação:</strong><br>
                    <span class="badge badge-secondary">{{ projeto[11] }}</span>
                  </p>
                  <p>
                    <strong>Data de Início:</strong><br>
                    <span class="badge badge-primary">{{ projeto[8] }}</span>
                  </p>
                  <p>
                    <strong>Data de Entrega:</strong><br>
                    <span class="badge badge-info">{{ projeto[9] }}</span>
                  </p>
                </div>
                <div class="col-6 mb-3">
                  <p>
                    <strong>Status Atual:</strong><br>
                    <span class="badge badge-success">{{ projeto[10] }}</span>
                  </p>
                  <p>
                    <strong>Prioridade:</strong><br>
                    {% if projeto[6] == 'Alta' %}
                      <span class="badge badge-danger">{{ projeto[6] }}</span>
                    {% elif projeto[6] in ['Média','Media'] %}
                      <span class="badge badge-warning">{{ projeto[6] }}</span>
                    {% elif projeto[6] == 'Baixa' %}
                      <span class="badge badge-success">{{ projeto[6] }}</span>
                    {% else %}
                      <span class="badge badge-secondary">{{ projeto[6] }}</span>
                    {% endif %}
                  </p>
                  <p>
                    <strong>Complexidade:</strong><br>
                    {% if projeto[7] == 'Alta' %}
                      <span class="badge badge-danger">{{ projeto[7] }}</span>
                    {% elif projeto[7] in ['Média','Media'] %}
                      <span class="badge badge-warning">{{ projeto[7] }}</span>
                    {% elif projeto[7] == 'Baixa' %}
                      <span class="badge badge-success">{{ projeto[7] }}</span>
                    {% else %}
                      <span class="badge badge-secondary">{{ projeto[7] }}</span>
                    {% endif %}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <button type="button" class="btn btn-warning mb-3" data-toggle="modal" data-target="#modalEditarResumo{{ projeto[0] }}">
            Editar Resumo
          </button>

          <!-- Modal Editar Resumo -->
          <div class="modal fade" id="modalEditarResumo{{ projeto[0] }}" tabindex="-1" role="dialog" aria-labelledby="modalEditarResumoLabel{{ projeto[0] }}" aria-hidden="true">
            <div class="modal-dialog" role="document">
              <form action="{{ url_for('editar_resumo', projeto_id=projeto[0]) }}" method="POST">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="modalEditarResumoLabel{{ projeto[0] }}">Editar Resumo Geral</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                      <span aria-hidden="true">&times;</span>
                    </button>
                  </div>
                  <div class="modal-body">
                    <div class="form-group">
                      <label for="titulo{{ projeto[0] }}">Título</label>
                      <input type="text" class="form-control" id="titulo{{ projeto[0] }}" name="titulo" value="{{ projeto[1] }}" required>
                    </div>
                    <div class="form-group">
                      <label for="descricao{{ projeto[0] }}">Descrição</label>
                      <textarea class="form-control" id="descricao{{ projeto[0] }}" name="descricao" rows="3" required>{{ projeto[2] }}</textarea>
                    </div>
                    <div class="form-group">
                      <label for="data_entrega{{ projeto[0] }}">Data de Entrega</label>
                      <input type="date" class="form-control" id="data_entrega{{ projeto[0] }}" name="data_entrega" value="{{ projeto[9] }}">
                    </div>
                    <div class="form-group">
                      <label for="status{{ projeto[0] }}">Status Atual</label>
                      <select class="form-control" id="status{{ projeto[0] }}" name="status">
                        <option value="backlog" {% if projeto[10]=='backlog' %}selected{% endif %}>Backlog</option>
                        <option value="planejamento" {% if projeto[10]=='planejamento' %}selected{% endif %}>Planejamento</option>
                        <option value="execucao" {% if projeto[10]=='execucao' %}selected{% endif %}>Em Execução</option>
                        <option value="em_andamento" {% if projeto[10]=='em_andamento' %}selected{% endif %}>Em Andamento</option>
                        <option value="concluido" {% if projeto[10]=='concluido' %}selected{% endif %}>Concluído</option>
                      </select>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-warning">Salvar Alterações</button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
        <!-- Fim da ABA: Resumo Geral -->

        <!-- ABA: Tarefas -->
        <div class="tab-pane fade" id="tasks{{ loop.index }}" role="tabpanel" aria-labelledby="tasks-tab{{ loop.index }}">
          <button type="button" class="btn btn-primary mb-3" data-toggle="modal" data-target="#modalNovaTarefa{{ projeto[0] }}">
            Nova Tarefa
          </button>

          <div class="row" style="max-height: 600px; overflow-y: auto;">
            {% set tarefas = tarefas_dict[projeto[0]] if projeto[0] in tarefas_dict else {'a_fazer': [], 'em_andamento': [], 'concluido': []} %}

            <!-- ABA: A FAZER -->
            <div class="col-md-4 mb-3">
              <div class="card card-primary">
                <div class="card-header">
                  <h3 class="card-title">A Fazer</h3>
                </div>
                <div class="card-body">
                  {% if tarefas.a_fazer and tarefas.a_fazer|length %}
                    {% for tarefa in tarefas.a_fazer %}
                      <div class="card card-widget mb-2">
                        <div class="card-header">
                          <h5 class="card-title">{{ tarefa.titulo }}</h5>
                        </div>
                        <div class="card-body">
                          <p>{{ tarefa.descricao }}</p>
                          <p>
                            <small>Responsável: {{ tarefa.responsavel_nome }}<br>
                            Prazo: {{ tarefa.data_fim }}</small>
                          </p>
                          {% if not tarefa.id_projeto %}
                            <span class="badge badge-dark">Tarefa sem Projeto</span>
                          {% endif %}
                          <!-- Exibição padronizada dos badges -->
                          <div>
                            {% if tarefa.prioridade == 'Alta' %}
                                <span class="badge badge-danger">Prioridade: Alta</span>
                            {% elif tarefa.prioridade in ['Média','Media'] %}
                                <span class="badge badge-warning">Prioridade: Média</span>
                            {% elif tarefa.prioridade == 'Baixa' %}
                                <span class="badge badge-success">Prioridade: Baixa</span>
                            {% else %}
                                <span class="badge badge-secondary">Prioridade: {{ tarefa.prioridade or 'Não definido' }}</span>
                            {% endif %}
                            {% if tarefa.complexidade == 'Alta' %}
                                <span class="badge badge-danger">Complexidade: Alta</span>
                            {% elif tarefa.complexidade in ['Média','Media'] %}
                                <span class="badge badge-warning">Complexidade: Média</span>
                            {% elif tarefa.complexidade == 'Baixa' %}
                                <span class="badge badge-success">Complexidade: Baixa</span>
                            {% else %}
                                <span class="badge badge-secondary">Complexidade: {{ tarefa.complexidade or 'Não definido' }}</span>
                            {% endif %}
                          </div>
                          <div class="mt-2">
                            {% if tarefa.esta_em_tempo_tracking %}
                              <form action="{{ url_for('finalizar_tarefa', tarefa_id=tarefa.id) }}" method="POST" style="display:inline;">
                                <button type="submit" class="btn btn-danger btn-sm">Parar Tempo</button>
                              </form>
                            {% else %}
                              <form action="{{ url_for('iniciar_tarefa', tarefa_id=tarefa.id) }}" method="POST" style="display:inline;">
                                <button type="submit" class="btn btn-primary btn-sm">Iniciar Tempo</button>
                              </form>
                            {% endif %}
                            <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#modalEditarTarefa{{ tarefa.id }}">
                              Editar Tarefa
                            </button>
                            <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#modalAdicionarMembro{{ tarefa.id }}">
                              Adicionar Membro
                            </button>
                          </div>
                        </div>
                      </div>

                      <!-- Modal EDITAR TAREFA (para A Fazer) -->
                      <div class="modal fade" id="modalEditarTarefa{{ tarefa.id }}" tabindex="-1" role="dialog" aria-labelledby="modalEditarTarefaLabel{{ tarefa.id }}" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                          <form action="{{ url_for('editar_tarefa', tarefa_id=tarefa.id) }}" method="POST">
                            <div class="modal-content">
                              <div class="modal-header">
                                <h5 class="modal-title" id="modalEditarTarefaLabel{{ tarefa.id }}">Editar Tarefa</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                  <span aria-hidden="true">&times;</span>
                                </button>
                              </div>
                              <div class="modal-body">
                                <div class="form-group">
                                  <label for="titulo{{ tarefa.id }}">Título</label>
                                  <input type="text" class="form-control" id="titulo{{ tarefa.id }}" name="titulo" value="{{ tarefa.titulo }}" required>
                                </div>
                                <div class="form-group">
                                  <label for="descricao{{ tarefa.id }}">Descrição</label>
                                  <textarea class="form-control" id="descricao{{ tarefa.id }}" name="descricao" rows="3" required>{{ tarefa.descricao }}</textarea>
                                </div>
                                <div class="form-group">
                                  <label for="data_fim{{ tarefa.id }}">Data Fim</label>
                                  <input type="date" class="form-control" id="data_fim{{ tarefa.id }}" name="data_fim" value="{{ tarefa.data_fim }}">
                                </div>
                                <!-- Sempre exibe os campos de Prioridade e Complexidade -->
                                <div class="form-group">
                                  <label for="prioridade{{ tarefa.id }}">Prioridade</label>
                                  <select class="form-control" name="prioridade" id="prioridade{{ tarefa.id }}">
                                    <option value="Alta" {% if tarefa.prioridade=='Alta' %}selected{% endif %}>Alta</option>
                                    <option value="Média" {% if tarefa.prioridade in ['Média','Media'] %}selected{% endif %}>Média</option>
                                    <option value="Baixa" {% if tarefa.prioridade=='Baixa' %}selected{% endif %}>Baixa</option>
                                  </select>
                                </div>
                                <div class="form-group">
                                  <label for="complexidade{{ tarefa.id }}">Complexidade</label>
                                  <select class="form-control" name="complexidade" id="complexidade{{ tarefa.id }}">
                                    <option value="Alta" {% if tarefa.complexidade=='Alta' %}selected{% endif %}>Alta</option>
                                    <option value="Média" {% if tarefa.complexidade in ['Média','Media'] %}selected{% endif %}>Média</option>
                                    <option value="Baixa" {% if tarefa.complexidade=='Baixa' %}selected{% endif %}>Baixa</option>
                                  </select>
                                </div>
                              </div>
                              <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                                <button type="submit" class="btn btn-warning">Salvar Alterações</button>
                              </div>
                            </div>
                          </form>
                        </div>
                      </div>

                      <!-- Modal ADICIONAR MEMBRO (para A Fazer) -->
                      <div class="modal fade" id="modalAdicionarMembro{{ tarefa.id }}" tabindex="-1" role="dialog" aria-labelledby="modalAdicionarMembroLabel{{ tarefa.id }}" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                          <form action="{{ url_for('adicionar_membro_tarefa', tarefa_id=tarefa.id) }}" method="POST">
                            <div class="modal-content">
                              <div class="modal-header">
                                <h5 class="modal-title" id="modalAdicionarMembroLabel{{ tarefa.id }}">Adicionar Membro à Tarefa ({{ tarefa.titulo }})</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                  <span aria-hidden="true">&times;</span>
                                </button>
                              </div>
                              <div class="modal-body">
                                <div class="form-group">
                                  <label for="membro_tarefa{{ tarefa.id }}">Selecione o Membro:</label>
                                  <select class="form-control" id="membro_tarefa{{ tarefa.id }}" name="membro_tarefa" required>
                                    {% for user in users %}
                                      <option value="{{ user.id }}">{{ user.name }}</option>
                                    {% endfor %}
                                  </select>
                                </div>
                              </div>
                              <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                                <button type="submit" class="btn btn-info">Adicionar</button>
                              </div>
                            </div>
                          </form>
                        </div>
                      </div>
                    {% endfor %}
                  {% else %}
                    <p>Nenhuma tarefa nesta categoria.</p>
                  {% endif %}
                </div>
              </div>
            </div>
            <!-- Fim da coluna A FAZER -->

            <!-- ABA: EM ANDAMENTO -->
            <div class="col-md-4 mb-3">
              <div class="card card-warning">
                <div class="card-header">
                  <h3 class="card-title">Em Andamento</h3>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                  {% if tarefas.em_andamento and tarefas.em_andamento|length %}
                    {% for tarefa in tarefas.em_andamento %}
                      <div class="card card-widget mb-2">
                        <div class="card-header">
                          <h5 class="card-title">{{ tarefa.titulo }}</h5>
                        </div>
                        <div class="card-body">
                          <p>{{ tarefa.descricao }}</p>
                          <p>
                            <small>Responsável: {{ tarefa.responsavel_nome }}<br>
                            Prazo: {{ tarefa.data_fim }}</small>
                          </p>
                          {% if not tarefa.id_projeto %}
                            <span class="badge badge-dark">Tarefa sem Projeto</span>
                          {% endif %}
                          <!-- Exibição padronizada dos badges -->
                          <div>
                            {% if tarefa.prioridade == 'Alta' %}
                                <span class="badge badge-danger">Prioridade: Alta</span>
                            {% elif tarefa.prioridade in ['Média','Media'] %}
                                <span class="badge badge-warning">Prioridade: Média</span>
                            {% elif tarefa.prioridade == 'Baixa' %}
                                <span class="badge badge-success">Prioridade: Baixa</span>
                            {% else %}
                                <span class="badge badge-secondary">Prioridade: {{ tarefa.prioridade or 'Não definido' }}</span>
                            {% endif %}
                            {% if tarefa.complexidade == 'Alta' %}
                                <span class="badge badge-danger">Complexidade: Alta</span>
                            {% elif tarefa.complexidade in ['Média','Media'] %}
                                <span class="badge badge-warning">Complexidade: Média</span>
                            {% elif tarefa.complexidade == 'Baixa' %}
                                <span class="badge badge-success">Complexidade: Baixa</span>
                            {% else %}
                                <span class="badge badge-secondary">Complexidade: {{ tarefa.complexidade or 'Não definido' }}</span>
                            {% endif %}
                          </div>
                          <div class="mt-2">
                            {% if tarefa.esta_em_tempo_tracking %}
                              <form action="{{ url_for('finalizar_tarefa', tarefa_id=tarefa.id) }}" method="POST" style="display:inline;">
                                <button type="submit" class="btn btn-danger btn-sm">Parar Tempo</button>
                              </form>
                            {% else %}
                              <form action="{{ url_for('iniciar_tarefa', tarefa_id=tarefa.id) }}" method="POST" style="display:inline;">
                                <button type="submit" class="btn btn-primary btn-sm">Iniciar Tempo</button>
                              </form>
                            {% endif %}
                            <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#modalEditarTarefa{{ tarefa.id }}">
                              Editar Tarefa
                            </button>
                            <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#modalAdicionarMembro{{ tarefa.id }}">
                              Adicionar Membro
                            </button>
                          </div>
                        </div>
                      </div>

                      <!-- Modal EDITAR TAREFA (para Em Andamento) -->
                      <div class="modal fade" id="modalEditarTarefa{{ tarefa.id }}" tabindex="-1" role="dialog" aria-labelledby="modalEditarTarefaLabel{{ tarefa.id }}" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                          <form action="{{ url_for('editar_tarefa', tarefa_id=tarefa.id) }}" method="POST">
                            <div class="modal-content">
                              <div class="modal-header">
                                <h5 class="modal-title" id="modalEditarTarefaLabel{{ tarefa.id }}">Editar Tarefa</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                  <span aria-hidden="true">&times;</span>
                                </button>
                              </div>
                              <div class="modal-body">
                                <div class="form-group">
                                  <label for="titulo{{ tarefa.id }}">Título</label>
                                  <input type="text" class="form-control" id="titulo{{ tarefa.id }}" name="titulo" value="{{ tarefa.titulo }}" required>
                                </div>
                                <div class="form-group">
                                  <label for="descricao{{ tarefa.id }}">Descrição</label>
                                  <textarea class="form-control" id="descricao{{ tarefa.id }}" name="descricao" rows="3" required>{{ tarefa.descricao }}</textarea>
                                </div>
                                <div class="form-group">
                                  <label for="data_fim{{ tarefa.id }}">Data Fim</label>
                                  <input type="date" class="form-control" id="data_fim{{ tarefa.id }}" name="data_fim" value="{{ tarefa.data_fim }}">
                                </div>
                                <!-- Exibe sempre os campos de Prioridade e Complexidade -->
                                <div class="form-group">
                                  <label for="prioridade{{ tarefa.id }}">Prioridade</label>
                                  <select class="form-control" name="prioridade" id="prioridade{{ tarefa.id }}">
                                    <option value="Alta" {% if tarefa.prioridade=='Alta' %}selected{% endif %}>Alta</option>
                                    <option value="Média" {% if tarefa.prioridade in ['Média','Media'] %}selected{% endif %}>Média</option>
                                    <option value="Baixa" {% if tarefa.prioridade=='Baixa' %}selected{% endif %}>Baixa</option>
                                  </select>
                                </div>
                                <div class="form-group">
                                  <label for="complexidade{{ tarefa.id }}">Complexidade</label>
                                  <select class="form-control" name="complexidade" id="complexidade{{ tarefa.id }}">
                                    <option value="Alta" {% if tarefa.complexidade=='Alta' %}selected{% endif %}>Alta</option>
                                    <option value="Média" {% if tarefa.complexidade in ['Média','Media'] %}selected{% endif %}>Média</option>
                                    <option value="Baixa" {% if tarefa.complexidade=='Baixa' %}selected{% endif %}>Baixa</option>
                                  </select>
                                </div>
                              </div>
                              <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                                <button type="submit" class="btn btn-warning">Salvar Alterações</button>
                              </div>
                            </div>
                          </form>
                        </div>
                      </div>

                      <!-- Modal ADICIONAR MEMBRO (para Em Andamento) -->
                      <div class="modal fade" id="modalAdicionarMembro{{ tarefa.id }}" tabindex="-1" role="dialog" aria-labelledby="modalAdicionarMembroLabel{{ tarefa.id }}" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                          <form action="{{ url_for('adicionar_membro_tarefa', tarefa_id=tarefa.id) }}" method="POST">
                            <div class="modal-content">
                              <div class="modal-header">
                                <h5 class="modal-title" id="modalAdicionarMembroLabel{{ tarefa.id }}">Adicionar Membro à Tarefa ({{ tarefa.titulo }})</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                  <span aria-hidden="true">&times;</span>
                                </button>
                              </div>
                              <div class="modal-body">
                                <div class="form-group">
                                  <label for="membro_tarefa{{ tarefa.id }}">Selecione o Membro:</label>
                                  <select class="form-control" id="membro_tarefa{{ tarefa.id }}" name="membro_tarefa" required>
                                    {% for user in users %}
                                      <option value="{{ user.id }}">{{ user.name }}</option>
                                    {% endfor %}
                                  </select>
                                </div>
                              </div>
                              <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                                <button type="submit" class="btn btn-info">Adicionar</button>
                              </div>
                            </div>
                          </form>
                        </div>
                      </div>
                    {% endfor %}
                  {% else %}
                    <p>Nenhuma tarefa nesta categoria.</p>
                  {% endif %}
                </div>
              </div>
            </div>
            <!-- Fim da coluna EM ANDAMENTO -->

            <!-- ABA: CONCLUÍDO -->
            <div class="col-md-4 mb-3">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Concluído</h3>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                  {% if tarefas.concluido and tarefas.concluido|length %}
                    {% for tarefa in tarefas.concluido %}
                      <div class="card card-widget mb-2">
                        <div class="card-header">
                          <h5 class="card-title">{{ tarefa.titulo }}</h5>
                        </div>
                        <div class="card-body">
                          <p>{{ tarefa.descricao }}</p>
                          <p>
                            <small>Responsável: {{ tarefa.responsavel_nome }}<br>
                            Prazo: {{ tarefa.data_fim }}<br>
                            Tempo Gasto: {{ tarefa.tempo_gasto or '00:00' }}</small>
                          </p>
                          {% if not tarefa.id_projeto %}
                            <span class="badge badge-dark">Tarefa sem Projeto</span>
                          {% endif %}
                          <!-- Exibição padronizada dos badges -->
                          <div>
                            {% if tarefa.prioridade == 'Alta' %}
                                <span class="badge badge-danger">Prioridade: Alta</span>
                            {% elif tarefa.prioridade in ['Média','Media'] %}
                                <span class="badge badge-warning">Prioridade: Média</span>
                            {% elif tarefa.prioridade == 'Baixa' %}
                                <span class="badge badge-success">Prioridade: Baixa</span>
                            {% else %}
                                <span class="badge badge-secondary">Prioridade: {{ tarefa.prioridade or 'Não definido' }}</span>
                            {% endif %}
                            {% if tarefa.complexidade == 'Alta' %}
                                <span class="badge badge-danger">Complexidade: Alta</span>
                            {% elif tarefa.complexidade in ['Média','Media'] %}
                                <span class="badge badge-warning">Complexidade: Média</span>
                            {% elif tarefa.complexidade == 'Baixa' %}
                                <span class="badge badge-success">Complexidade: Baixa</span>
                            {% else %}
                                <span class="badge badge-secondary">Complexidade: {{ tarefa.complexidade or 'Não definido' }}</span>
                            {% endif %}
                          </div>
                        </div>
                      </div>
                    {% endfor %}
                  {% else %}
                    <p>Nenhuma tarefa nesta categoria.</p>
                  {% endif %}
                </div>
              </div>
            </div>
            <!-- Fim da coluna CONCLUÍDO -->
          </div>

          <!-- Modal de Cadastro de Nova Tarefa -->
          <div class="modal fade" id="modalNovaTarefa{{ projeto[0] }}" tabindex="-1" role="dialog" aria-labelledby="modalNovaTarefaLabel{{ projeto[0] }}" aria-hidden="true">
            <div class="modal-dialog" role="document">
              <form action="{{ url_for('tarefas_add') }}" method="POST">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="modalNovaTarefaLabel{{ projeto[0] }}">Cadastro de Nova Tarefa</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                      <span aria-hidden="true">&times;</span>
                    </button>
                  </div>
                  <div class="modal-body">
                    <div class="form-group">
                      <label for="titulo{{ projeto[0] }}">Título</label>
                      <input type="text" class="form-control" id="titulo{{ projeto[0] }}" name="titulo" placeholder="Digite o título da tarefa" required>
                    </div>
                    <div class="form-group">
                      <label for="descricao{{ projeto[0] }}">Descrição</label>
                      <textarea class="form-control" id="descricao{{ projeto[0] }}" name="descricao" rows="3" placeholder="Descreva a tarefa" required></textarea>
                    </div>
                    <div class="form-group">
                      <label for="data_inicio{{ projeto[0] }}">Data de Início</label>
                      <input type="date" class="form-control" id="data_inicio{{ projeto[0] }}" name="data_inicio">
                    </div>
                    <div class="form-group">
                      <label for="data_fim{{ projeto[0] }}">Data de Fim / Prazo</label>
                      <input type="date" class="form-control" id="data_fim{{ projeto[0] }}" name="data_fim">
                    </div>
                    <!-- Campos adicionados para registrar Prioridade e Complexidade -->
                    <div class="form-group">
                      <label for="prioridade{{ projeto[0] }}">Prioridade</label>
                      <select class="form-control" name="prioridade" id="prioridade{{ projeto[0] }}">
                        <option value="Alta">Alta</option>
                        <option value="Média">Média</option>
                        <option value="Baixa">Baixa</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label for="complexidade{{ projeto[0] }}">Complexidade</label>
                      <select class="form-control" name="complexidade" id="complexidade{{ projeto[0] }}">
                        <option value="Alta">Alta</option>
                        <option value="Média">Média</option>
                        <option value="Baixa">Baixa</option>
                      </select>
                    </div>
                    <input type="hidden" name="projeto_id" value="{{ projeto[0] }}">
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Salvar Tarefa</button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
        <!-- Fim da ABA: Tarefas -->

        <!-- ABA: Equipe -->
        <div class="tab-pane fade" id="team{{ loop.index }}" role="tabpanel" aria-labelledby="team-tab{{ loop.index }}">
          <p><strong>Solicitante do projeto:</strong></p>
          <ul>
            <li>
              {{ projeto.responsavel or projeto[4] }}
              {% if projeto[5] %}
                ({{ projeto[5] }})
              {% endif %}
            </li>
          </ul>
          <p><strong>Membros da Equipe:</strong></p>
          {% if equipe_dict and projeto[0] in equipe_dict %}
            {% set equipe = equipe_dict[projeto[0]] %}
            <ul>
              {% for membro in equipe %}
              <li>
                {{ membro.usuario_nome }}
                {% if membro.cargo %} - {{ membro.cargo }}{% endif %}
                <small>(Adicionado em: {{ membro.data_adicao }})</small>
              </li>
              {% endfor %}
            </ul>
          {% else %}
            <p>Nenhum membro adicionado.</p>
          {% endif %}

          <p><strong>Adicionar Membros da Equipe:</strong></p>
          <form action="{{ url_for('add_team_members', project_id=projeto[0]) }}" method="POST">
            <div class="form-group">
              <label for="teamMembers{{ loop.index }}">Selecione os Membros:</label>
              <select class="form-control" id="teamMembers{{ loop.index }}" name="team_members" multiple required>
                {% for user in users %}
                  <option value="{{ user.id }}">{{ user.name }}</option>
                {% endfor %}
              </select>
            </div>
            <button type="submit" class="btn btn-primary">Adicionar Membros</button>
          </form>
        </div>
        <!-- Fim da ABA: Equipe -->

        <!-- ABA: Linha do Tempo -->
        <div class="tab-pane fade" id="timeline{{ loop.index }}" role="tabpanel" aria-labelledby="timeline-tab{{ loop.index }}">
          <div class="timeline">
            <div class="time-label">
              <span class="bg-red">01/01/2025</span>
            </div>
            <div>
              <i class="fas fa-check bg-green"></i>
              <div class="timeline-item">
                <h3 class="timeline-header">
                  <a href="#">Fase 1: Planejamento</a>
                </h3>
                <div class="timeline-body">
                  Planejamento inicial, brainstorming, reuniões...
                </div>
              </div>
            </div>
            <div>
              <i class="fas fa-clock bg-gray"></i>
            </div>
          </div>
        </div>
        <!-- Fim da ABA: Linha do Tempo -->

      </div>
    </div>
  </div>
  {% endfor %}
</div>
{% endblock %}

{% block extra_styles %}
<style>
  .callout {
    border-radius: 0.25rem;
    margin: 1.5rem 0;
    padding: 1rem;
  }
  .nav-tabs {
    margin-bottom: 1rem;
  }
  .tab-content {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-top: none;
  }
  .tab-content > .tab-pane {
    display: none;
  }
  .tab-content > .active {
    display: block;
  }
  .card {
    border-radius: 0.25rem;
  }
  .card-body {
    padding: 1.25rem;
  }
  .card-body p {
    margin-bottom: 0.75rem;
  }
  .card-widget {
    margin-bottom: 1rem;
  }
  .timeline {
    position: relative;
    padding: 2rem 0;
  }
  .time-label {
    display: inline-block;
    margin-bottom: 1rem;
  }
  .bg-red {
    background-color: #f56954 !important;
    color: #fff;
    padding: 5px 10px;
    border-radius: 5px;
  }
  .timeline-item {
    margin-bottom: 1.5rem;
    padding-left: 2.5rem;
    position: relative;
  }
  .timeline-header {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  .timeline-body {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 5px;
  }
</style>
{% endblock %}
