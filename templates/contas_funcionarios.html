<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Funcionários</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/css/adminlte.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .feedback-container {
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1050;
            width: 90%;
            max-width: 600px;
            pointer-events: none;
            /* Evita cliques acidentais */
        }

        .feedback-container .alert {
            pointer-events: all;
            /* Permite fechar o alerta */
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>

<body class="hold-transition sidebar-mini">
    <div id="feedback-container" class="feedback-container"></div>
    <div class="wrapper">
        <div class="content-wrapper">
            <div class="wrapper">
                <!-- Exibir mensagens de flash -->
                <div class="container mt-3">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                    {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    {% endfor %}
                    {% endif %}
                    {% endwith %}
                </div>
                <div class="container">
                    <!-- Header -->
                    <section class="content-header">
                        <div class="container-fluid">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <h1>Gestão de Funcionários</h1>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Main Content -->
                    <section class="content">
                        <div class="container-fluid">
                            <div class="card card-primary">
                                <div class="card-header">
                                    <h3 class="card-title">Gerenciar Funcionários</h3>
                                </div>
                                <div class="card-body">
                                    <ul class="nav nav-tabs" id="tabfuncionario" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="tab-cadastrar-funcionario" data-toggle="tab"
                                                href="#cadastrar-funcionario" role="tab"
                                                aria-controls="cadastrar-funcionario" aria-selected="true">
                                                <i class="fas fa-plus-circle"></i> Cadastrar Funcionário
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="tab-listar-funcionario" data-toggle="tab"
                                                href="#listar-funcionario" role="tab" aria-controls="listar-funcionario"
                                                aria-selected="false">
                                                <i class="fas fa-list"></i> Funcionários Cadastrados
                                            </a>
                                        </li>
                                    </ul>
                                    <div class="tab-content" id="tabContentfuncionario">
                                        <!-- Aba Cadastrar Funcionário -->
                                        <div class="tab-pane fade show active" id="cadastrar-funcionario"
                                            role="tabpanel" aria-labelledby="tab-cadastrar-funcionario">
                                            <div class="card mt-3">
                                                <div class="card-header bg-primary text-white">
                                                    <h3 class="card-title">Cadastrar Novo Funcionários</h3>
                                                </div>
                                                <form id="form-funcionario">
                                                    <div class="card-body">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="nome">Nome</label>
                                                                    <input type="text" class="form-control" id="nome"
                                                                        name="nome"
                                                                        placeholder="Digite o nome do funcionários"
                                                                        required>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="cpf">CPF</label>
                                                                    <input type="text" class="form-control" id="cpf"
                                                                        name="cpf"
                                                                        placeholder="Digite o CPF do Funcionário"
                                                                        required>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="telefone">Telefone</label>
                                                                    <input type="text" class="form-control"
                                                                        id="telefone" name="telefone"
                                                                        placeholder="Digite o telefone do Funcionário">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="email">Email</label>
                                                                    <input type="email" class="form-control" id="email"
                                                                        name="email"
                                                                        placeholder="Digite o email do Funcionário">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="card-footer text-right">
                                                        <button id="botaoCadastrarFuncionario" type="button"
                                                            class="btn btn-primary">Cadastrar</button>
                                                        <button type="reset" class="btn btn-secondary">Limpar</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>

                                        <!-- Aba Listar Funcionários -->
                                        <div class="tab-pane fade" id="listar-funcionario" role="tabpanel"
                                            aria-labelledby="tab-listar-funcionario">
                                            <div class="card mt-3">
                                                <div class="card-header bg-secondary text-white">
                                                    <h3 class="card-title">Funcionários Cadastrados</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <select id="filtro-nome-funcionario"
                                                                    class="form-control">
                                                                    <option value="">Todos os Funcionários</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="table-responsive">
                                                        <table class="table table-bordered table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Nome</th>
                                                                    <th>CPF</th>
                                                                    <th>Email</th>
                                                                    <th>Telefone</th>
                                                                    <th>Ações</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="funcionario-list">
                                                                <!-- Os dados serão inseridos aqui dinamicamente -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Modal Editar Funcionário (Novo) -->
                                        <div class="modal fade" id="modalEditarFuncionarioNovo" tabindex="-1" role="dialog"
                                            aria-labelledby="modalEditarFuncionarioNovoLabel" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="modalEditarFuncionarioNovoLabel">Editar
                                                            Funcionário</h5>
                                                        <button type="button" class="close" data-dismiss="modal"
                                                            aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <form id="form-editar-funcionario-novo">
                                                            <input type="hidden" id="editar-funcionario-id-novo">
                                                            <div class="form-group">
                                                                <label for="editar-nome-funcionario-novo">Nome</label>
                                                                <input type="text" class="form-control"
                                                                    id="editar-nome-funcionario-novo" name="nome" required>
                                                            </div>
                                                            <div class="form-group">
                                                                <label for="editar-cpf-funcionario-novo">CPF</label>
                                                                <input type="text" class="form-control"
                                                                    id="editar-cpf-funcionario-novo" name="cpf" required>
                                                            </div>
                                                            <div class="form-group">
                                                                <label for="editar-telefone-funcionario-novo">Telefone</label>
                                                                <input type="text" class="form-control"
                                                                    id="editar-telefone-funcionario-novo" name="telefone" required>
                                                            </div>
                                                            <div class="form-group">
                                                                <label for="editar-email-funcionario-novo">Email</label>
                                                                <input type="email" class="form-control"
                                                                    id="editar-email-funcionario-novo" name="email" required>
                                                            </div>
                                                        </form>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-dismiss="modal">Cancelar</button>
                                                        <button type="button" id="salvar-editar-funcionario-novo"
                                                            class="btn btn-primary">Salvar</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>

        <!-- Flash Messages Container -->
        <div class="flash-message-container">
            {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            {% endfor %}
            {% endif %}
            {% endwith %}
        </div>


        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="spinner-border" role="status">
                <span class="sr-only">Carregando...</span>
            </div>
        </div>

        <!-- JQuery -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
        <!-- AdminLTE JS -->
        <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/js/adminlte.min.js"></script>
        <!-- Custom JS -->
        <script src="/static/js/contas_home.js"></script>

        <!-- Script personalizado para edição de funcionários -->
        <script>
            // Função para adicionar eventos aos botões de editar
            function addEditButtonEvents() {
                console.log('Adicionando eventos aos botões de editar funcionários');
                $('.btn-edit-funcionario').off('click').on('click', function(event) {
                    event.preventDefault();

                    // Tenta obter o ID do atributo data-id
                    let funcionarioId = $(this).data('id') || $(this).attr('data-id');
                    console.log('Botão clicado, ID do atributo data-id:', funcionarioId);

                    // Se não encontrou, tenta obter do atributo data-funcionario-id da linha
                    if (!funcionarioId) {
                        const parentRow = $(this).closest('tr');
                        if (parentRow.length) {
                            funcionarioId = parentRow.data('funcionario-id') || parentRow.attr('data-funcionario-id');
                            console.log('ID obtido da linha (data-funcionario-id):', funcionarioId);
                        }
                    }

                    // Se ainda não encontrou, tenta obter do texto do botão
                    if (!funcionarioId) {
                        // Tenta extrair o ID do texto do botão ou de outro lugar
                        const buttonText = $(this).text().trim();
                        const match = buttonText.match(/\d+/);
                        if (match) {
                            funcionarioId = match[0];
                            console.log('ID extraído do texto do botão:', funcionarioId);
                        }
                    }

                    if (!funcionarioId) {
                        console.error('ID do funcionário não encontrado.');
                        return;
                    }

                    console.log('Buscando dados do funcionário ID:', funcionarioId);

                    // Busca os dados do funcionário pelo ID
                    fetch(`/get_funcionarios/${funcionarioId}`)
                        .then(response => {
                            console.log('Resposta da API:', response);
                            if (!response.ok) {
                                throw new Error('Funcionário não encontrado');
                            }
                            return response.json();
                        })
                        .then(funcionario => {
                            console.log('Dados do funcionário recebidos da API:', funcionario);

                            // Preenche os campos do formulário com os dados recebidos da API
                            $('#editar-funcionario-id-novo').val(funcionario.id || '');
                            $('#editar-nome-funcionario-novo').val(funcionario.nome || '');
                            $('#editar-cpf-funcionario-novo').val(funcionario.cpf || '');
                            $('#editar-email-funcionario-novo').val(funcionario.email || '');
                            $('#editar-telefone-funcionario-novo').val(funcionario.telefone || '');

                            // Abre o modal
                            console.log('Abrindo modal existente...');

                            // Verifica se o modal existe
                            if ($('#modalEditarFuncionarioNovo').length) {
                                $('#modalEditarFuncionarioNovo').modal('show');
                            } else {
                                alert('Modal não encontrado! Verifique o console para mais detalhes.');
                                console.error('Modal #modalEditarFuncionarioNovo não encontrado!');

                                // Lista todos os modais na página
                                console.log('Modais disponíveis:');
                                $('.modal').each(function() {
                                    console.log('- Modal ID:', $(this).attr('id'));
                                });
                            }
                        })
                        .catch(error => console.error('Erro ao carregar dados do funcionário:', error));
                });
            }

            $(document).ready(function() {
                // Verifica se o modal existe e se o Bootstrap está carregado corretamente
                console.log('DIAGNÓSTICO - Modal existe:', $('#modalEditarFuncionarioNovo').length);
                console.log('DIAGNÓSTICO - Bootstrap modal disponível:', typeof $.fn.modal !== 'undefined');

                // Verifica se o Bootstrap está carregado corretamente
                if (typeof $.fn.modal === 'undefined') {
                    console.error('DIAGNÓSTICO - Bootstrap modal não está disponível. Recarregando a biblioteca...');
                    // Tenta recarregar o Bootstrap
                    $('head').append('<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>');

                    // Espera um pouco e tenta inicializar o modal novamente
                    setTimeout(function() {
                        if (typeof $.fn.modal !== 'undefined') {
                            console.log('DIAGNÓSTICO - Bootstrap recarregado com sucesso!');
                            // Inicializa o modal
                            $('#modalEditarFuncionarioNovo').modal({
                                backdrop: true,
                                keyboard: true,
                                focus: true,
                                show: false
                            });
                        } else {
                            console.error('DIAGNÓSTICO - Falha ao recarregar o Bootstrap. Modal pode não funcionar corretamente.');
                        }
                    }, 1000);
                } else {
                    // Inicializa o modal
                    $('#modalEditarFuncionarioNovo').modal({
                        backdrop: true,
                        keyboard: true,
                        focus: true,
                        show: false
                    });
                }

                // Adiciona eventos aos botões de editar quando a página carrega
                addEditButtonEvents();

                // Adiciona eventos aos botões de editar quando a tabela é atualizada
                $(document).on('DOMNodeInserted', '#funcionario-list', function() {
                    addEditButtonEvents();
                });

                // Aplica máscaras ao campo de CPF
                $('#editar-cpf-funcionario-novo').on('input', function() {
                    this.value = this.value.replace(/\D/g, '').replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
                });

                // Aplica máscaras ao campo de Telefone
                $('#editar-telefone-funcionario-novo').on('input', function() {
                    this.value = this.value.replace(/\D/g, '').replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
                });

                // Adiciona evento de clique ao botão de salvar
                $('#salvar-editar-funcionario-novo').on('click', function() {
                    if (confirm('Deseja salvar as alterações?')) {
                        // Coleta os valores dos campos do modal
                        const funcionarioId = $('#editar-funcionario-id-novo').val();
                        const nome = $('#editar-nome-funcionario-novo').val();
                        const cpf = $('#editar-cpf-funcionario-novo').val();
                        const email = $('#editar-email-funcionario-novo').val();
                        const telefone = $('#editar-telefone-funcionario-novo').val();

                        console.log('Enviando dados para atualização:', {
                            id: funcionarioId,
                            nome: nome,
                            cpf: cpf,
                            email: email,
                            telefone: telefone
                        });

                        // Envia os dados para a API
                        fetch(`/update_funcionarios/${funcionarioId}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                nome: nome,
                                cpf: cpf,
                                email: email,
                                telefone: telefone
                            })
                        })
                        .then(response => {
                            console.log('Resposta da API de atualização:', response);
                            return response.json();
                        })
                        .then(result => {
                            console.log('Resultado da atualização:', result);
                            if (result.message) {
                                alert('Funcionário atualizado com sucesso!');
                                location.reload();
                            } else {
                                alert('Erro ao atualizar funcionário: ' + result.error);
                            }
                        })
                        .catch(error => {
                            console.error('Erro ao atualizar o funcionário:', error);
                            alert('Erro ao atualizar o funcionário.');
                        });
                    }
                });
            });
        </script>
</body>

</html>