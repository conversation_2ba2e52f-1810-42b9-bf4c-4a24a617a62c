<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM - Perfil da Empresa</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        /* Estilos personalizados */
        body {
            background-color: #f4f6f9;
            font-family: Arial, sans-serif;
        }

        .sidebar {
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            width: 250px;
            background-color: #343a40;
            color: #fff;
            padding: 20px;
            overflow-y: auto;
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
        }

        .sidebar h4 {
            color: #fff;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .filter-option label {
            font-weight: bold;
            font-size: 0.9em;
        }

        .main-content {
            margin-left: 270px;
            padding: 30px;
        }

        .filter-container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        #cliente-container {
            margin-bottom: 30px;
            max-height: 400px;
            overflow-y: auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .nav-tabs {
            margin-bottom: 20px;
        }

        .filter-item select {
            border-radius: 5px;
            width: 100%;
        }

        .nav-tabs .nav-link {
            color: #007bff;
            font-weight: bold;
        }

        .nav-tabs .nav-link.active {
            color: #343a40;
            background-color: #ffffff;
            border-color: #dee2e6 #dee2e6 #ffffff;
        }

        .tab-content .content {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }

        .table th {
            background-color: #007bff;
            color: #ffffff;
        }

        .table td,
        .table th {
            padding: 12px;
            text-align: left;
        }

        .card-header button {
            text-align: left;
            width: 100%;
        }

        .card.collapsed .card-body {
            display: none;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 15px;
            }

            .sidebar {
                width: 100%;
                height: auto;
                margin-bottom: 20px;
                position: relative;
            }
        }
    </style>
</head>

<body>
    <!-- Barra lateral de filtros -->
    <div class="sidebar">
        <h4>Filtros</h4>
        <div class="filter-option">
            <label for="filter4">Inicial</label>
            <input type="date" id="filter4" class="form-control">
        </div>
        <div class="filter-option">
            <label for="filter5">Final</label>
            <input type="date" id="filter5" class="form-control">
        </div>
        <button id="searchButton" class="btn btn-primary mt-3">Pesquisar</button>
        <button id="clearButton" class="btn btn-secondary mt-3">Limpar</button>
    </div>

    <div class="main-content" style="margin-left: 270px;">
        <!-- Filtros adicionais -->
        <div class="filter-container d-flex flex-wrap mb-3">
            <div class="filter-item flex-fill p-2">
                <select class="js-example-basic-multiple form-control" name="operadoras[]" multiple="multiple"
                    aria-label="Filtrar por Operadora">
                    <option value="operadora1">Operadora 1</option>
                    <option value="operadora2">Operadora 2</option>
                    <option value="operadora3">Operadora 3</option>
                </select>
            </div>
            <div class="filter-item flex-fill p-2">
                <select class="js-example-basic-multiple form-control" name="status[]" multiple="multiple"
                    aria-label="Filtrar por Produção Status">
                    <option value="status1">Status 1</option>
                    <option value="status2">Status 2</option>
                    <option value="status3">Status 3</option>
                </select>
            </div>
            <div class="filter-item flex-fill p-2">
                <select class="js-example-basic-multiple form-control" name="modalidade[]" multiple="multiple"
                    aria-label="Filtrar por Modalidade">
                    <option value="individual">Individual</option>
                    <option value="familiar">Familiar</option>
                    <option value="empresarial">Empresarial</option>
                </select>
            </div>
            <div class="filter-item flex-fill p-2">
                <select class="js-example-basic-multiple form-control" name="parcelas[]" multiple="multiple"
                    aria-label="Filtrar por Parcelas">
                    <option value="1">Parcela 1</option>
                    <option value="2">Parcela 2</option>
                    <option value="3">Parcela 3</option>
                </select>
            </div>
        </div>

        <!-- Container para Tabela de Clientes -->
        <div id="cliente-container" class="content">
            <h2>Clientes</h2>
            <div id="accordionClientes">
                <!-- Aqui cada cliente será um "card" dentro do accordion -->
                <div class="card mb-4">
                    <div class="card-header" id="headingCliente1">
                        <h5 class="mb-0">
                            <button class="btn btn-link collapsed toggle-client" data-toggle="collapse" data-target="#collapseCliente1"
                                aria-expanded="false" aria-controls="collapseCliente1">
                                Cliente: Larissa Dias de Melo - Proposta: 123456, Assinatura: 05/09/2024, Status: Aguardando Implantação, Operadora: Amil, Modalidade: Adesão
                            </button>
                        </h5>
                    </div>
                    <div id="collapseCliente1" class="collapse" aria-labelledby="headingCliente1"
                        data-parent="#accordionClientes">
                        <div class="card-body">
                            <table class="table table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Proposta</th>
                                        <th>Assinatura</th>
                                        <th>Segurado</th>
                                        <th>Operadora</th>
                                        <th>Modalidade</th>
                                        <th>Subproduto</th>
                                        <th>Status</th>
                                        <th>Valor</th>
                                        <th>Parcela</th>
                                        <th>% Parcela</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Para cada parcela desse cliente -->
                                    <tr>
                                        <td>123456</td>
                                        <td>05/09/2024</td>
                                        <td>Larissa Dias de Melo</td>
                                        <td>Amil</td>
                                        <td>Adesão</td>
                                        <td>Supermed / Amil Linha Selecionada</td>
                                        <td>Aguardando Implantação</td>
                                        <td>R$ 380,23</td>
                                        <td>1</td>
                                        <td>100%</td>
                                        <td><button class="btn btn-sm btn-warning">Abrir Chamado de Comissão</button></td>
                                    </tr>
                                    <tr>
                                        <td>123456</td>
                                        <td>05/09/2024</td>
                                        <td>Larissa Dias de Melo</td>
                                        <td>Amil</td>
                                        <td>Adesão</td>
                                        <td>Supermed / Amil Linha Selecionada</td>
                                        <td>Aguardando Implantação</td>
                                        <td>R$ 380,23</td>
                                        <td>2</td>
                                        <td>30%</td>
                                        <td><button class="btn btn-sm btn-warning">Abrir Chamado de Comissão</button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Repetir a estrutura acima para outros clientes -->
                <div class="card mb-4">
                    <div class="card-header" id="headingCliente2">
                        <h5 class="mb-0">
                            <button class="btn btn-link collapsed toggle-client" data-toggle="collapse"
                                data-target="#collapseCliente2" aria-expanded="false" aria-controls="collapseCliente2">
                                Cliente: João Silva - Proposta: 654321, Assinatura: 12/10/2024, Status: Implantado, Operadora: Bradesco Saúde, Modalidade: Familiar
                            </button>
                        </h5>
                    </div>
                    <div id="collapseCliente2" class="collapse" aria-labelledby="headingCliente2"
                        data-parent="#accordionClientes">
                        <div class="card-body">
                            <table class="table table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Proposta</th>
                                        <th>Assinatura</th>
                                        <th>Segurado</th>
                                        <th>Operadora</th>
                                        <th>Modalidade</th>
                                        <th>Subproduto</th>
                                        <th>Status</th>
                                        <th>Valor</th>
                                        <th>Parcela</th>
                                        <th>% Parcela</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Para cada parcela desse cliente -->
                                    <tr>
                                        <td>654321</td>
                                        <td>12/10/2024</td>
                                        <td>João Silva</td>
                                        <td>Bradesco Saúde</td>
                                        <td>Familiar</td>
                                        <td>Plano Ouro</td>
                                        <td>Implantado</td>
                                        <td>R$ 450,00</td>
                                        <td>1</td>
                                        <td>100%</td>
                                        <td><button class="btn btn-sm btn-warning">Abrir Chamado de Comissão</button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs para navegação -->
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" id="dados-cliente-tab" data-toggle="tab" href="#dados-cliente" role="tab"
                    aria-controls="dados-cliente" aria-selected="true">Dados do Cliente</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="dados-operadora-tab" data-toggle="tab" href="#dados-operadora" role="tab"
                    aria-controls="dados-operadora" aria-selected="false">Dados da Operadora</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="dados-contrato-tab" data-toggle="tab" href="#dados-contrato" role="tab"
                    aria-controls="dados-contrato" aria-selected="false">Dados do Contrato</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="parcelas-comissionamento-tab" data-toggle="tab" href="#parcelas-comissionamento"
                    role="tab" aria-controls="parcelas-comissionamento" aria-selected="false">Parcelas e
                    Comissionamento</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="dados-pagamento-tab" data-toggle="tab" href="#dados-pagamento" role="tab"
                    aria-controls="dados-pagamento" aria-selected="false">Dados de Pagamento</a>
            </li>
        </ul>
        <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active content" id="dados-cliente" role="tabpanel"
                aria-labelledby="dados-cliente-tab">
                <h2>Dados do Cliente</h2>
                <p><strong>Segurado:</strong> Larissa Dias de Melo</p>
                <p><strong>Documento:</strong> 46436357890</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Valor Produção:</strong> R$ 380,23</p>
                <p><strong>Quantidade de Beneficiários:</strong> 1</p>
                <p><strong>Status:</strong> Aguardando Implantação</p>
                <p><strong>Subproduto:</strong> Supermed / Amil Linha Selecionada SP Online Até 69A 11M 29D - Adesão</p>
                <p><strong>Corretor:</strong> CONSEDE CORRETORA DE SEGUROS</p>
            </div>
            <div class="tab-pane fade content" id="dados-operadora" role="tabpanel"
                aria-labelledby="dados-operadora-tab">
                <h2>Dados da Operadora</h2>
                <p><strong>Nome:</strong> Supermed</p>
                <p><strong>Modalidade:</strong> Adesão</p>
                <p><strong>Produto:</strong> Amil Linha Selecionada - São Paulo</p>
                <p><strong>Entidade:</strong> Associação Brasileira de Benefícios Aos Profissionais Liberais</p>
                <p><strong>Plano:</strong> Bronze</p>
            </div>
            <div class="tab-pane fade content" id="dados-contrato" role="tabpanel" aria-labelledby="dados-contrato-tab">
                <h2>Dados do Contrato</h2>
                <p><strong>Data Produção:</strong> 01/10/2024</p>
                <p><strong>Data Assinatura:</strong> 05/09/2024</p>
                <p><strong>Data Cadastro:</strong> 05/09/2024</p>
                <p><strong>Data Vigência:</strong> 01/11/2024</p>
                <p><strong>Data Protocolo:</strong> 05/09/2024</p>
                <p><strong>Data Implantação:</strong> N/A</p>
            </div>
            <div class="tab-pane fade content" id="parcelas-comissionamento" role="tabpanel"
                aria-labelledby="parcelas-comissionamento-tab">
                <h2>Parcelas e Comissionamento</h2>
                <div id="comissionamento-container"></div>
            </div>
            <div class="tab-pane fade content" id="dados-pagamento" role="tabpanel"
                aria-labelledby="dados-pagamento-tab">
                <h2>Dados de Pagamento</h2>
                <div id="pagamento-container"></div>
            </div>
        </div>
    </div>
    <script>
        document.querySelectorAll('.nav-link').forEach(tab => {
            tab.addEventListener('click', function () {
                const targetId = this.getAttribute('href').substring(1);
                document.querySelectorAll('.tab-pane').forEach(pane => {
                    pane.classList.remove('show', 'active');
                });
                document.getElementById(targetId).classList.add('show', 'active');
                if (targetId === 'parcelas-comissionamento') {
                    renderComissionamento();
                } else if (targetId === 'dados-pagamento') {
                    renderPagamentos();
                }
            });
        });

        const comissionamentoData = [
            { corretor: "BRAZIL HEALTH CONSULTORIA DE BENEFICIOS", ordem: 0, parcela: 1, vlParcela: 380.23, comissao: 1 },
            { corretor: "BRAZIL HEALTH CONSULTORIA DE BENEFICIOS", ordem: 0, parcela: 2, vlParcela: 380.23, comissao: 0.3 },
            { corretor: "CONSEDE CORRETORA DE SEGUROS", ordem: 1, parcela: 1, vlParcela: 380.23, comissao: 1 },
            { corretor: "CONSEDE CORRETORA DE SEGUROS", ordem: 1, parcela: 2, vlParcela: 380.23, comissao: 0.1 }
        ];

        const pagamentoData = [
            { idProducao: 712338, parcela: 1, vlParcela: 380.23, dtVencimento: "2024-09-01", dtRecebimento: "2024-09-01", dtPagamento: "2024-09-01" },
            { idProducao: 712338, parcela: 2, vlParcela: 380.23, dtVencimento: "2024-10-01", dtRecebimento: null, dtPagamento: null }
        ];

        function renderComissionamento() {
            const container = document.getElementById('comissionamento-container');
            container.innerHTML = '';

            const groupedData = comissionamentoData.reduce((acc, curr) => {
                if (!acc[curr.corretor]) acc[curr.corretor] = [];
                acc[curr.corretor].push(curr);
                return acc;
            }, {});

            Object.keys(groupedData).forEach(corretor => {
                const group = groupedData[corretor];
                const corretorDiv = document.createElement('div');
                corretorDiv.classList.add('order-group');
                corretorDiv.innerHTML = `<div class="corretor-header">${corretor}</div>`;

                const table = document.createElement('table');
                table.classList.add('table');
                table.innerHTML = `
                    <thead>
                        <tr><th>Parcela</th><th>Valor Parcela</th><th>Comissão</th></tr>
                    </thead>
                    <tbody>
                        ${group.map(item => `<tr><td>${item.parcela}</td><td>R$ ${item.vlParcela.toFixed(2)}</td><td>${(item.comissao * 100).toFixed(2)}%</td></tr>`).join('')}
                    </tbody>
                `;

                corretorDiv.appendChild(table);
                container.appendChild(corretorDiv);
            });
        }

        function renderPagamentos() {
            const container = document.getElementById('pagamento-container');
            container.innerHTML = '';

            const groupedData = pagamentoData.reduce((acc, curr) => {
                if (!acc[curr.idProducao]) acc[curr.idProducao] = [];
                acc[curr.idProducao].push(curr);
                return acc;
            }, {});

            Object.keys(groupedData).forEach(idProducao => {
                const group = groupedData[idProducao];
                const producaoDiv = document.createElement('div');
                producaoDiv.classList.add('order-group');
                producaoDiv.innerHTML = `<div class="corretor-header">Produção ID: ${idProducao}</div>`;

                const table = document.createElement('table');
                table.classList.add('table');
                table.innerHTML = `
                    <thead>
                        <tr><th>Parcela</th><th>Valor Parcela</th><th>Data Vencimento</th><th>Data Recebimento</th><th>Data Pagamento</th><th>Status</th><th>Ações</th></tr>
                    </thead>
                    <tbody>
                        ${group.map(item => `
                            <tr class="${item.dtPagamento ? 'paid' : 'unpaid'}">
                                <td>${item.parcela}</td>
                                <td>R$ ${item.vlParcela.toFixed(2)}</td>
                                <td>${item.dtVencimento || 'N/A'}</td>
                                <td>${item.dtRecebimento || 'N/A'}</td>
                                <td>${item.dtPagamento || 'N/A'}</td>
                                <td>${item.dtPagamento ? 'Pago' : 'Pendente'}</td>
                                <td>${!item.dtPagamento ? '<button class="btn btn-sm btn-warning">Abrir Chamado de Comissão</button>' : ''}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                `;

                producaoDiv.appendChild(table);
                container.appendChild(producaoDiv);
            });
        }
    </script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function () {
            $('.js-example-basic-multiple').each(function () {
                const ariaLabel = $(this).attr('aria-label');
                $(this).select2({
                    placeholder: ariaLabel,
                    allowClear: true,
                    width: '100%'  // Ajusta a largura para 100% da div pai
                });
            });

            // Inicialização dos componentes do accordion
            $('#accordionClientes').on('shown.bs.collapse', function (e) {
                var offset = $('.card .collapse.show').offset();
                if (offset) {
                    $('html,body').animate({
                        scrollTop: $('.card .collapse.show').offset().top - 100
                    }, 500);
                }
            });
        });
    </script>

</body>

</html>
