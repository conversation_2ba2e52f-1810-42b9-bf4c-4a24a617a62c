{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}comissoes{% endblock %}


{% block content %}
<div class="comissoes-container">
    <div class="comissoes-header">
        <h2>Tabela de Comissão</h2>
        <button id="clearFilterBtn">Limpar Filtro</button>
    </div>
    <div>
        <div class="search-comissoes-container">
            <input type="text" id="searchInput" placeholder="Buscar por Operadora, Modalidade ou Grupo"
                onkeyup="searchTable()">

            <div class="filter-container">
                <label for="filter-operadora">Filtrar por Operadora:</label>
                <select id="filter-operadora" onchange="filterTable()">
                    <option value="">Todas</option>
                    <!-- Opções serão carregadas via JS -->
                </select>
            </div>

            <div class="filter-container">
                <label for="filter-modalidade">Filtrar por Modalidade:</label>
                <select id="filter-modalidade" onchange="filterTable()">
                    <option value="">Todas</option>
                    <!-- Opções serão carregadas via JS -->
                </select>
            </div>
        </div>
        <div id="table-container-comissoes">
            <table class="comissoes-table">
                <thead>
                    <tr>
                        <th>Operadora <button class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th>Modalidade <button class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th>Grupo <button class="filter-button"><i class="bi bi-filter"></i></button></th>
                    </tr>
                </thead>
                <tbody>
                    {% for comissoes in listar_comissoes %}
                    <tr class="clickable-row" data-id="{{ comissoes.grupo }}">
                        <td data-label="operadora">{{ comissoes.operadora }}</td>
                        <td data-label="modalidade">{{ comissoes.modalidade }}</td>
                        <td data-label="grupo">{{ comissoes.grupo }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Estrutura -->
<div id="comissoesModal" class="comissoes-modal">
    <div class="comissoes-modal-content">
        <div class="comissoes-modal-header">
            <h2>Detalhes da Comissão</h2>
            <button id="closeModalBtn"><i class="fa-solid fa-circle-xmark"></i></button>
        </div>
        <div class="comissoes-modal-body">
            <label for="gradeSelect">Selecione a Grade:</label>
            <select id="gradeSelect">
                <!-- Options serão carregadas via JS -->
            </select>
            <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                <p>Carregando...</p>
            </div>
            <div id="comissoesTableContainer" style="display: none; overflow-x: auto;">
                <table id="comissoesTable" class="comissoes-table">
                    <thead>
                        <tr>
                            <th>Comissionáveis</th>
                            <th>Totais</th>
                            <!-- Parcelas dinâmicas -->
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Linhas da tabela serão inseridas via JS -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/comissoes.js') }}"></script>
{% endblock %}