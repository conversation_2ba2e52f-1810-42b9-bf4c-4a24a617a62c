{% extends "base.html" %}

{% block title %}Administração - Intranet | BrazilHealth{% endblock %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-sort.css') }}">
{% endblock %}

{% block body_class %}admin{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="admin-header">
        <h2>Administrador</h2>
        <button id="clearFilterBtn">Limpar Filtro</button>
    </div>
    <div>
        <div class="search-admin-container">
            <input type="text" id="searchInput" placeholder="Buscar por ID, Nome, CPF ou E-mail" onkeyup="searchTable()">
        </div>
        <div id="table-container-admin">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>ID <button class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th>Perfil <button class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th>Nome <button class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th>Data de Nascimento <button class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th>Telefone <button class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th>CPF <button class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th>E-mail <button class="filter-button"><i class="bi bi-filter"></i></button></th>
                        <th>E-mail Confirmado? <button class="filter-button"><i class="bi bi-filter"></i></button></th>
                    </tr>
                </thead>
                <tbody>
                    {% for usuario in usuarios %}
                    <tr class="clickable-row" data-id="{{ usuario.id }}">
                        <td data-label="ID">{{ usuario.id }}</td>
                        <td data-label="tipo_usuario">{{ usuario.tipo_usuario }}</td>
                        <td data-label="nome">{{ usuario.nome }}</td>
                        <td data-label="data_nascimento">{{ usuario.data_nascimento }}</td>
                        <td data-label="telefone">{{ usuario.telefone }}</td>
                        <td data-label="cpf">{{ usuario.cpf }}</td>
                        <td data-label="email">{{ usuario.email }}</td>
                        <td data-label="email_confirmed">{{ usuario.email_confirmed }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            <button id="clearFilterBtn">Limpar Filtro</button>
        </div>
        <div class="pagination">
            <div id="paginas">
                {% if pagina_atual > 1 %}
                <a class="pass-pag" href="?pagina={{ pagina_atual - 1 }}{% if ordenar_por %}&ordenar_por={{ ordenar_por }}{% endif %}{% if direcao %}&direcao={{ direcao }}{% endif %}{% if filtro_coluna %}&filtro_coluna={{ filtro_coluna }}{% endif %}{% if filtro_valor %}&filtro_valor={{ filtro_valor }}{% endif %}"><i class="bi bi-chevron-left"></i> Anterior</a>
                {% else %}
                <span class="pass-pag" style="opacity: 0.5; cursor: not-allowed;"><i class="bi bi-chevron-left"></i> Anterior</span>
                {% endif %}

                <div class="pagination-numbers">
                    {% set start_page = [pagina_atual - 3, 1]|max %}
                    {% set end_page = [pagina_atual + 3, total_de_paginas]|min %}

                    {% if start_page > 1 %}
                        <a href="?pagina=1{% if ordenar_por %}&ordenar_por={{ ordenar_por }}{% endif %}{% if direcao %}&direcao={{ direcao }}{% endif %}{% if filtro_coluna %}&filtro_coluna={{ filtro_coluna }}{% endif %}{% if filtro_valor %}&filtro_valor={{ filtro_valor }}{% endif %}">1</a>
                        {% if start_page > 2 %}
                            <span class="pagination-ellipsis">...</span>
                        {% endif %}
                    {% endif %}

                    {% for p in range(start_page, end_page + 1) %}
                        {% if p == pagina_atual %}
                            <span class="current-page">{{ p }}</span>
                        {% else %}
                            <a href="?pagina={{ p }}{% if ordenar_por %}&ordenar_por={{ ordenar_por }}{% endif %}{% if direcao %}&direcao={{ direcao }}{% endif %}{% if filtro_coluna %}&filtro_coluna={{ filtro_coluna }}{% endif %}{% if filtro_valor %}&filtro_valor={{ filtro_valor }}{% endif %}">{{ p }}</a>
                        {% endif %}
                    {% endfor %}

                    {% if end_page < total_de_paginas %}
                        {% if end_page < total_de_paginas - 1 %}
                            <span class="pagination-ellipsis">...</span>
                        {% endif %}
                        <a href="?pagina={{ total_de_paginas }}{% if ordenar_por %}&ordenar_por={{ ordenar_por }}{% endif %}{% if direcao %}&direcao={{ direcao }}{% endif %}{% if filtro_coluna %}&filtro_coluna={{ filtro_coluna }}{% endif %}{% if filtro_valor %}&filtro_valor={{ filtro_valor }}{% endif %}">{{ total_de_paginas }}</a>
                    {% endif %}
                </div>

                {% if pagina_atual < total_de_paginas %}
                <a class="pass-pag" href="?pagina={{ pagina_atual + 1 }}{% if ordenar_por %}&ordenar_por={{ ordenar_por }}{% endif %}{% if direcao %}&direcao={{ direcao }}{% endif %}{% if filtro_coluna %}&filtro_coluna={{ filtro_coluna }}{% endif %}{% if filtro_valor %}&filtro_valor={{ filtro_valor }}{% endif %}">Próximo <i class="bi bi-chevron-right"></i></a>
                {% else %}
                <span class="pass-pag" style="opacity: 0.5; cursor: not-allowed;">Próximo <i class="bi bi-chevron-right"></i></span>
                {% endif %}
            </div>
            <button id="exportExcelBtn">Exportar para Excel</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/admin.js') }}"></script>
<script src="{{ url_for('static', filename='js/script.js') }}"></script>
{% endblock %}
