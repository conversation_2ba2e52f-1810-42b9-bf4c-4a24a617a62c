<!DOCTYPE html>
<html lang="pt">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro de Empresas</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/css/adminlte.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        /* Adiciona um espaçamento inferior para os cards primários */
        .card-primary {
            margin-bottom: 20px;
        }
    </style>
</head>

<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <div class="content-wrapper">
            <div class="container">
                <!-- Cabeçalho da página -->
                <section class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1>Gestão de Empresas</h1>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Conteúdo principal -->
                <section class="content">
                    <div class="container-fluid">
                        <!-- Card principal para gerenciamento -->
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Gerenciar Empresas</h3>
                            </div>
                            <div class="card-body">
                                <!-- Abas de navegação -->
                                <ul class="nav nav-tabs" id="tabEmpresas" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" id="tab-cadastrar-empresa" data-toggle="tab"
                                            href="#cadastrar-empresa" role="tab" aria-controls="cadastrar-empresa"
                                            aria-selected="true">
                                            <i class="fas fa-plus-circle"></i> Cadastrar Empresa
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="tab-listar-empresas" data-toggle="tab"
                                            href="#listar-empresas" role="tab" aria-controls="listar-empresas"
                                            aria-selected="false">
                                            <i class="fas fa-list"></i> Empresas Cadastradas
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content" id="tabContentEmpresas">
                                    <!-- Aba para cadastrar nova empresa -->
                                    <div class="tab-pane fade show active" id="cadastrar-empresa" role="tabpanel"
                                        aria-labelledby="tab-cadastrar-empresa">
                                        <div class="card mt-3">
                                            <div class="card-header bg-primary text-white">
                                                <h3 class="card-title">Cadastrar Nova Empresa</h3>
                                            </div>
                                            <!-- Formulário para cadastrar empresa -->
                                            <form id="form-empresas">
                                                <div class="card-body">
                                                    <!-- Linha com campos Nome da Empresa e CNPJ -->
                                                    <div class="row mt-3">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label for="empresa">Nome da Empresa</label>
                                                                <input type="text" class="form-control" id="empresa"
                                                                    name="empresa"
                                                                    placeholder="Digite o nome da empresa" required>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label for="cnpjEmpresa">CNPJ</label>
                                                                <input type="text" class="form-control" id="cnpjEmpresa"
                                                                    name="cnpjEmpresa"
                                                                    placeholder="Digite o CNPJ da empresa" required>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Linha com campos Endereço e Telefone -->
                                                    <div class="row mt-3">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label for="enderecoEmpresa">Endereço</label>
                                                                <input type="text" class="form-control"
                                                                    id="enderecoEmpresa" name="enderecoEmpresa"
                                                                    placeholder="Digite o endereço da empresa" required>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label for="telefoneEmpresa">Telefone</label>
                                                                <input type="text" class="form-control"
                                                                    id="telefoneEmpresa" name="telefoneEmpresa"
                                                                    placeholder="Digite o telefone da empresa" required>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Campo de Email -->
                                                    <div class="row mt-3">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label for="emailEmpresa">Email</label>
                                                                <input type="email" class="form-control"
                                                                    id="emailEmpresa" name="emailEmpresa"
                                                                    placeholder="Digite o email da empresa" required>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Campo de Observações -->
                                                    <div class="row mt-3">
                                                        <div class="col-md-12">
                                                            <div class="form-group">
                                                                <label for="observacoes">Observações</label>
                                                                <textarea class="form-control" id="observacoes"
                                                                    name="observacoes" rows="3"
                                                                    placeholder="Adicione quaisquer observações relevantes"></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- Botões de ação -->
                                                <div class="card-footer text-right">
                                                    <button id="botaoCadastrarEmpresa" type="button"
                                                        class="btn btn-primary">Cadastrar</button>
                                                    <button type="reset" class="btn btn-secondary">Limpar</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>

                                    <!-- Aba para listar empresas cadastradas -->
                                    <div class="tab-pane fade" id="listar-empresas" role="tabpanel"
                                        aria-labelledby="tab-listar-empresas">
                                        <div class="card mt-3">
                                            <div class="card-header bg-secondary text-white">
                                                <h3 class="card-title">Empresas Cadastradas</h3>
                                            </div>
                                            <div class="card-body">
                                                <!-- Filtro por Nome gerado via JS -->
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <select id="filtro-nome-empresa" class="form-control">
                                                                <option value="">Todas as Empresas</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- Tabela para exibir empresas -->
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>Nome</th>
                                                            <th>CNPJ</th>
                                                            <th>Endereço</th>
                                                            <th>Telefone</th>
                                                            <th>Email</th>
                                                            <th>Observações</th>
                                                            <th>Ações</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="empresas-list">
                                                        <!-- Preenchido via JavaScript -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>



    <!-- JQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE JS -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/js/adminlte.min.js"></script>
    <script src="/static/js/contas_home.js"></script>
    <!-- Script personalizado -->
    <script>
        $(document).ready(function () {
            // Aplica máscaras ao campo de CNPJ
            $('#cnpjEmpresa, #editar-cnpj-empresa').on('input', function () {
                this.value = this.value.replace(/\D/g, '').replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
            });

            // Aplica máscaras ao campo de Telefone
            $('#telefoneEmpresa, #editar-telefone-empresa').on('input', function () {
                this.value = this.value.replace(/\D/g, '').replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
            });

            // Verifica se o Bootstrap está carregado corretamente
            if (typeof $.fn.modal === 'undefined') {
                console.error('Bootstrap modal não está disponível. Verifique se o Bootstrap está carregado corretamente.');
            } else {
                console.log('Bootstrap modal está disponível.');
            }
        });
    </script>


</body>

</html>