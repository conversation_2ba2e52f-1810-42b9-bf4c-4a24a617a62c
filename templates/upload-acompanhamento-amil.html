{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}uploadAcompanhamento{% endblock %}

{% block head_extra %}
<style>
    #mensagem-sucesso {
        width: 100%;
        padding: 15px;
        margin-bottom: 20px;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="uploadTabela-container">
    <h1 class="upload-h1">Gestão - Acompanhamento de Vendas - Amil</h1>

    <!-- Mensagem de sucesso -->
    <div id="mensagem-sucesso" class="alert alert-success" style="display: none;">
        Upload realizado com sucesso!
    </div>

    <!-- Formulário de Upload -->
    <div class="upload-container">
        <form id="upload-form" action="/upload_acompanhamento" method="post" enctype="multipart/form-data">
            <h3 class="upload-h3">Faça o upload do acompanhamento de vendas da Amil.</h3>
            <div class="form-group">
                <label for="modalidade">Modalidade:</label>
                <select name="modalidade" id="modalidade">
                    <option value="PME">PME</option>
                    <option value="PF">PF</option>
                </select>
            </div>
            <div class="form-group">
                <label for="file">Arquivo Excel:</label>
                <input type="file" id="file" name="file" class="form-control" required>
            </div>
            <button type="submit" class="btn btn-primary">Upload</button>
        </form>
    </div>

    <div id="loadingScreen" class="loading-screen" style="display: none;">
        <div class="spinner"></div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/upload-acompanhamento-amil.js') }}"></script>
{% endblock %}