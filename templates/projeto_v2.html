{% extends "base.html" %}

{% block title %}Novo Projeto V2{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h2>Novo Projeto</h2>
            <p class="text-muted">Sistema de priorização inteligente</p>
        </div>
        <div class="card-body">
            <form id="projetoForm" class="needs-validation" novalidate>
                <!-- Nome e Descrição -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="nome">Nome do Projeto</label>
                            <input type="text" class="form-control" id="nome" required>
                            <div class="invalid-feedback">
                                Por favor, insira o nome do projeto.
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="descricao">Descrição</label>
                            <textarea class="form-control" id="descricao" rows="3" required></textarea>
                            <div class="invalid-feedback">
                                Por favor, insira uma descrição.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Métricas de Priorização -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="urgencia">Urgência (1-5)</label>
                            <select class="form-control" id="urgencia" required>
                                <option value="">Selecione</option>
                                <option value="1">1 - Muito Baixa</option>
                                <option value="2">2 - Baixa</option>
                                <option value="3">3 - Média</option>
                                <option value="4">4 - Alta</option>
                                <option value="5">5 - Muito Alta</option>
                            </select>
                            <small class="form-text text-muted">Quão urgente é este projeto?</small>
                            <div class="invalid-feedback">
                                Por favor, selecione o nível de urgência.
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="valor">Valor para o Negócio (1-5)</label>
                            <select class="form-control" id="valor" required>
                                <option value="">Selecione</option>
                                <option value="1">1 - Muito Baixo</option>
                                <option value="2">2 - Baixo</option>
                                <option value="3">3 - Médio</option>
                                <option value="4">4 - Alto</option>
                                <option value="5">5 - Muito Alto</option>
                            </select>
                            <small class="form-text text-muted">Qual o valor que este projeto agrega?</small>
                            <div class="invalid-feedback">
                                Por favor, selecione o valor para o negócio.
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="risco">Risco (1-5)</label>
                            <select class="form-control" id="risco" required>
                                <option value="">Selecione</option>
                                <option value="1">1 - Muito Baixo</option>
                                <option value="2">2 - Baixo</option>
                                <option value="3">3 - Médio</option>
                                <option value="4">4 - Alto</option>
                                <option value="5">5 - Muito Alto</option>
                            </select>
                            <small class="form-text text-muted">Qual o nível de risco envolvido?</small>
                            <div class="invalid-feedback">
                                Por favor, selecione o nível de risco.
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="complexidade">Complexidade (1-5)</label>
                            <select class="form-control" id="complexidade" required>
                                <option value="">Selecione</option>
                                <option value="1">1 - Muito Baixa</option>
                                <option value="2">2 - Baixa</option>
                                <option value="3">3 - Média</option>
                                <option value="4">4 - Alta</option>
                                <option value="5">5 - Muito Alta</option>
                            </select>
                            <small class="form-text text-muted">Qual a complexidade de implementação?</small>
                            <div class="invalid-feedback">
                                Por favor, selecione o nível de complexidade.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custo -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="custo">Custo (1-5)</label>
                            <select class="form-control" id="custo" required>
                                <option value="">Selecione</option>
                                <option value="1">1 - Muito Baixo</option>
                                <option value="2">2 - Baixo</option>
                                <option value="3">3 - Médio</option>
                                <option value="4">4 - Alto</option>
                                <option value="5">5 - Muito Alto</option>
                            </select>
                            <small class="form-text text-muted">Qual o custo estimado do projeto?</small>
                            <div class="invalid-feedback">
                                Por favor, selecione o nível de custo.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Prioridade Calculada -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="alert alert-info" role="alert">
                            <strong>Prioridade Calculada: </strong><span id="prioridadeCalculada">-</span>
                            <br>
                            <small>Fórmula: [(Urgência * 3) + (Valor * 2) + (Risco * 1.5)] / [(Complexidade * 2) + (Custo * 1.5)]</small>
                        </div>
                    </div>
                </div>

                <!-- Responsável e Datas -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="responsavel">Responsável</label>
                            <input type="text" class="form-control" id="responsavel" required>
                            <div class="invalid-feedback">
                                Por favor, insira o responsável.
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="data_inicio">Data de Início</label>
                            <input type="date" class="form-control" id="data_inicio" required>
                            <div class="invalid-feedback">
                                Por favor, selecione a data de início.
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="data_fim">Data de Término Prevista</label>
                            <input type="date" class="form-control" id="data_fim" required>
                            <div class="invalid-feedback">
                                Por favor, selecione a data de término.
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">Criar Projeto</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('projetoForm');
    const campos = ['urgencia', 'valor', 'risco', 'complexidade', 'custo'];
    
    // Função para calcular a prioridade
    function calcularPrioridade() {
        const valores = {};
        let todosPreenchidos = true;
        
        campos.forEach(campo => {
            const valor = parseInt(document.getElementById(campo).value);
            if (isNaN(valor)) {
                todosPreenchidos = false;
            }
            valores[campo] = valor;
        });
        
        if (todosPreenchidos) {
            const numerador = (valores.urgencia * 3) + (valores.valor * 2) + (valores.risco * 1.5);
            const denominador = (valores.complexidade * 2) + (valores.custo * 1.5);
            const prioridade = numerador / denominador;
            document.getElementById('prioridadeCalculada').textContent = prioridade.toFixed(2);
        }
    }
    
    // Adicionar listeners para recalcular a prioridade
    campos.forEach(campo => {
        document.getElementById(campo).addEventListener('change', calcularPrioridade);
    });
    
    // Validação do formulário
    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        if (!form.checkValidity()) {
            event.stopPropagation();
            form.classList.add('was-validated');
            return;
        }
        
        // Coletar dados do formulário
        const dados = {
            nome: document.getElementById('nome').value,
            descricao: document.getElementById('descricao').value,
            urgencia: parseInt(document.getElementById('urgencia').value),
            valor_negocio: parseInt(document.getElementById('valor').value),
            risco: parseInt(document.getElementById('risco').value),
            complexidade: parseInt(document.getElementById('complexidade').value),
            custo: parseInt(document.getElementById('custo').value),
            responsavel: document.getElementById('responsavel').value,
            data_inicio: document.getElementById('data_inicio').value,
            data_fim: document.getElementById('data_fim').value
        };
        
        // Enviar dados para o servidor
        fetch('/api/projeto_v2', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(dados)
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                alert('Projeto criado com sucesso!');
                window.location.href = '/projetos_v2';
            } else {
                alert('Erro ao criar projeto: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao criar projeto. Por favor, tente novamente.');
        });
    });
});
</script>
{% endblock %} 