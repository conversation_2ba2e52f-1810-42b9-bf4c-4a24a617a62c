{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}ranking{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.1.0/css/buttons.dataTables.min.css">
<link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.15/css/bootstrap-multiselect.css">
<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
{% endblock %}

{% block content %}
<div class="ranking-container">
    <h1 id="ranking-name">Ranking por Assistente</h1>
    <div id="last-update" class="text-muted mb-3 font-weight-bold"></div>
    <div class="form-group">
        <label for="monthYearPicker">Selecione Mês/Ano</label>
        <select id="monthYearPicker" multiple="multiple" class="form-control"></select>
    </div>
    <table id="rankingTable" class="display" style="width:100%">
        <thead class="ranking-header">
            <tr>
                <th class="ranking-column-header">Assistente</th>
                <th class="ranking-column-header">Total</th>
                <th class="ranking-column-header">Vidas</th>
                <th class="ranking-column-header">Corretores</th>
                <th class="ranking-column-header">Contratos</th>
                <th class="ranking-column-header">Média Contrato</th>
                <th class="ranking-column-header">Ticket Média</th>
            </tr>
        </thead>
        <tbody class="ranking-body">
            <!-- Dados serão inseridos aqui -->
        </tbody>
        <tfoot class="ranking-footer">
            <tr>
                <th class="ranking-column-footer">Total Geral</th>
                <th class="ranking-column-footer"></th>
                <th class="ranking-column-footer"></th>
                <th class="ranking-column-footer"></th>
                <th class="ranking-column-footer"></th>
                <th class="ranking-column-footer"></th>
                <th class="ranking-column-footer"></th>
            </tr>
        </tfoot>
    </table>
    <div id="buttons-container" class="mt-3">
        <!-- Os botões serão renderizados aqui pelo DataTables -->
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.1.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.1.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.1.0/js/buttons.print.min.js"></script>
<script
    src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.15/js/bootstrap-multiselect.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/ranking-assist.js') }}"></script>
{% endblock %}