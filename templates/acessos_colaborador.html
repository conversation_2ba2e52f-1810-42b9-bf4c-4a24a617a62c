<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acessos Operadoras</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<div id="alertBox" class="alert d-none" role="alert">
    <!-- Mensagem de alerta será inserida aqui -->
</div>

<body class="acessos-colaborador">
    <div class="acessos-colaborador-container">
        <button id="clearFilterBtn">Limpar Filtro</button>

        <div class="search-acessos-container">
            <input type="text" id="searchInput" placeholder="Buscar por Nome, Login, Unidade ou Código"
                onkeyup="searchTable()">
        </div>

        <div id="table-container-acessos">
            <table class="acessos-colaborador-table table table-striped">
                <thead>
                    <tr>
                        <th id="nome-col" class="column-nome">Nome <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="link-col" class="column-link">Link <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="login-col" class="column-login">Login <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="senha-col" class="column-senha">Senha <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="unidade-col" class="column-unidade">Unidade/Empresa <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="modalidade-col" class="column-modalidade">Modalidade <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="codigo-col" class="column-codigo">Código <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                        <th id="outros-col" class="column-outros">Outros <button class="filter-button"><i
                                    class="bi bi-filter"></i></button></th>
                    </tr>
                </thead>
                <tbody>
                    {% for acessos in listar_acessos %}
                    <tr>
                        <td data-label="nome" class="column-nome">{{ acessos.nome }}</td>
                        <td data-label="link" class="column-link">
                            <a href="{{ acessos.link }}" class="link-acessos" target="_blank">{{ acessos.link }}</a>
                        </td>
                        <td data-label="login" class="column-login">{{ acessos.login }}</td>
                        <td data-label="senha" class="column-senha">
                            <span class="senha-text" data-password="{{ acessos.senha }}">***********</span>
                            <i class="fa-solid fa-copy" onclick="copyPassword(this)"></i>
                            <i class="fa-solid fa-eye" onclick="togglePasswordVisibility(this)"></i>
                            <i class="fa-solid fa-pen-to-square" onclick="openPasswordModal('{{ acessos.id }}')"></i>
                        </td>
                        <td data-label="unidade" class="column-unidade">{{ acessos.unidade }}</td>
                        <td data-label="modalidade" class="column-modalidade" title="{{ acessos.modalidade }}">{{
                            acessos.modalidade }}</td>
                        <td data-label="codigo" class="column-codigo" title="{{ acessos.codigo }}">{{ acessos.codigo }}
                        </td>
                        <td data-label="outros" class="column-outros" title="{{ acessos.outros }}">{{ acessos.outros }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

    </div>
    <script src="{{ url_for('static', filename='js/acessos_colaborador.js') }}"></script>
</body>

</html>