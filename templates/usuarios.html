{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}usuarios{% endblock %}

{% block content %}
<div class="usuarios-container">
    <div class="usuarios-header">
        <h2>Alteração Cadastral</h2>
    </div>
    <div class="picture-profile-content">
        <img src="{{ usuario.profile_image_url }}" alt="Imagem de Perfil" id="profile-image">
        <input type="file" id="profile_image" name="profile_image" accept="image/*">
    </div>
    <table class="usuarios-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nome</th>
                <th>Data de Nascimento</th>
                <th>Telefone</th>
                <th>CPF</th>
                <th>E-mail</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>{{ usuario.id }}</td>
                <td><input type="text" name="nome" value="{{ usuario.nome }}" class="nome-input" /></td>
                <td>{{ usuario.data_nascimento }}</td>
                <td><input type="text" name="telefone" value="{{ usuario.telefone }}" class="telefone-input"
                        oninput="aplicarMascaraTelefone(this)" /></td>
                <td>{{ usuario.cpf }}</td>
                <td><input type="email" name="email" value="{{ usuario.email }}" class="email-input" /></td>
                <td><a href="#" class="atualizar-link" data-user-id="{{ usuario.id }}">Atualizar</a></td>
            </tr>
        </tbody>
    </table>
</div>
<script src="{{ url_for('static', filename='js/update-users.js') }}"></script>
{% endblock %}