{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}gerenciais{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.0/css/all.min.css">
<link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
<style>
    .gerenciais-content {
        width: 90%;
        height: 70%;
        margin-left: 5%;
    }
</style>

{% endblock %}

{% block content %}
<div id="gerenciais-container">
    <div class="gerenciais-content">
        <div class="card-container">
            {% if session.get('user_type') in [1, 7] %}
            <div class="card" name="ranking-assistente">
                <i class="material-symbols-outlined">leaderboard</i>
                <h3>Ranking por Assistente</h3>
                <a href="/ranking" class="btn-acessar">Acessar</a>
            </div>
            {% elif session.get('user_type') == 3 %}
            <div class="card" name="ranking-assistente">
                <i class="material-symbols-outlined">leaderboard</i>
                <h3>Ranking por Assistente</h3>
                <a href="/breve" class="btn-acessar">Acessar</a>
            </div>
            {% elif session.get('user_type') == 4 %}
            <div class="card" name="ranking-assistente">
                <i class="material-symbols-outlined">leaderboard</i>
                <h3>Ranking por Assistente</h3>
                <a href="/ranking-individual" class="btn-acessar">Acessar</a>
            </div>
            {% endif %}

            {% if session.get('user_type') in [1, 7] %}
            <div class="card" name="ranking-empresarial">
                <i class="material-symbols-outlined">leaderboard</i>
                <h3>Ranking Empresarial</h3>
                <a href="/ranking-empresarial" class="btn-acessar">Acessar</a>
            </div>
            {% elif session.get('user_type') == 3 %}
            <div class="card" name="ranking-empresarial">
                <i class="material-symbols-outlined">leaderboard</i>
                <h3>Ranking Empresarial</h3>
                <a href="/breve" class="btn-acessar">Acessar</a>
            </div>
            {% elif session.get('user_type') == 4 %}
            <div class="card" name="ranking-empresarial">
                <i class="material-symbols-outlined">leaderboard</i>
                <h3>Ranking Empresarial</h3>
                <a href="/ranking-individual-empresarial" class="btn-acessar">Acessar</a>
            </div>
            {% endif %}

            {% if session.get('user_type') in [1, 7] %}
            <div class="card" name="ranking-operadora">
                <i class="material-symbols-outlined">leaderboard</i>
                <h3>Ranking por Operadora</h3>
                <a href="/ranking-operadora" class="btn-acessar">Acessar</a>
            </div>
            {% endif %}

            {% if session.get('user_type') in [1, 2, 7] %}
            <div class="card" name="ranking-unidades">
                <i class="material-symbols-outlined">leaderboard</i>
                <h3>Ranking Unidades</h3>
                <a href="/ranking-unidades" class="btn-acessar">Acessar</a>
            </div>
            {% endif %}

            {% if session.get('user_type') in [1, 7] %}
            <div class="card" name="ranking-comp-mes">
                <i class="material-symbols-outlined">stacked_line_chart</i>
                <h3>Ranking Comparativo - Mensal</h3>
                <a href="/ranking-comparativo-mes" class="btn-acessar">Acessar</a>
            </div>
            <div class="card" name="ranking-comp-ano">
                <i class="material-symbols-outlined">stacked_line_chart</i>
                <h3>Ranking Comparativo - Ano Anterior</h3>
                <a href="/ranking-comparativo-anual" class="btn-acessar">Acessar</a>
            </div>
            {% elif session.get('user_type') == 3 %}
            <div class="card" name="ranking-comp-mes">
                <i class="material-symbols-outlined">stacked_line_chart</i>
                <h3>Ranking Comparativo - Mensal</h3>
                <a href="/breve" class="btn-acessar">Acessar</a>
            </div>
            {% elif session.get('user_type') == 4 %}
            <div class="card" name="ranking-comp-mes">
                <i class="material-symbols-outlined">stacked_line_chart</i>
                <h3>Ranking Comparativo - Mensal</h3>
                <a href="/ranking-comp-mes-individual" class="btn-acessar">Acessar</a>
            </div>
            <div class="card" name="ranking-comp-ano">
                <i class="material-symbols-outlined">stacked_line_chart</i>
                <h3>Ranking Comparativo - Ano Anterior</h3>
                <a href="/ranking-comp-anual-individual" class="btn-acessar">Acessar</a>
            </div>
            {% endif %}

            {% if session.get('user_type') in [1, 7] %}
            <div class="card" name="ranking-op-ass">
                <i class="material-symbols-outlined">leaderboard</i>
                <h3>Ranking Operadora x Assistente</h3>
                <a href="/ranking-op-x-ass" class="btn-acessar">Acessar</a>
            </div>
            <div class="card" name="ranking-op-cor">
                <i class="material-symbols-outlined">leaderboard</i>
                <h3>Ranking Operadora x Corretor</h3>
                <a href="/ranking-op-x-cor" class="btn-acessar">Acessar</a>
            </div>
            <div class="card" name="ranking-campanha">
                <i class="fa-solid fa-jet-fighter"></i>
                <h3>Ranking ASES de Vendas</h3>
                <a href="/ases" class="btn-acessar">Acessar</a>
            </div>
            {% endif %}

            {% if session.get('user_type') in [1, 4, 7] %}
            <div class="card" name="ranking-campanha-amil">
                <i class="fa-solid fa-magnifying-glass-chart"></i>
                <h3>Acompanhamento de Vendas - Amil</h3>
                <a href="/gerenciais/acompanhamento/amil" class="btn-acessar">Acessar</a>
            </div>
            {% endif %}

            {% if session.get('user_type') in [1, 7] %}
            <div class="card" name="ranking-metas">
                <i class="bi bi-speedometer2"></i>
                <h3>Metas</h3>
                <a href="/breve" class="btn-acessar">Acessar</a>
            </div>
            {% elif session.get('user_type') in [2, 3] %}
            <div class="card" name="ranking-metas">
                <i class="bi bi-speedometer2"></i>
                <h3>Metas</h3>
                <a href="/metas-superintendente" class="btn-acessar">Acessar</a>
            </div>
            {% elif session.get('user_type') == 4 %}
            <div class="card" name="ranking-metas">
                <i class="bi bi-speedometer2"></i>
                <h3>Metas</h3>
                <a href="/metas-individuais" class="btn-acessar">Acessar</a>
            </div>
            {% endif %}

            {% if session.get('user_type') in [1, 7] %}
            <div class="card" name="ranking-metas-vidas">
                <i class="bi bi-people-fill"></i>
                <h3>Metas Por Vidas</h3>
                <a href="/metas-vidas" class="btn-acessar">Acessar</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/gerenciais.js') }}"></script>
{% endblock %}