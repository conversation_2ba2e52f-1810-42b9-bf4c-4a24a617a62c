{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}seletivo{% endblock %}
{% block content %}

<div class="container mt-4">
    <h2>Criar Novo Processo Seletivo Interno</h2>
    <form id="seletivo-form" action="{{ url_for('novo_processo_seletivo') }}" method="post">
        <div class="form-group-seletivo">
            <label for="titulo">Título:</label>
            <input type="text" class="form-control" id="titulo" name="titulo" required>
        </div>

        <div class="form-group-seletivo">
            <label for="descricao">Descrição da Vaga:</label>
            <textarea class="form-control" id="descricao" name="descricao" rows="10" maxlength="5000" required></textarea>
            <small id="conteudo-contador" class="form-text text-muted">0/5000</small>
        </div>

        <div class="form-group-seletivo">
            <label for="requisitos">Requisitos:</label>
            <input type="text" class="form-control" id="requisitos" name="requisitos" required>
        </div>

        <div class="form-group-seletivo">
            <label for="data-inicio">Data de Início:</label>
            <input type="date" class="form-control" id="data-inicio" name="data_inicio" required>
        </div>

        <div class="form-group-seletivo">
            <label for="data-fim">Data de Término:</label>
            <input type="date" class="form-control" id="data-fim" name="data_fim" required>
        </div>

        <button id="publicar-inform" type="submit" class="btn btn-primary">Publicar Vaga</button>
    </form>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>
<script src="{{ url_for('static', filename='js/criar_processo_seletivo.js') }}"></script>

{% endblock %}