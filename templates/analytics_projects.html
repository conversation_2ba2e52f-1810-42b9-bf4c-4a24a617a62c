{% extends "projects_base.html" %}
{% block title %}Dashboard Executivo - Projetos e Tarefas{% endblock %}

{% block content %}
<div class="container-fluid py-4">

    <!-- Título -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h2">Dashboard Executivo</h2>
        <a href="{{ url_for('projects_dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Voltar ao Painel
        </a>
    </div>

    <!-- Filtros -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label class="form-label">Período</label>
                    <input type="month" class="form-control">
                </div>
                <div class="col-md-3">
                    <label>Responsável</label>
                    <select class="form-select">
                        <option selected>Todos</option>
                        {% for user in users %}
                        <option>{{ user.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-primary">Aplicar Filtros</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Sessão: KPIs de Projetos -->
    <h5 class="mb-3">Indicadores de Projetos</h5>
    <div class="row text-center mb-4">
        <div class="col-md-3">
            <div class="card bg-info text-white shadow-sm">
                <div class="card-body">
                    <h6>Projetos Abertos</h6>
                    <span class="h2">{{ total_projetos_abertos }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark shadow-sm">
                <div class="card-body">
                    <h6>Em Progresso</h6>
                    <span class="h2">{{ total_projetos_progresso }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white shadow-sm">
                <div class="card-body">
                    <h6>Concluídos</h6>
                    <span class="h2">{{ total_projetos_concluidos }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white shadow-sm">
                <div class="card-body">
                    <h6>Atrasados</h6>
                    <span class="h2">{{ total_projetos_atrasados }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Sessão: KPIs de Tarefas -->
    <h5 class="mb-3">Indicadores de Tarefas</h5>
    <div class="row text-center mb-4">
        <div class="col-md-2">
            <div class="card bg-dark text-white shadow-sm">
                <div class="card-body">
                    <h6>Total Tarefas</h6>
                    <span class="h2">{{ total_tarefas }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-primary text-white shadow-sm">
                <div class="card-body">
                    <h6>Concluídas</h6>
                    <span class="h2">{{ total_tarefas_concluidas }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white shadow-sm">
                <div class="card-body">
                    <h6>Em Andamento</h6>
                    <span class="h2">{{ tarefas_em_andamento }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white shadow-sm">
                <div class="card-body">
                    <h6>Atrasadas</h6>
                    <span class="h2">{{ total_tarefas_atrasadas }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-light text-dark shadow-sm">
                <div class="card-body">
                    <h6>Tempo Médio Conclusão (dias)</h6>
                    <span class="h2">{{ tempo_medio_conclusao }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-light text-dark shadow-sm">
                <div class="card-body">
                    <h6>Tempo Médio Em Andamento (dias)</h6>
                    <span class="h2">{{ tempo_medio_em_andamento }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Sessão: Ranking de Usuários -->
    <h5 class="mb-3">Ranking de Usuários (Tarefas Concluídas)</h5>
    <div class="row">
        <div class="col-md-6">
            <ul class="list-group mb-4">
                {% for r in ranking_usuarios %}
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    {{ r.user_name }}
                    <span class="badge bg-primary rounded-pill">{{ r.tarefas_concluidas }}</span>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Gráficos Principais -->
    <div class="row">
        <!-- Status dos Projetos -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header">Status dos Projetos</div>
                <div class="card-body">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Tarefas por Prioridade -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header">Tarefas por Prioridade</div>
                <div class="card-body">
                    <canvas id="prioridadeChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráfico de Tendência (Projetos e Tarefas Concluídas Mensal) -->
    <h5 class="mb-3">Tendência Mensal</h5>
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <canvas id="tendenciaChart"></canvas>
        </div>
    </div>

</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- 
    AQUI isolamos os dados Jinja em <script type="application/json">, 
    para evitar erro de sintaxe no VSCode. 
    Depois faremos JSON.parse(...).
-->
<script type="application/json" id="labelsStatusJson">
    {{ labels_status|tojson|safe }}
</script>
<script type="application/json" id="dataStatusJson">
    {{ data_status|tojson|safe }}
</script>

<script type="application/json" id="labelsPrioridadesJson">
    {{ labels_prioridades|tojson|safe }}
</script>
<script type="application/json" id="dataPrioridadesJson">
    {{ data_prioridades|tojson|safe }}
</script>

<script type="application/json" id="mesesJson">
    {{ meses|tojson|safe }}
</script>
<script type="application/json" id="projConclJson">
    {{ projetos_concluidos_mensal|tojson|safe }}
</script>
<script type="application/json" id="tarefasConclJson">
    {{ tarefas_concluidas_mensal|tojson|safe }}
</script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // Ler dados do DOM
    const labelsStatus = JSON.parse(document.getElementById('labelsStatusJson').textContent);
    const dataStatus = JSON.parse(document.getElementById('dataStatusJson').textContent);

    const labelsPrioridades = JSON.parse(document.getElementById('labelsPrioridadesJson').textContent);
    const dataPrioridades = JSON.parse(document.getElementById('dataPrioridadesJson').textContent);

    const meses = JSON.parse(document.getElementById('mesesJson').textContent);
    const projConcl = JSON.parse(document.getElementById('projConclJson').textContent);
    const tarefasConcl = JSON.parse(document.getElementById('tarefasConclJson').textContent);

    // 1) Status dos Projetos
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: labelsStatus,
            datasets: [{
                data: dataStatus,
                backgroundColor: ['#3498db', '#f1c40f', '#2ecc71', '#e74c3c']
            }]
        }
    });

    // 2) Tarefas por Prioridade
    const prioridadeCtx = document.getElementById('prioridadeChart').getContext('2d');
    new Chart(prioridadeCtx, {
        type: 'bar',
        data: {
            labels: labelsPrioridades,
            datasets: [{
                label: "Qtd Tarefas",
                data: dataPrioridades,
                backgroundColor: ['#e67e22', '#2980b9', '#27ae60', '#8e44ad']
            }]
        }
    });

    // 3) Tendência Mensal (linhas para projetos e tarefas concluídas)
    const tendenciaCtx = document.getElementById('tendenciaChart').getContext('2d');
    new Chart(tendenciaCtx, {
        type: 'line',
        data: {
            labels: meses,
            datasets: [
                {
                    label: 'Projetos Concluídos',
                    data: projConcl,
                    borderColor: '#2ecc71',
                    fill: false,
                    tension: 0.1
                },
                {
                    label: 'Tarefas Concluídas',
                    data: tarefasConcl,
                    borderColor: '#3498db',
                    fill: false,
                    tension: 0.1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});
</script>
{% endblock %}
